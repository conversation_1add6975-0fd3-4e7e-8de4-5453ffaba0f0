"""
命令行测试程序 - 验证核心功能
"""

import sys
import os
from pathlib import Path
import time
import json

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_chrome_manager():
    """测试Chrome管理器"""
    print("\n" + "="*50)
    print("测试Chrome管理器")
    print("="*50)
    
    try:
        from core.chrome_manager import ChromeManager
        
        manager = ChromeManager()
        
        # 测试查找Chrome路径
        chrome_path = manager.find_chrome_path()
        print(f"OK Chrome路径: {chrome_path}")
        
        # 测试检查端口占用
        port_in_use = manager.is_port_in_use(9222)
        print(f"OK 端口9222占用状态: {port_in_use}")
        
        # 测试终止进程
        print("OK 正在清理现有Chrome进程...")
        manager.kill_chrome_processes()
        time.sleep(2)
        
        print("OK Chrome管理器测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR Chrome管理器测试失败: {e}")
        return False

def test_data_processor():
    """测试数据处理器"""
    print("\n" + "="*50)
    print("测试数据处理器")
    print("="*50)
    
    try:
        from core.data_processor import DataProcessor
        
        processor = DataProcessor()
        
        # 测试数据格式化
        test_data = {
            "name": "测试商品",
            "price": "99.99",
            "quantity": "5个",
            "supplier": "测试供应商"
        }
        
        processed = processor.process_item(test_data)
        print("OK 数据处理测试:")
        print(f"  原始数据: {test_data}")
        print(f"  处理后: {processed}")
        
        print("OK 数据处理器测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR 数据处理器测试失败: {e}")
        return False

def test_error_handler():
    """测试错误处理器"""
    print("\n" + "="*50)
    print("测试错误处理器")
    print("="*50)
    
    try:
        from core.error_handler import ErrorHandler
        
        handler = ErrorHandler()
        
        # 测试错误处理
        error_message = handler.handle_error("FILE_NOT_FOUND", "测试文件")
        print("OK 错误处理测试:")
        print(f"  错误信息: {error_message}")
        
        # 测试错误代码映射
        test_exception = FileNotFoundError("测试文件不存在")
        error_code = handler.get_error_code(test_exception)
        print(f"  错误代码: {error_code}")
        
        print("OK 错误处理器测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR 错误处理器测试失败: {e}")
        return False

def test_file_utils():
    """测试文件工具"""
    print("\n" + "="*50)
    print("测试文件工具")
    print("="*50)
    
    try:
        from utils.file_utils import FileUtils
        
        # 测试文件信息获取
        test_file = __file__
        file_info = FileUtils.get_file_info(test_file)
        print("OK 文件信息测试:")
        print(f"  文件名: {file_info['name']}")
        print(f"  文件大小: {file_info['size']} 字节")
        print(f"  文件存在: {file_info['exists']}")
        
        # 测试Excel验证（创建一个测试文件）
        test_excel_data = [
            {"商品名称": "测试商品1", "价格": 100, "数量": 5},
            {"商品名称": "测试商品2", "价格": 200, "数量": 3}
        ]
        
        import pandas as pd
        test_excel_path = Path(__file__).parent / "test_output.xlsx"
        df = pd.DataFrame(test_excel_data)
        df.to_excel(test_excel_path, index=False)
        
        # 验证Excel文件
        is_valid, message = FileUtils.validate_excel_file(str(test_excel_path))
        print(f"OK Excel验证测试: {is_valid}, {message}")
        
        # 清理测试文件
        if test_excel_path.exists():
            test_excel_path.unlink()
        
        print("OK 文件工具测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR 文件工具测试失败: {e}")
        return False

def test_config():
    """测试配置管理"""
    print("\n" + "="*50)
    print("测试配置管理")
    print("="*50)
    
    try:
        from core.config import Config
        
        config = Config()
        
        print("OK 配置加载测试:")
        print(f"  应用名称: {config.get('app_name')}")
        print(f"  Chrome端口: {config.chrome_port}")
        print(f"  窗口大小: {config.window_width}x{config.window_height}")
        print(f"  超时时间: {config.chrome_timeout}")
        print(f"  最大工作线程: {config.max_workers}")
        
        print("OK 配置管理测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR 配置管理测试失败: {e}")
        return False

def test_helpers():
    """测试辅助函数"""
    print("\n" + "="*50)
    print("测试辅助函数")
    print("="*50)
    
    try:
        from utils.helpers import (
            format_duration, format_file_size, calculate_progress,
            sanitize_filename, get_timestamp
        )
        
        # 测试时间格式化
        duration = format_duration(3665)
        print(f"OK 时间格式化: 3665秒 -> {duration}")
        
        # 测试文件大小格式化
        size = format_file_size(1024 * 1024 * 2.5)
        print(f"OK 文件大小格式化: 2621440字节 -> {size}")
        
        # 测试进度计算
        progress = calculate_progress(75, 100)
        print(f"OK 进度计算: 75/100 -> {progress}%")
        
        # 测试文件名安全化
        safe_name = sanitize_filename("测试文件/名称.xlsx")
        print(f"OK 文件名安全化: 测试文件/名称.xlsx -> {safe_name}")
        
        # 测试时间戳生成
        timestamp = get_timestamp()
        print(f"OK 时间戳生成: {timestamp}")
        
        print("OK 辅助函数测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR 辅助函数测试失败: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    print("\n" + "="*50)
    print("创建测试数据")
    print("="*50)
    
    try:
        # 创建测试采购车数据
        cart_data = {
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "source": "1688_cart",
            "total_products": 3,
            "products": [
                {
                    "index": 1,
                    "name": "测试商品1",
                    "price": "99.99",
                    "quantity": "5",
                    "imageUrl": "https://example.com/image1.jpg",
                    "productUrl": "https://detail.1688.com/offer/123.html",
                    "supplier": "测试供应商1"
                },
                {
                    "index": 2,
                    "name": "测试商品2",
                    "price": "199.99",
                    "quantity": "3",
                    "imageUrl": "https://example.com/image2.jpg",
                    "productUrl": "https://detail.1688.com/offer/456.html",
                    "supplier": "测试供应商2"
                },
                {
                    "index": 3,
                    "name": "测试商品3",
                    "price": "299.99",
                    "quantity": "2",
                    "imageUrl": "https://example.com/image3.jpg",
                    "productUrl": "https://detail.1688.com/offer/789.html",
                    "supplier": "测试供应商3"
                }
            ]
        }
        
        # 保存测试数据
        data_dir = Path(__file__).parent / "data"
        data_dir.mkdir(exist_ok=True)
        
        test_file = data_dir / "test_cart_data.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(cart_data, f, indent=2, ensure_ascii=False)
        
        print(f"OK 测试数据已创建: {test_file}")
        print(f"OK 包含{len(cart_data['products'])}个商品")
        
        return True
        
    except Exception as e:
        print(f"ERROR 创建测试数据失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("1688采购车数据处理工具 - 命令行测试")
    print("="*60)
    
    tests = [
        test_config,
        test_error_handler,
        test_file_utils,
        test_helpers,
        test_data_processor,
        test_chrome_manager,
        create_test_data
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("SUCCESS 所有测试通过！核心功能正常工作。")
        print("\n测试总结:")
        print("  OK 配置管理系统正常")
        print("  OK 错误处理系统正常")
        print("  OK 文件操作工具正常")
        print("  OK 辅助函数正常")
        print("  OK 数据处理功能正常")
        print("  OK Chrome管理功能正常")
        print("  OK 测试数据创建成功")
        print("\n提示:")
        print("  - 核心功能已经可以正常使用")
        print("  - GUI功能需要安装tkinter模块")
        print("  - 在Windows环境中，GUI功能应该可以正常工作")
    else:
        print("WARNING 部分测试失败，请检查相关模块。")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)