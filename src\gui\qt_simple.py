#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688采购车数据处理工具 - PyQt6极简版
基于PyQt6的轻量级界面，专注于核心功能
"""

import sys
import os
import json
import threading
import time
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QTextEdit, QRadioButton,
    QButtonGroup, QFileDialog, QMessageBox, QGroupBox, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class WorkerThread(QThread):
    """工作线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    log_added = pyqtSignal(str, str)  # message, type
    finished = pyqtSignal()
    
    def __init__(self, function_type, file_path=None):
        super().__init__()
        self.function_type = function_type
        self.file_path = file_path
        self._stop_flag = False
        
    def stop(self):
        self._stop_flag = True
        
    def run(self):
        try:
            if self.function_type == 'cart':
                self.simulate_cart_processing()
            else:
                self.simulate_order_processing()
        except Exception as e:
            self.log_added.emit(f"处理失败：{str(e)}", "error")
        finally:
            self.finished.emit()
    
    def simulate_cart_processing(self):
        """模拟采购车数据处理"""
        self.log_added.emit("开始采购车数据处理演示...", "info")
        
        steps = [
            "启动Chrome调试模式",
            "连接到1688采购车页面", 
            "提取商品数据",
            "下载商品图片",
            "处理和标准化数据",
            "生成Excel报告",
            "保存结果文件"
        ]
        
        for i, step in enumerate(steps):
            if self._stop_flag:
                break
                
            self.log_added.emit(f"步骤 {i+1}/{len(steps)}：{step}", "info")
            
            # 模拟处理时间
            for j in range(101):
                if self._stop_flag:
                    break
                    
                progress = (i * 100 + j) // len(steps)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"正在处理：{step}")
                
                self.msleep(30)
        
        self.log_added.emit("采购车数据处理完成！", "success")
        self.status_updated.emit("处理完成")
    
    def simulate_order_processing(self):
        """模拟订单数据处理"""
        self.log_added.emit("开始订单数据处理演示...", "info")
        
        if not self.file_path:
            self.log_added.emit("未选择文件，处理取消", "warning")
            return
            
        self.log_added.emit(f"已选择文件：{os.path.basename(self.file_path)}", "info")
        
        steps = [
            "读取Excel文件",
            "分析数据结构",
            "识别商品信息字段",
            "搜索商品图片和链接",
            "增强订单数据",
            "生成增强后的Excel文件",
            "保存结果"
        ]
        
        for i, step in enumerate(steps):
            if self._stop_flag:
                break
                
            self.log_added.emit(f"步骤 {i+1}/{len(steps)}：{step}", "info")
            
            # 模拟处理时间
            for j in range(101):
                if self._stop_flag:
                    break
                    
                progress = (i * 100 + j) // len(steps)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"正在处理：{step}")
                
                self.msleep(40)
        
        self.log_added.emit("订单数据处理完成！", "success")
        self.status_updated.emit("处理完成")


class SimpleQtApp(QMainWindow):
    """极简版PyQt6应用"""
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("1688采购车数据处理工具")
        self.setFixedSize(600, 500)
        self.setWindowFlags(Qt.WindowType.WindowCloseButtonHint)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("1688采购车数据处理工具")
        title_font = QFont("微软雅黑", 14, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 功能选择
        self.create_function_selection(layout)
        
        # 进度条
        self.create_progress_area(layout)
        
        # 日志区域
        self.create_log_area(layout)
        
        # 按钮区域
        self.create_button_area(layout)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
            }
            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: #ffffff;
                font-family: Consolas;
                font-size: 9px;
            }
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def create_function_selection(self, layout):
        """创建功能选择区域"""
        group = QGroupBox("选择功能")
        group_layout = QVBoxLayout()
        
        self.function_group = QButtonGroup(self)
        
        # 采购车数据提取
        cart_radio = QRadioButton("🛒 采购车数据提取")
        cart_radio.setChecked(True)
        self.function_group.addButton(cart_radio, 'cart')
        group_layout.addWidget(cart_radio)
        
        # 订单数据增强
        order_radio = QRadioButton("📋 订单数据增强")
        self.function_group.addButton(order_radio, 'order')
        group_layout.addWidget(order_radio)
        
        group.setLayout(group_layout)
        layout.addWidget(group)
    
    def create_progress_area(self, layout):
        """创建进度显示区域"""
        group = QGroupBox("处理进度")
        group_layout = QVBoxLayout()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        group_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        group_layout.addWidget(self.status_label)
        
        group.setLayout(group_layout)
        layout.addWidget(group)
    
    def create_log_area(self, layout):
        """创建日志显示区域"""
        group = QGroupBox("操作日志")
        group_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        group_layout.addWidget(self.log_text)
        
        group.setLayout(group_layout)
        layout.addWidget(group)
    
    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 开始按钮
        self.start_button = QPushButton("开始处理")
        self.start_button.clicked.connect(self.start_processing)
        button_layout.addWidget(self.start_button)
        
        # 停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        # 清空日志按钮
        self.clear_button = QPushButton("清空日志")
        self.clear_button.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_button)
        
        # 退出按钮
        self.exit_button = QPushButton("退出")
        self.exit_button.clicked.connect(self.close)
        button_layout.addWidget(self.exit_button)
        
        layout.addLayout(button_layout)
    
    def start_processing(self):
        """开始处理"""
        function_type = self.function_group.checkedId()
        
        # 如果是订单处理，选择文件
        if function_type == 'order':
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Excel订单文件",
                "",
                "Excel文件 (*.xlsx *.xls);;所有文件 (*.*)"
            )
            if not file_path:
                self.add_log("未选择文件，处理取消", "warning")
                return
        else:
            file_path = None
        
        # 禁用开始按钮
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 清空进度
        self.progress_bar.setValue(0)
        
        # 创建工作线程
        self.worker = WorkerThread(function_type, file_path)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.log_added.connect(self.add_log)
        self.worker.finished.connect(self.on_worker_finished)
        
        self.worker.start()
    
    def stop_processing(self):
        """停止处理"""
        if self.worker:
            self.worker.stop()
        self.add_log("处理已停止", "warning")
        self.reset_buttons()
    
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def on_worker_finished(self):
        """工作线程完成"""
        self.reset_buttons()
        if self.worker:
            self.worker.deleteLater()
            self.worker = None
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空", "info")
    
    def add_log(self, message: str, log_type: str = "info"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        colored_message = f"[{timestamp}] {message}"
        
        # 根据类型设置颜色
        if log_type == "success":
            colored_message = f'<span style="color: #4CAF50;">{colored_message}</span>'
        elif log_type == "warning":
            colored_message = f'<span style="color: #FF9800;">{colored_message}</span>'
        elif log_type == "error":
            colored_message = f'<span style="color: #f44336;">{colored_message}</span>'
        
        self.log_text.append(colored_message)
        
        # 限制日志行数
        document = self.log_text.document()
        while document.blockCount() > 100:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            cursor.select(cursor.SelectionType.BlockUnderCursor)
            cursor.removeSelectedText()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self,
                "确认退出",
                "任务正在运行中，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.worker.stop()
                self.worker.wait()
            else:
                event.ignore()
                return
        
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("1688采购车数据处理工具")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = SimpleQtApp()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()