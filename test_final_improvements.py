#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终改进的商品SKU提取功能
"""

import asyncio
import sys
from pathlib import Path
import os

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.extract_orders import OrderDataExtractor

async def test_final_improvements():
    """测试最终改进的功能"""
    print("🧪 测试最终改进的商品SKU提取功能...")
    
    # 创建提取器实例
    extractor = OrderDataExtractor()
    
    # 模拟完整的测试数据
    test_data = [
        # 订单1的多个商品SKU
        {
            'order_id': '*****************',
            'title': '女装连衣裙',
            'product_specs': '红色, L码',
            'price': 89.90,
            'quantity': 1,
            'total_amount': 89.90,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'image_url': 'https://cbu01.alicdn.com/img/ibank/O1CN01example1.jpg',
            'detail_url': 'https://detail.1688.com/offer/example1.html'
        },
        {
            'order_id': '*****************',
            'title': '女装连衣裙',
            'product_specs': '蓝色, M码',
            'price': 89.90,
            'quantity': 2,
            'total_amount': 179.80,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'image_url': 'https://cbu01.alicdn.com/img/ibank/O1CN01example2.jpg',
            'detail_url': 'https://detail.1688.com/offer/example1.html'
        },
        {
            'order_id': '*****************',
            'title': '女装连衣裙',
            'product_specs': '黑色, S码',
            'price': 89.90,
            'quantity': 1,
            'total_amount': 89.90,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'image_url': 'https://cbu01.alicdn.com/img/ibank/O1CN01example3.jpg',
            'detail_url': 'https://detail.1688.com/offer/example1.html'
        },
        # 订单2的商品SKU
        {
            'order_id': '*****************',
            'title': '男士T恤',
            'product_specs': '白色, XL码',
            'price': 45.00,
            'quantity': 3,
            'total_amount': 135.00,
            'shop_name': '另一家供应商',
            'image_url': 'https://cbu01.alicdn.com/img/ibank/O1CN01example4.jpg',
            'detail_url': 'https://detail.1688.com/offer/example2.html'
        },
        {
            'order_id': '*****************',
            'title': '男士T恤',
            'product_specs': '灰色, L码',
            'price': 45.00,
            'quantity': 2,
            'total_amount': 90.00,
            'shop_name': '另一家供应商',
            'image_url': 'https://cbu01.alicdn.com/img/ibank/O1CN01example5.jpg',
            'detail_url': 'https://detail.1688.com/offer/example2.html'
        }
    ]
    
    print(f"📊 测试数据: {len(test_data)} 个商品SKU")
    
    # 测试保存到Excel（包含图片和超链接）
    output_path = PROJECT_ROOT / "reports" / "test_final_improvements.xlsx"
    
    print("💾 保存Excel文件（包含图片和超链接）...")
    await extractor.save_to_excel(test_data, str(output_path))
    
    print("✅ 测试完成!")
    print(f"📄 测试结果已保存到: {output_path}")
    
    # 检查生成的文件
    if output_path.exists():
        file_size = output_path.stat().st_size
        print(f"📏 文件大小: {file_size / 1024:.1f} KB")
        
        # 检查图片目录
        images_dir = output_path.parent / "images"
        if images_dir.exists():
            image_files = list(images_dir.glob("*.jpg"))
            print(f"📸 下载的图片: {len(image_files)} 张")
        else:
            print("📸 图片目录未创建（可能是因为网络问题）")
    
    # 验证数据结构
    print("\n🔍 数据结构验证:")
    print("=" * 60)
    
    # 按订单分组显示
    orders = {}
    for item in test_data:
        order_id = item['order_id']
        if order_id not in orders:
            orders[order_id] = []
        orders[order_id].append(item)
    
    for order_id, products in orders.items():
        print(f"\n📦 订单号: {order_id}")
        print(f"   卖家: {products[0]['shop_name']}")
        print(f"   商品SKU数量: {len(products)} 个")
        
        for i, product in enumerate(products):
            print(f"   商品 {i+1}:")
            print(f"     名称: {product['title']}")
            print(f"     规格: {product['product_specs']}")
            print(f"     单价: ￥{product['price']}")
            print(f"     数量: {product['quantity']}")
            print(f"     小计: ￥{product['total_amount']}")
            print(f"     图片: {product['image_url'][:50]}...")
            print(f"     详情: {product['detail_url'][:50]}...")
    
    # 统计信息
    print("\n📈 统计信息:")
    print("=" * 60)
    unique_orders = len(orders)
    unique_suppliers = len(set(item['shop_name'] for item in test_data))
    total_amount = sum(item['total_amount'] for item in test_data)
    total_quantity = sum(item['quantity'] for item in test_data)
    
    print(f"  商品SKU总数: {len(test_data)}")
    print(f"  订单数量: {unique_orders}")
    print(f"  供应商数量: {unique_suppliers}")
    print(f"  商品总数量: {total_quantity}")
    print(f"  总金额: ￥{total_amount:.2f}")
    
    print("\n💡 改进特点:")
    print("  ✅ 每个商品SKU都有独立记录")
    print("  ✅ 支持商品规格信息（颜色、尺寸等）")
    print("  ✅ 自动下载商品图片并插入Excel")
    print("  ✅ 图片URL作为'原图'超链接")
    print("  ✅ 详情页URL作为'详情页'超链接")
    print("  ✅ 简化了输出信息，提高可读性")
    print("  ✅ 默认启用多页抓取")
    print("  ✅ 自动展开隐藏的商品")

if __name__ == "__main__":
    asyncio.run(test_final_improvements())
