# -*- coding: utf-8 -*-
"""
1688自动化项目测试套件运行器
"""

import unittest
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_tests():
    """运行所有测试"""
    print("🚀 开始运行1688自动化项目测试套件...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试模块
    test_modules = [
        'test_config',
        'test_utils',
        'test_integration'
    ]
    
    for module_name in test_modules:
        try:
            module = __import__(module_name, fromlist=[''])
            tests = loader.loadTestsFromModule(module)
            suite.addTests(tests)
            print(f"✅ 已加载测试模块: {module_name}")
        except ImportError as e:
            print(f"❌ 无法加载测试模块 {module_name}: {e}")
    
    print("=" * 60)
    print(f"📊 总共加载了 {suite.countTestCases()} 个测试用例")
    print("=" * 60)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("=" * 60)
    print("📋 测试结果汇总:")
    print(f"✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 失败: {len(result.failures)}")
    print(f"🚨 错误: {len(result.errors)}")
    print(f"📊 总计: {result.testsRun}")
    
    if result.failures:
        print("\n💥 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    # 返回是否成功
    return len(result.failures) == 0 and len(result.errors) == 0

if __name__ == '__main__':
    success = run_tests()
    
    if success:
        print("\n🎉 所有测试通过！项目质量良好。")
        sys.exit(0)
    else:
        print("\n⚠️  存在测试失败，请检查代码。")
        sys.exit(1)