#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证订单数据抓取的准确性和完整性
"""

import json
import os
import sys
import pandas as pd
try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from extract_orders import OrderDataExtractor
except ImportError:
    print("ERROR: 无法导入 OrderDataExtractor，请确保 extract_orders.py 文件存在")
    OrderDataExtractor = None

# 设置控制台编码
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())


def validate_data_completeness():
    """验证数据完整性"""
    print("[VALIDATION] 验证数据完整性...")

    # 读取测试数据
    test_file = "test_output/mock_orders.json"
    if not os.path.exists(test_file):
        print("[ERROR] 测试数据文件不存在")
        return False

    with open(test_file, 'r', encoding='utf-8') as f:
        orders = json.load(f)

    print(f"📊 总共 {len(orders)} 个订单")

    # 定义必需字段
    required_fields = ['order_id', 'title', 'price', 'quantity',
                       'total_amount']
    optional_fields = ['shop_name', 'order_time', 'status', 'image_url',
                       'detail_url']

    completeness_stats = {
        'total_orders': len(orders),
        'complete_orders': 0,
        'partial_orders': 0,
        'missing_fields': {}
    }

    for field in required_fields + optional_fields:
        completeness_stats['missing_fields'][field] = 0

    for order in orders:
        has_required = all(order.get(field) for field in required_fields)

        # 检查缺失字段
        for field in required_fields + optional_fields:
            if not order.get(field):
                completeness_stats['missing_fields'][field] += 1

        if has_required:
            completeness_stats['complete_orders'] += 1
        else:
            completeness_stats['partial_orders'] += 1

    print(f"✅ 完整订单: {completeness_stats['complete_orders']}")
    print(f"⚠️ 部分完整订单: {completeness_stats['partial_orders']}")

    print("\n📋 缺失字段统计:")
    for field, count in completeness_stats['missing_fields'].items():
        if count > 0:
            percentage = (count / completeness_stats['total_orders']) * 100
            print(f"  {field}: {count} 个缺失 ({percentage:.1f}%)")

    # 计算完整性得分
    completeness_score = (
        completeness_stats['complete_orders'] /
        completeness_stats['total_orders']
    ) * 100
    print(f"\n📊 数据完整性得分: {completeness_score:.1f}%")

    return completeness_score >= 80  # 80%以上为合格


def validate_data_accuracy():
    """验证数据准确性"""
    print("\n🔍 验证数据准确性...")

    # 读取测试数据
    test_file = "test_output/mock_orders.json"
    with open(test_file, 'r', encoding='utf-8') as f:
        orders = json.load(f)

    accuracy_stats = {
        'total_orders': len(orders),
        'price_accuracy': 0,
        'total_accuracy': 0,
        'data_type_errors': 0
    }

    for order in orders:
        # 验证价格计算准确性
        price = order.get('price', 0)
        quantity = order.get('quantity', 0)
        total_amount = order.get('total_amount', 0)

        if (isinstance(price, (int, float)) and
                isinstance(quantity, (int, float))):
            calculated_total = price * quantity
            if abs(calculated_total - total_amount) < 0.01:  # 允许小的浮点误差
                accuracy_stats['price_accuracy'] += 1
                accuracy_stats['total_accuracy'] += 1

        # 验证数据类型
        for field, value in order.items():
            if (field in ['price', 'quantity', 'total_amount'] and
                    not isinstance(value, (int, float))):
                accuracy_stats['data_type_errors'] += 1

    print(
        f"📊 价格计算准确: {accuracy_stats['price_accuracy']}/"
        f"{accuracy_stats['total_orders']}"
    )
    print(f"📊 数据类型错误: {accuracy_stats['data_type_errors']}")

    # 计算准确性得分
    accuracy_score = (
        accuracy_stats['price_accuracy'] / accuracy_stats['total_orders']
    ) * 100
    print(f"📊 数据准确性得分: {accuracy_score:.1f}%")

    return accuracy_score >= 90  # 90%以上为合格


def validate_field_mapping():
    """验证字段映射"""
    print("\n🔍 验证字段映射...")

    if OrderDataExtractor is None:
        print("❌ OrderDataExtractor 不可用")
        return False

    extractor = OrderDataExtractor()

    # 测试各种字段映射
    test_mappings = [
        {
            'input': {'orderId': '123', 'title': '测试商品', 'price': 10.0},
            'expected': {'order_id': '123', 'title': '测试商品', 'price': 10.0}
        },
        {
            'input': {'id': '456', 'subject': '另一个商品', 'amount': 20.0},
            'expected': {'order_id': '456', 'title': '另一个商品', 'price': 20.0}
        },
        {
            'input': {'orderNo': '789', 'productName': '产品名称',
                      'unitPrice': 30.0},
            'expected': {'order_id': '789', 'title': '产品名称', 'price': 30.0}
        }
    ]

    mapping_success = 0

    for i, test_case in enumerate(test_mappings):
        print(f"\n📋 测试映射 {i+1}:")
        result = extractor._extract_order_fields(test_case['input'])

        if result:
            success = True
            for key, expected_value in test_case['expected'].items():
                if result.get(key) != expected_value:
                    success = False
                    print(f"  ❌ {key}: 期望 {expected_value}, "
                          f"实际 {result.get(key)}")

            if success:
                print("  ✅ 映射成功")
                mapping_success += 1
        else:
            print("  ❌ 映射失败")

    mapping_score = (mapping_success / len(test_mappings)) * 100
    print(f"\n📊 字段映射得分: {mapping_score:.1f}%")

    return mapping_score >= 90


def validate_excel_output():
    """验证Excel输出"""
    print("\n🔍 验证Excel输出...")

    excel_file = "test_output/test_orders.xlsx"
    if not os.path.exists(excel_file):
        print("❌ Excel文件不存在")
        return False

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)

        print(f"📊 Excel包含 {len(df)} 行数据")
        print(f"📋 列名: {list(df.columns)}")

        # 检查必需列
        required_columns = ['订单号', '商品名称', '单价', '数量', '总金额']
        missing_columns = [col for col in required_columns
                           if col not in df.columns]

        if missing_columns:
            print(f"❌ 缺少必需列: {missing_columns}")
            return False

        # 检查数据完整性
        empty_cells = 0
        total_cells = 0

        for column in required_columns:
            empty_count = df[column].isna().sum()
            empty_cells += empty_count
            total_cells += len(df)
            print(f"  {column}: {empty_count} 个空值")

        if empty_cells > 0:
            empty_percentage = (empty_cells / total_cells) * 100
            print(f"⚠️ 空值率: {empty_percentage:.1f}%")

        print("✅ Excel输出验证通过")
        return True

    except Exception as e:
        print(f"❌ Excel读取失败: {e}")
        return False


def validate_error_handling():
    """验证错误处理"""
    print("\n🔍 验证错误处理...")

    if OrderDataExtractor is None:
        print("❌ OrderDataExtractor 不可用")
        return False

    extractor = OrderDataExtractor()

    # 测试空数据处理
    test_cases = [
        ({}, "空字典"),
        (None, "None值"),
        ([], "空列表"),
        ({"invalid": "data"}, "无效数据"),
        ({"title": "只有标题"}, "部分数据")
    ]

    error_handling_success = 0

    for test_data, description in test_cases:
        try:
            if test_data is None:
                result = None
            else:
                result = extractor._extract_order_fields(test_data)

            # 检查是否正确处理了错误情况
            if test_data == {} or test_data is None:
                if result is None:
                    error_handling_success += 1
                    print(f"  ✅ {description}: 正确处理")
                else:
                    print(f"  ❌ {description}: 应该返回None")
            elif test_data == {"title": "只有标题"}:
                if result and result.get('title') == "只有标题":
                    error_handling_success += 1
                    print(f"  ✅ {description}: 正确处理")
                else:
                    print(f"  ❌ {description}: 处理不当")
            else:
                print(f"  ⚠️ {description}: 需要人工检查")

        except Exception as e:
            print(f"  ❌ {description}: 异常 {e}")

    error_score = (error_handling_success / len(test_cases)) * 100
    print(f"\n📊 错误处理得分: {error_score:.1f}%")

    return error_score >= 80


def main():
    """主验证函数"""
    print("🚀 开始验证订单数据抓取的准确性和完整性...")

    validation_results = []

    # 执行各项验证
    validation_results.append(("数据完整性", validate_data_completeness()))
    validation_results.append(("数据准确性", validate_data_accuracy()))
    validation_results.append(("字段映射", validate_field_mapping()))
    validation_results.append(("Excel输出", validate_excel_output()))
    validation_results.append(("错误处理", validate_error_handling()))

    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    print("=" * 50)

    passed_count = 0
    for test_name, passed in validation_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if passed:
            passed_count += 1

    total_score = (passed_count / len(validation_results)) * 100
    print(
        f"\n🎯 总体得分: {total_score:.1f}% "
        f"({passed_count}/{len(validation_results)})"
    )

    if total_score >= 80:
        print("🎉 验证通过！订单数据抓取工具的质量符合要求。")
        print("\n💡 建议的改进方向：")
        print("   1. 增加更多的API数据结构支持")
        print("   2. 优化错误处理机制")
        print("   3. 增加数据验证和清洗功能")
        print("   4. 添加更多的日志记录")
    else:
        print("⚠️ 验证未完全通过，建议改进相关问题后再进行测试。")

    return total_score >= 80


if __name__ == "__main__":
    main()
