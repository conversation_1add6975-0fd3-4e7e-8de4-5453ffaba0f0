#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON文件点击修复效果
"""

import sys
import os
import time
import json
import subprocess
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_subprocess_file_open():
    """测试subprocess方式打开文件"""
    print("🔧 测试subprocess方式打开文件...")
    
    # 创建测试JSON文件
    data_dir = PROJECT_ROOT / "data"
    data_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    test_file = data_dir / f"test_subprocess_{timestamp}.json"
    
    test_data = {
        "测试": "subprocess方式打开文件",
        "时间": timestamp
    }
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试文件: {test_file}")
    
    # 测试不同的打开方式
    print("\n📋 测试不同的文件打开方式:")
    
    # 方式1: os.startfile (原来的方式)
    print("1. os.startfile方式:")
    try:
        print(f"   正在打开文件...")
        # 不实际执行，只是说明
        print(f"   命令: os.startfile('{test_file}')")
        print(f"   特点: 可能会有输出重定向问题")
    except Exception as e:
        print(f"   失败: {e}")
    
    # 方式2: subprocess with DEVNULL (新的方式)
    print("\n2. subprocess + DEVNULL方式:")
    try:
        print(f"   正在打开文件...")
        result = subprocess.run(
            ["cmd", "/c", "start", "", str(test_file)], 
            shell=False, 
            stdout=subprocess.DEVNULL, 
            stderr=subprocess.DEVNULL,
            timeout=5
        )
        print(f"   命令执行成功，返回码: {result.returncode}")
        print(f"   特点: 输出被重定向到DEVNULL，不会影响程序")
    except Exception as e:
        print(f"   失败: {e}")
    
    # 清理测试文件
    time.sleep(2)  # 等待文件打开
    try:
        test_file.unlink()
        print(f"\n🧹 清理测试文件: {test_file}")
    except:
        print(f"\n⚠️ 无法删除测试文件（可能正在使用中）: {test_file}")

def main():
    """主函数"""
    print("JSON文件点击修复测试")
    print("=" * 30)
    
    print("🎯 修复说明:")
    print("1. 将os.startfile改为subprocess.run")
    print("2. 添加stdout=DEVNULL和stderr=DEVNULL")
    print("3. 防止外部程序的输出显示在程序中")
    
    # 测试subprocess方式
    test_subprocess_file_open()
    
    print(f"\n💡 修复效果:")
    print("- JSON文件仍然会用默认程序打开")
    print("- 但不会有任何输出显示在程序日志中")
    print("- 程序界面保持干净整洁")

if __name__ == "__main__":
    main()
