#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome调试接口诊断工具
深度检查Chrome调试协议的可用性
"""

import asyncio
import sys
import time
import json
import requests
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

async def diagnose_chrome_debug_interface():
    """诊断Chrome调试接口状态"""
    print("[诊断] Chrome调试接口深度诊断")
    print("=" * 60)
    
    port = 9222
    base_url = f"http://localhost:{port}"
    
    try:
        # 1. 测试基本连接
        print(f"[步骤1] 测试基本HTTP连接...")
        version_url = f"{base_url}/json/version"
        
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        
        if response.status_code == 200:
            version_info = response.json()
            print(f"[成功] Chrome调试服务HTTP接口正常")
            print(f"[信息] 浏览器版本: {version_info.get('Browser', 'Unknown')}")
            print(f"[信息] 协议版本: {version_info.get('Protocol-Version', 'Unknown')}")
        else:
            print(f"[失败] HTTP接口响应异常: {response.status_code}")
            return False
            
        # 2. 测试WebSocket端点
        print(f"\n[步骤2] 测试WebSocket端点...")
        ws_url = version_info.get('webSocketDebuggerUrl')
        if ws_url:
            print(f"[信息] WebSocket URL: {ws_url}")
            
            # 尝试使用playwright连接WebSocket
            try:
                from playwright.async_api import async_playwright
                
                async with async_playwright() as playwright:
                    print(f"[测试] 尝试Playwright WebSocket连接...")
                    browser = await playwright.chromium.connect_over_cdp(ws_url, timeout=10000)
                    print(f"[成功] Playwright WebSocket连接成功")
                    
                    # 检查浏览器状态
                    if browser.contexts:
                        print(f"[信息] 找到 {len(browser.contexts)} 个上下文")
                        for i, context in enumerate(browser.contexts):
                            print(f"   上下文 {i+1}: {len(context.pages)} 个页面")
                            for j, page in enumerate(context.pages):
                                print(f"      页面 {j+1}: {page.url}")
                    else:
                        print(f"[警告] 没有找到浏览器上下文")
                    
                    await browser.close()
                    return True
                    
            except Exception as e:
                error_msg = str(e)
                print(f"[失败] Playwright WebSocket连接失败: {error_msg}")
                
                # 分析具体错误
                if "503" in error_msg:
                    print(f"[分析] 503错误 - Chrome调试接口可能正在初始化")
                    print(f"[建议] 等待更长时间让Chrome完全启动")
                elif "econnrefused" in error_msg.lower():
                    print(f"[分析] 连接被拒绝 - WebSocket服务未启动")
                elif "timeout" in error_msg.lower():
                    print(f"[分析] 连接超时 - Chrome响应过慢")
                
                return False
        else:
            print(f"[错误] 无法获取WebSocket URL")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"[错误] 无法连接到Chrome调试服务")
        print(f"[建议] 请确保Chrome正在运行并启用了调试模式")
        return False
    except requests.exceptions.Timeout:
        print(f"[错误] 连接超时")
        print(f"[建议] Chrome可能正在启动中，请稍后重试")
        return False
    except Exception as e:
        print(f"[错误] 诊断失败: {e}")
        return False

async def test_direct_websocket():
    """测试直接WebSocket连接"""
    print(f"\n[步骤3] 测试直接WebSocket连接...")
    
    try:
        import websockets
        import json
        
        uri = "ws://localhost:9222/devtools/page/abcdef"  # 使用一个测试URI
        
        try:
            async with websockets.connect(uri, timeout=5) as websocket:
                print(f"[成功] WebSocket连接建立成功")
                return True
        except websockets.exceptions.ConnectionClosed:
            print(f"[失败] WebSocket连接被关闭")
            return False
        except websockets.exceptions.InvalidURI:
            print(f"[信息] WebSocket URI无效，这是正常的")
            return True  # 这是预期的，因为我们使用了假的URI
        except Exception as e:
            print(f"[失败] WebSocket连接失败: {e}")
            return False
            
    except ImportError:
        print(f"[跳过] websockets库未安装")
        return True

async def check_chrome_process():
    """检查Chrome进程状态"""
    print(f"\n[步骤4] 检查Chrome进程状态...")
    
    try:
        import subprocess
        
        # Windows系统检查Chrome进程
        if sys.platform == "win32":
            # 检查是否有Chrome进程运行
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq chrome.exe', '/FO', 'CSV'],
                capture_output=True, text=True, encoding='gbk'
            )
            
            if result.returncode == 0 and 'chrome.exe' in result.stdout:
                lines = result.stdout.strip().split('\n')
                chrome_count = len([line for line in lines if 'chrome.exe' in line])
                print(f"[信息] 找到 {chrome_count} 个Chrome进程")
                
                # 检查是否有调试参数的Chrome进程
                result = subprocess.run(
                    ['wmic', 'process', 'where', 'name="chrome.exe"', 'get', 'commandline', '/format:list'],
                    capture_output=True, text=True, encoding='gbk'
                )
                
                if result.returncode == 0:
                    debug_processes = []
                    for line in result.stdout.split('\n'):
                        if 'remote-debugging-port=9222' in line:
                            debug_processes.append(line.strip())
                    
                    if debug_processes:
                        print(f"[成功] 找到 {len(debug_processes)} 个启用调试的Chrome进程")
                        for i, proc in enumerate(debug_processes[:3]):  # 只显示前3个
                            print(f"   进程 {i+1}: {proc[:100]}...")
                    else:
                        print(f"[警告] 没有找到启用调试端口的Chrome进程")
                        print(f"[建议] 请使用 start_debug_chrome.bat 启动Chrome")
            else:
                print(f"[警告] 没有找到Chrome进程")
        else:
            print(f"[跳过] 非Windows系统，跳过进程检查")
            
    except Exception as e:
        print(f"[错误] 进程检查失败: {e}")

async def main():
    """主诊断函数"""
    print("[开始] Chrome调试接口诊断工具")
    print("=" * 70)
    
    # 执行诊断步骤
    await check_chrome_process()
    http_ok = await diagnose_chrome_debug_interface()
    await test_direct_websocket()
    
    print(f"\n[总结] 诊断结果:")
    if http_ok:
        print(f"[成功] Chrome调试接口HTTP部分正常工作")
        print(f"[建议] 503错误可能是WebSocket接口初始化延迟造成的")
        print(f"[建议] 尝试以下解决方案:")
        print(f"   1. 等待30秒让Chrome完全初始化")
        print(f"   2. 重新启动Chrome调试模式")
        print(f"   3. 检查是否有防火墙阻止WebSocket连接")
    else:
        print(f"[失败] Chrome调试接口存在问题")
        print(f"[建议] 请按照以下步骤操作:")
        print(f"   1. 关闭所有Chrome进程")
        print(f"   2. 运行 start_debug_chrome.bat")
        print(f"   3. 等待30秒让Chrome完全启动")
        print(f"   4. 重新运行诊断工具")

if __name__ == "__main__":
    asyncio.run(main())