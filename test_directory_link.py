#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试目录链接问题
"""

import sys
import os
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_directory_path_extraction():
    """测试目录路径提取"""
    print("📁 测试目录路径提取...")
    
    test_files = [
        "D:\\1688_automation_project\\reports\\cart_report_20250812_170248.xlsx",
        "D:\\1688_automation_project\\data\\cart_data_20250812_170248.json",
        "reports\\test_file.xlsx",
        "data\\test_file.json"
    ]
    
    for file_path in test_files:
        print(f"\n文件路径: {file_path}")
        
        # 使用os.path.dirname
        dir_path = os.path.dirname(file_path)
        print(f"os.path.dirname: {dir_path}")
        
        # 使用pathlib
        path_obj = Path(file_path)
        parent_path = str(path_obj.parent)
        print(f"pathlib.parent: {parent_path}")
        
        # 检查是否为空或当前目录
        if not dir_path or dir_path == '.':
            print("⚠️ 目录路径为空或当前目录")
        else:
            print(f"✅ 目录路径正常")

def test_hyperlink_generation():
    """测试超链接生成"""
    print("\n🔗 测试超链接生成...")
    
    # 导入GUI模块
    from src.gui.qt_real import RealFunctionApp
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = RealFunctionApp()
    
    # 测试不同类型的文件路径
    test_messages = [
        "Excel报告已生成: D:\\1688_automation_project\\reports\\cart_report_20250812_170248.xlsx",
        "数据已保存到: D:\\1688_automation_project\\data\\cart_data_20250812_170248.json",
        "Excel报告已生成: reports\\test_file.xlsx",
        "数据已保存到: data\\test_file.json"
    ]
    
    for msg in test_messages:
        print(f"\n原始消息: {msg}")
        enhanced = window.enhance_message_with_links(msg)
        print(f"增强后: {enhanced}")
        
        # 提取目录链接
        import re
        dir_links = re.findall(r'<a href="file:///([^"]+)">📁</a>', enhanced)
        for link in dir_links:
            print(f"目录链接: {link}")

def main():
    """主函数"""
    print("目录链接问题测试")
    print("=" * 30)
    
    # 1. 测试目录路径提取
    test_directory_path_extraction()
    
    # 2. 测试超链接生成
    test_hyperlink_generation()
    
    print(f"\n🎯 可能的问题:")
    print("1. 相对路径的目录提取结果为空")
    print("2. 空目录路径被转换为当前工作目录")
    print("3. 需要将相对路径转换为绝对路径")

if __name__ == "__main__":
    main()
