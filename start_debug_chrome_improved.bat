@echo off
echo Starting Chrome Debug Mode for 1688 Automation...
echo.

echo Checking for processes using port 9222...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :9222 ^| findstr LISTENING') do (
    echo Found process %%a using port 9222, terminating...
    taskkill /F /PID %%a 2>nul
)
timeout /t 2 >nul 2>&1

echo Starting Chrome with debug port...
echo.
echo IMPORTANT: Please wait 20-30 seconds for Chrome to fully initialize
echo.
echo Steps to follow:
echo 1. Wait for Chrome to completely start
echo 2. Login to your 1688 account
echo 3. Navigate to cart.1688.com
echo 4. Ensure page is fully loaded
echo 5. Return to the application and start processing
echo.

start "" chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug_1688" --no-first-run --no-default-browser-check --disable-extensions --disable-background-mode --disable-background-timer-throttling --disable-renderer-backgrounding

echo.
echo [OK] Chrome started with debug port: 9222
echo [INFO] User data directory: %TEMP%\chrome_debug_1688
echo [TIP] Wait for Chrome to fully initialize before proceeding
echo.
timeout /t 5 >nul 2>&1