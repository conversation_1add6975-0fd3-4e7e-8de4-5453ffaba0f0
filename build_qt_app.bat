@echo off
echo ========================================
echo 1688采购车数据处理工具 - 打包脚本
echo ========================================
echo.

REM 检查PyInstaller是否安装
python -c "import PyInstaller" 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到PyInstaller，正在安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo [错误] PyInstaller安装失败！
        pause
        exit /b 1
    )
    echo [成功] PyInstaller安装完成
    echo.
)

echo [信息] 开始打包应用程序...
echo.

REM 使用--onedir模式打包，比--onefile更节省内存
pyinstaller --noconfirm --clean ^
    --name "1688数据工具" ^
    --windowed ^
    --onedir ^
    --add-data "enrich_orders_with_images.py;." ^
    --add-data "enrich_orders_simple.py;." ^
    --add-data "start_debug_chrome.bat;." ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    --hidden-import=PIL ^
    --hidden-import=aiohttp ^
    --hidden-import=asyncio ^
    --hidden-import=difflib ^
    --hidden-import=hashlib ^
    --exclude-module=tkinter ^
    --exclude-module=matplotlib ^
    --exclude-module=numpy ^
    --exclude-module=scipy ^
    --exclude-module=pytest ^
    --exclude-module=unittest ^
    --exclude-module=docutils ^
    --exclude-module=sphinx ^
    --exclude-module=jinja2 ^
    --exclude-module=markupsafe ^
    --icon=NONE ^
    --distpath "dist" ^
    --workpath "build" ^
    "ui_app/qt_minimal.py"

if %errorlevel% neq 0 (
    echo.
    echo [错误] 打包失败！
    pause
    exit /b 1
)

echo.
echo [成功] 打包完成！
echo.
echo [信息] 输出位置: dist\1688数据工具\
echo [信息] 可执行文件: dist\1688数据工具\1688数据工具.exe
echo.
echo [提示] 打包大小优化建议:
echo   1. 删除不需要的Qt插件: dist\1688数据工具\PyQt6\Qt6\plugins\
echo   2. 只保留必要的插件: platforms, imageformats
echo   3. 使用UPX压缩(需要单独安装)
echo.

pause