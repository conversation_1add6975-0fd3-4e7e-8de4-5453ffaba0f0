#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688订单数据处理工具 - 真实功能版
集成真实的数据处理功能，不是演示版本
支持深色主题和自适应字体大小
"""

import sys
import os
import time
import asyncio
import json
import re
import subprocess
from pathlib import Path
from threading import Event
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QTextEdit, QTextBrowser, QRadioButton,
    QButtonGroup, QFileDialog, QMessageBox, QGroupBox
)
from PyQt6.QtCore import QUrl, QVariant

class SafeTextBrowser(QTextBrowser):
    """安全的文本浏览器，阻止自动加载文件内容"""

    def loadResource(self, type, name):
        """重写loadResource方法，阻止加载文件内容"""
        # 对于file://协议的URL，返回空内容，阻止加载
        if isinstance(name, QUrl) and name.scheme() == 'file':
            return QVariant()
        # 对于其他资源，使用默认行为
        return super().loadResource(type, name)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
import playwright

# 定义项目根目录并添加到Python路径
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 尝试导入项目模块
try:
    from src.core.enrich_orders_with_images import OrderEnricher
    REAL_FUNCTIONS_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入功能模块: {e}")
    REAL_FUNCTIONS_AVAILABLE = False


def is_dark_theme():
    """检测是否为深色主题"""
    app = QApplication.instance()
    if app is None:
        return False
    
    palette = app.palette()
    background_color = palette.color(QPalette.ColorRole.Window)
    # 如果背景色较暗，认为是深色主题
    return background_color.lightness() < 128


def get_theme_colors():
    """获取主题颜色"""
    if is_dark_theme():
        return {
            'bg': '#2b2b2b',
            'text': '#ffffff',
            'text_secondary': '#cccccc',
            'border': '#555555',
            'button_bg': '#3c3c3c',
            'button_hover': '#4c4c4c',
            'button_pressed': '#2c2c2c',
            'progress_bg': '#3c3c3c',
            'progress_chunk': '#4CAF50',
            'success': '#4CAF50',
            'warning': '#FF9800',
            'error': '#f44336',
            'info': '#2196F3'
        }
    else:
        return {
            'bg': '#f5f5f5',
            'text': '#333333',
            'text_secondary': '#666666',
            'border': '#dddddd',
            'button_bg': '#2196F3',
            'button_hover': '#1976D2',
            'button_pressed': '#0D47A1',
            'progress_bg': '#ffffff',
            'progress_chunk': '#4CAF50',
            'success': '#4CAF50',
            'warning': '#FF9800',
            'error': '#f44336',
            'info': '#2196F3'
        }


def get_stylesheet():
    """获取样式表"""
    colors = get_theme_colors()
    
    return f"""
    QMainWindow {{
        background-color: {colors['bg']};
        color: {colors['text']};
    }}
    
    QWidget {{
        background-color: {colors['bg']};
        color: {colors['text']};
        font-family: "微软雅黑", "Microsoft YaHei", Arial, sans-serif;
        font-size: 11px;
    }}
    
    QLabel {{
        background-color: transparent;
        color: {colors['text']};
        font-size: 12px;
    }}
    
    QLabel[class="title"] {{
        font-size: 16px;
        font-weight: bold;
        color: {colors['text']};
    }}
    
    QLabel[class="status"] {{
        font-size: 11px;
        color: {colors['text_secondary']};
    }}
    
    QPushButton {{
        background-color: {colors['button_bg']};
        color: white;
        border: 1px solid {colors['border']};
        border-radius: 6px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 500;
        min-height: 20px;
    }}
    
    QPushButton:hover {{
        background-color: {colors['button_hover']};
        border-color: {colors['border']};
    }}
    
    QPushButton:pressed {{
        background-color: {colors['button_pressed']};
    }}
    
    QPushButton:disabled {{
        background-color: {colors['border']};
        color: {colors['text_secondary']};
    }}
    
    QRadioButton {{
        background-color: transparent;
        color: {colors['text']};
        font-size: 12px;
        spacing: 8px;
        padding: 4px;
    }}
    
    QRadioButton::indicator {{
        width: 16px;
        height: 16px;
        border: 2px solid {colors['border']};
        border-radius: 8px;
        background-color: {colors['bg']};
    }}
    
    QRadioButton::indicator:checked {{
        background-color: {colors['button_bg']};
        border-color: {colors['button_bg']};
    }}
    
    QGroupBox {{
        border: 2px solid {colors['border']};
        border-radius: 8px;
        margin-top: 12px;
        padding-top: 12px;
        font-size: 12px;
        font-weight: bold;
        color: {colors['text']};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 12px;
        padding: 0 8px 0 8px;
        background-color: {colors['bg']};
    }}
    
    QProgressBar {{
        border: 2px solid {colors['border']};
        border-radius: 6px;
        text-align: center;
        height: 24px;
        font-size: 11px;
        font-weight: bold;
        color: {colors['text']};
        background-color: {colors['progress_bg']};
    }}
    
    QProgressBar::chunk {{
        background-color: {colors['progress_chunk']};
        border-radius: 4px;
    }}
    
    QTextEdit {{
        border: 2px solid {colors['border']};
        border-radius: 6px;
        background-color: {colors['bg']};
        color: {colors['text']};
        font-family: "Consolas", "Courier New", monospace;
        font-size: 11px;
        padding: 8px;
        selection-background-color: {colors['button_bg']};
    }}
    
    QTextEdit:focus {{
        border-color: {colors['button_bg']};
    }}
    """


class BrowserPrepWorker(QThread):
    """在后台准备浏览器环境的工作线程"""
    result = pyqtSignal(str)  # 'READY', 'ZOMBIE', 'CLEAN'

    def run(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            status = loop.run_until_complete(self.check_browser_status())
            self.result.emit(status)
        finally:
            loop.close()

    async def check_browser_status(self) -> str:
        debug_port = 9222
        try:
            import requests
            from playwright.async_api import async_playwright

            # 首先检查HTTP接口
            try:
                version_url = f"http://localhost:{debug_port}/json/version"
                # 禁用代理，确保直接连接到localhost
                proxies = {'http': None, 'https': None}
                response = requests.get(version_url, timeout=5, proxies=proxies)
                if response.status_code == 200:
                    version_info = response.json()
                    ws_url = version_info.get('webSocketDebuggerUrl')
                    if not ws_url:
                        return 'CLEAN'
                elif response.status_code == 503:
                    # 503错误表示Chrome正在初始化，返回INITIALIZING状态
                    return 'INITIALIZING'
                else:
                    return 'CLEAN'
            except requests.exceptions.ConnectionError:
                return 'CLEAN'
            except Exception:
                return 'CLEAN'

            # 使用WebSocket连接检查页面
            async with async_playwright() as playwright:
                try:
                    browser = await playwright.chromium.connect_over_cdp(ws_url, timeout=5000)
                    found_1688_page = False
                    if browser.contexts and browser.contexts[0].pages:
                        for page in browser.contexts[0].pages:
                            if '1688.com' in page.url:
                                found_1688_page = True
                                break
                    await browser.close()
                    return 'READY' if found_1688_page else 'ZOMBIE'
                except Exception as e:
                    error_msg = str(e).lower()
                    if "503" in error_msg:
                        return 'INITIALIZING'
                    elif "econnrefused" in error_msg:
                        return 'CLEAN'
                    else:
                        return 'CLEAN'
        except Exception:
            return 'CLEAN'


class RealWorker(QThread):
    """真实功能工作线程"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    log = pyqtSignal(str, str)
    done = pyqtSignal()
    user_confirmation_required = pyqtSignal(str)

    def __init__(self, mode, file_path=None):
        super().__init__()
        self.mode = mode
        self.file_path = file_path
        self.running = True
        self.user_confirmed = Event()

    def stop(self):
        self.running = False

    def confirm_user_action(self):
        self.user_confirmed.set()

    def run(self):
        try:
            if self.mode == 'order': self.process_order_real()
            elif self.mode == 'extract': self.process_extract_orders_real()
        except Exception as e:
            self.log.emit(f"错误: {str(e)}", "error")
        finally:
            self.done.emit()





    def process_order_real(self):
        if not self.file_path or not os.path.exists(self.file_path):
            self.log.emit(f"文件不存在: {self.file_path}", "error")
            return
        self.log.emit(f"开始处理订单文件: {os.path.basename(self.file_path)}", "info")
        try:
            self.progress.emit(10)
            self.status.emit("正在增强数据...")
            base_name = os.path.splitext(self.file_path)[0]
            output_file = f"{base_name}_enhanced.xlsx"
            enricher = OrderEnricher()
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(enricher.enrich_orders(self.file_path, output_file))
            loop.close()
            self.progress.emit(100)
            self.log.emit(f"订单数据处理完成! 输出文件: {output_file}", "success")
            self.status.emit("完成")
        except Exception as e:
            self.log.emit(f"处理失败: {str(e)}", "error")
            self.status.emit("失败")

    def process_extract_orders_real(self):
        self.log.emit("等待用户确认开始提取...", "info")
        self.user_confirmation_required.emit(self.mode)
        while not self.user_confirmed.is_set() and self.running:
            self.msleep(100)
        if not self.running: return

        self.progress.emit(20)
        self.status.emit("正在提取订单数据...")
        script_path = PROJECT_ROOT / "src" / "core" / "extract_orders.py"
        command = [sys.executable, str(script_path)]
        self.log.emit(f"执行脚本: {' '.join(command)}", "info")
        result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8', shell=False)
        if result.returncode == 0:
            self.log.emit("订单提取脚本成功执行。", "success")
            self.log.emit(f"输出: {result.stdout}", "info")
        else:
            self.log.emit("订单提取脚本执行失败。", "error")
            self.log.emit(f"错误: {result.stderr}", "error")
        self.progress.emit(100)
        self.status.emit("完成")



class RealFunctionApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.browser_prep_worker = None
        self.setup_ui()
        self.apply_theme()
        if not REAL_FUNCTIONS_AVAILABLE:
            self.add_log("警告: 运行在演示模式...", "warning")
        else:
            self.add_log("完整功能模式已启用", "success")
            self.on_mode_selected(self.radio_group.checkedButton())

    def setup_ui(self):
        self.setWindowTitle("1688订单数据处理工具")
        self.setFixedSize(600, 500)  # 增大窗口尺寸
        widget = QWidget()
        self.setCentralWidget(widget)
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)  # 增加边距
        layout.setSpacing(15)  # 增加间距
        
        # 标题
        title = QLabel("1688订单数据处理工具")
        title.setProperty("class", "title")  # 设置CSS类
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 功能选择组
        func_group = QGroupBox("选择功能")
        func_layout = QVBoxLayout()
        func_layout.setSpacing(10)  # 增加单选按钮间距
        
        self.radio_group = QButtonGroup()
        self.radio_group.buttonClicked.connect(self.on_mode_selected)
        modes = [("订单数据抓取", "extract"), ("订单数据增强", "order")]
        for text, mode in modes:
            radio = QRadioButton(text)
            radio.mode = mode
            self.radio_group.addButton(radio)
            func_layout.addWidget(radio)
        
        self.radio_group.buttons()[0].setChecked(True)
        func_group.setLayout(func_layout)
        layout.addWidget(func_group)
        
        # 进度条
        self.progress = QProgressBar()
        self.progress.setFixedHeight(30)  # 增大进度条高度
        layout.addWidget(self.progress)
        
        # 状态标签
        self.status = QLabel("准备就绪")
        self.status.setProperty("class", "status")  # 设置CSS类
        self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status)
        
        # 日志文本区域 - 使用SafeTextBrowser支持超链接但阻止文件加载
        self.log_text = SafeTextBrowser()
        self.log_text.setMinimumHeight(150)  # 设置最小高度
        self.log_text.setOpenExternalLinks(False)  # 禁用自动打开外部链接，使用自定义处理
        self.log_text.setOpenLinks(False)  # 禁用自动加载链接内容
        # 连接超链接点击事件
        self.log_text.anchorClicked.connect(self.handle_link_clicked)
        layout.addWidget(self.log_text)
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)  # 增加按钮间距
        
        self.start_btn = QPushButton("开始处理")
        self.start_btn.setMinimumHeight(35)  # 增大按钮高度
        self.start_btn.clicked.connect(self.start_work)
        btn_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setMinimumHeight(35)  # 增大按钮高度
        self.stop_btn.clicked.connect(self.stop_work)
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)
        
        self.exit_btn = QPushButton("退出")
        self.exit_btn.setMinimumHeight(35)  # 增大按钮高度
        self.exit_btn.clicked.connect(self.close)
        btn_layout.addWidget(self.exit_btn)
        
        layout.addLayout(btn_layout)

    def apply_theme(self):
        """应用主题样式"""
        self.setStyleSheet(get_stylesheet())

    def on_mode_selected(self, button):
        mode = button.mode
        if mode in ['cart', 'extract']:
            self.status.setText("正在准备浏览器环境...")
            self.add_log(f"模式已切换，正在后台准备浏览器环境...", "info")
            if self.browser_prep_worker and self.browser_prep_worker.isRunning():
                return
            self.browser_prep_worker = BrowserPrepWorker()
            self.browser_prep_worker.result.connect(self.handle_browser_prep_result)
            self.browser_prep_worker.start()
        else:
            self.status.setText("准备就绪")

    def handle_browser_prep_result(self, status):
        if status == 'READY':
            self.add_log("检测到有效的1688会话，浏览器环境已就绪。", "success")
            self.status.setText("浏览器已就绪")
        elif status == 'INITIALIZING':
            self.add_log("检测到Chrome正在初始化，请稍等...", "info")
            self.status.setText("Chrome正在初始化...")
            # 等待一段时间后重新检查
            QTimer.singleShot(5000, lambda: self.on_mode_selected(self.radio_group.checkedButton()))
        elif status == 'ZOMBIE':
            self.add_log("警告: 端口被未知进程占用。", "warning")
            self.status.setText("操作确认")
            reply = QMessageBox.question(self, "环境问题",
                "端口9222已被未知进程占用（可能是上次未关闭的Chrome）。\n\n是否需要我尝试为您强制清理该进程？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.add_log("正在尝试清理进程...", "info")
                self.kill_process_on_port(9222)
            else:
                self.add_log("操作已取消。请手动清理进程后重试。", "warning")
                self.status.setText("请手动处理")
        elif status == 'CLEAN':
            self.add_log("未检测到Chrome会话，正在启动新实例...", "info")
            self.status.setText("正在启动浏览器...")
            self.start_chrome_debug()

    def kill_process_on_port(self, port):
        try:
            if sys.platform == "win32":
                command = f"netstat -aon | findstr :{port}"
                result = subprocess.run(command, capture_output=True, text=True, shell=True, encoding='gbk')
                output = result.stdout.strip()
                if not output:
                    self.add_log(f"未找到占用端口 {port} 的进程。", "warning")
                    return
                pid = output.split()[-1]
                self.add_log(f"找到占用端口 {port} 的进程ID: {pid}，正在终止...", "info")
                kill_command = f"taskkill /F /PID {pid}"
                kill_result = subprocess.run(kill_command, capture_output=True, text=True, shell=True, encoding='gbk')
                if kill_result.returncode == 0 and ("成功" in kill_result.stdout or "SUCCESS" in kill_result.stdout.upper()):
                    self.add_log(f"成功终止进程 {pid}。", "success")
                    # 重新检查环境状态
                    self.add_log("重新检查浏览器环境状态...", "info")
                    QTimer.singleShot(2000, lambda: self.on_mode_selected(self.radio_group.checkedButton()))
                else:
                    self.add_log(f"终止进程 {pid} 失败: {kill_result.stderr or kill_result.stdout}", "error")
            else: # macOS / Linux
                command = f"lsof -i tcp:{port} | grep LISTEN | awk '{{print $2}}' | xargs kill -9"
                subprocess.run(command, shell=True)
                self.add_log(f"已发送终止命令到占用端口 {port} 的进程。", "info")
                # 重新检查环境状态
                self.add_log("重新检查浏览器环境状态...", "info")
                QTimer.singleShot(2000, lambda: self.on_mode_selected(self.radio_group.checkedButton()))
        except Exception as e:
            self.add_log(f"清理进程时出错: {e}", "error")

    def start_chrome_debug(self):
        try:
            script_path = PROJECT_ROOT / "start_debug_chrome.bat"
            if not script_path.exists():
                self.add_log(f"启动脚本未找到: {script_path}", "error")
                return
            self.add_log(f"正在执行启动脚本: {script_path}", "info")
            subprocess.Popen([str(script_path)], shell=True)
            self.status.setText("等待浏览器启动...")
            self.add_log("Chrome正在启动，请等待20-30秒完全初始化...", "info")
            self.add_log("启动过程：", "info")
            self.add_log("1. Chrome浏览器窗口打开", "info")
            self.add_log("2. 调试接口初始化（10-15秒）", "info")
            self.add_log("3. 请在Chrome中登录1688账号", "info")
            self.add_log("4. 导航到订单页面", "info")
            self.add_log("5. 完成上述步骤后，点击开始处理", "info")
            
        except Exception as e:
            self.add_log(f"启动Chrome时发生错误: {str(e)}", "error")

    def start_work(self):
        checked = self.radio_group.checkedButton()
        mode = checked.mode if checked else 'cart'
        file_path = None
        if mode == 'order':
            file_path, _ = QFileDialog.getOpenFileName(self, "选择Excel文件", str(PROJECT_ROOT), "Excel (*.xlsx *.xls)")
            if not file_path: return
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress.setValue(0)
        self.worker = RealWorker(mode, file_path)
        self.worker.progress.connect(self.progress.setValue)
        self.worker.status.connect(self.status.setText)
        self.worker.log.connect(self.add_log)
        self.worker.done.connect(self.work_done)
        self.worker.user_confirmation_required.connect(self.show_user_confirmation_dialog)
        self.worker.start()
    
    def stop_work(self):
        if self.worker and self.worker.isRunning():
            self.worker.stop()
        self.add_log("已停止", "warning")
        self.reset_buttons()
    
    def work_done(self):
        self.reset_buttons()
        if self.worker:
            self.worker.deleteLater()
            self.worker = None
    
    def show_user_confirmation_dialog(self, mode):
        message = {
            'extract': "请确认以下步骤已完成：\n\n1. Chrome已完全启动（等待20-30秒）\n2. 已登录1688账号\n3. 已打开订单页面\n4. 页面完全加载完成\n\n如果Chrome还在启动中，请等待完成后再继续。\n\n准备好后点击确定继续..."
        }.get(mode)
        if not message:
            if self.worker: self.worker.confirm_user_action()
            return
        reply = QMessageBox.question(self, "用户确认", message, QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
        if reply == QMessageBox.StandardButton.Ok:
            if self.worker: self.worker.confirm_user_action()
        else:
            self.add_log("用户取消了操作", "warning")
            self.stop_work()
    
    def reset_buttons(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def add_log(self, msg, msg_type="info"):
        timestamp = time.strftime("%H:%M:%S")
        colors = get_theme_colors()
        color = colors.get(msg_type, colors['text'])

        # 检查消息中是否包含文件路径，如果有则转换为超链接
        enhanced_msg = self.enhance_message_with_links(msg)

        self.log_text.append(f'<span style="color: {color};">[{timestamp}] {enhanced_msg}</span>')

    def enhance_message_with_links(self, msg):
        """将消息中的文件路径转换为可点击的超链接"""
        import re
        import os

        # 匹配文件路径的正则表达式 - 优先匹配绝对路径，避免重叠
        # 匹配 Windows 绝对路径格式，如 D:\path\to\file.xlsx
        absolute_pattern = r'([A-Za-z]:[\\\/][^<>\s]+\.(?:xlsx|xls|json|csv|txt))'

        enhanced_msg = msg

        # 首先处理绝对路径
        matches = list(re.finditer(absolute_pattern, enhanced_msg))
        for match in reversed(matches):  # 从后往前替换，避免位置偏移
            file_path = match.group(1)
            start, end = match.span(1)

            # 确保路径存在
            if os.path.exists(file_path):
                # 创建可点击的超链接
                file_name = os.path.basename(file_path)

                # 确保使用绝对路径
                if not os.path.isabs(file_path):
                    abs_file_path = os.path.abspath(file_path)
                    abs_dir_path = os.path.dirname(abs_file_path)
                else:
                    abs_file_path = file_path
                    abs_dir_path = os.path.dirname(file_path)

                # 将Windows路径转换为URL格式（使用正斜杠）
                file_url = abs_file_path.replace('\\', '/')
                dir_url = abs_dir_path.replace('\\', '/')

                # 创建超链接HTML
                link_html = (
                    f'<a href="file:///{file_url}" '
                    f'style="color: #2196F3; text-decoration: underline;" '
                    f'title="点击打开文件: {abs_file_path}">'
                    f'{file_name}</a> '
                    f'<a href="file:///{dir_url}" '
                    f'style="color: #4CAF50; text-decoration: none; font-size: 0.9em;" '
                    f'title="点击打开目录: {abs_dir_path}">'
                    f'📁</a>'
                )

                # 替换原始路径
                enhanced_msg = enhanced_msg[:start] + link_html + enhanced_msg[end:]

        # 然后处理相对路径（只有在没有被绝对路径处理过的情况下）
        if '<a href=' not in enhanced_msg:  # 如果还没有超链接
            relative_patterns = [
                r'(reports[\\\/][^<>\s]+\.(?:xlsx|xls|json|csv|txt))',     # reports目录相对路径
                r'(data[\\\/][^<>\s]+\.(?:xlsx|xls|json|csv|txt))',        # data目录相对路径
            ]

            for pattern in relative_patterns:
                matches = list(re.finditer(pattern, enhanced_msg))
                for match in reversed(matches):
                    file_path = match.group(1)
                    start, end = match.span(1)

                    # 确保路径存在
                    if os.path.exists(file_path):
                        # 创建可点击的超链接
                        file_name = os.path.basename(file_path)

                        # 对于相对路径，转换为绝对路径
                        if not os.path.isabs(file_path):
                            abs_file_path = os.path.abspath(file_path)
                            abs_dir_path = os.path.dirname(abs_file_path)
                        else:
                            abs_file_path = file_path
                            abs_dir_path = os.path.dirname(file_path)

                        # 将Windows路径转换为URL格式（使用正斜杠）
                        file_url = abs_file_path.replace('\\', '/')
                        dir_url = abs_dir_path.replace('\\', '/')

                        # 创建超链接HTML
                        link_html = (
                            f'<a href="file:///{file_url}" '
                            f'style="color: #2196F3; text-decoration: underline;" '
                            f'title="点击打开文件: {abs_file_path}">'
                            f'{file_name}</a> '
                            f'<a href="file:///{dir_url}" '
                            f'style="color: #4CAF50; text-decoration: none; font-size: 0.9em;" '
                            f'title="点击打开目录: {abs_dir_path}">'
                            f'📁</a>'
                        )

                        # 替换原始路径
                        enhanced_msg = enhanced_msg[:start] + link_html + enhanced_msg[end:]
                        break  # 只处理第一个匹配的相对路径

        return enhanced_msg

    def handle_link_clicked(self, url):
        """处理超链接点击事件"""
        import os
        import subprocess
        from urllib.parse import unquote
        from PyQt6.QtCore import QUrl

        # 将QUrl转换为字符串
        url_str = url.toString()

        # 移除file:///前缀并解码URL
        if url_str.startswith('file:///'):
            file_path = unquote(url_str[8:])  # 移除file:///并解码
        else:
            file_path = unquote(url_str)  # 解码URL

        # 清理路径中的特殊字符（如📁图标）
        file_path = file_path.replace(' 📁', '').strip()



        # 确保路径存在
        if os.path.exists(file_path):
            try:
                if os.path.isfile(file_path):
                    # 打开文件 - 使用subprocess避免可能的输出重定向问题
                    if sys.platform == "win32":
                        # 使用subprocess.run而不是os.startfile，避免输出重定向
                        subprocess.run(["cmd", "/c", "start", "", file_path],
                                     shell=False,
                                     stdout=subprocess.DEVNULL,
                                     stderr=subprocess.DEVNULL)
                    elif sys.platform == "darwin":  # macOS
                        subprocess.run(["open", file_path],
                                     stdout=subprocess.DEVNULL,
                                     stderr=subprocess.DEVNULL)
                    else:  # Linux
                        subprocess.run(["xdg-open", file_path],
                                     stdout=subprocess.DEVNULL,
                                     stderr=subprocess.DEVNULL)



                elif os.path.isdir(file_path):
                    # 打开目录
                    if sys.platform == "win32":
                        # Windows explorer需要反斜杠格式的路径
                        windows_path = file_path.replace('/', '\\')
                        subprocess.run(["explorer", windows_path])
                    elif sys.platform == "darwin":  # macOS
                        subprocess.run(["open", file_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", file_path])



            except Exception as e:
                self.add_log(f"打开失败: {e}", "error")
        else:
            self.add_log(f"文件不存在: {file_path}", "warning")

    def closeEvent(self, event):
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()
        event.accept()

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("1688订单数据处理工具")
    
    # 设置应用信息
    app.setApplicationVersion("1.0")
    app.setOrganizationName("1688自动化工具")
    
    # 创建并显示主窗口
    window = RealFunctionApp()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()