"""
1688采购车数据处理工具 - 主程序入口
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.main_window import MainWindow
from ui.function_selection import FunctionSelectionWindow
from core.config import Config
from core.error_handler import ErrorHandler
from utils.logger import setup_logger

class Application:
    """主应用程序类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("1688采购车数据处理工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置应用图标
        self.set_app_icon()
        
        # 初始化配置
        self.config = Config()
        
        # 初始化错误处理器
        self.error_handler = ErrorHandler()
        
        # 设置日志
        self.logger = setup_logger()
        
        # 当前模式
        self.current_mode = None
        
        # 主窗口引用
        self.main_window = None
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 显示功能选择界面
        self.show_function_selection()
        
    def set_app_icon(self):
        """设置应用图标"""
        icon_path = Path(__file__).parent / "assets" / "icon.ico"
        if icon_path.exists():
            try:
                self.root.iconbitmap(str(icon_path))
            except Exception as e:
                self.logger.warning(f"无法加载应用图标: {e}")
    
    def show_function_selection(self):
        """显示功能选择界面"""
        if self.main_window:
            self.main_window.destroy()
            self.main_window = None
        
        self.function_window = FunctionSelectionWindow(
            self.root,
            on_cart_selected=self.start_cart_mode,
            on_order_selected=self.start_order_mode,
            on_exit=self.on_closing
        )
    
    def start_cart_mode(self):
        """启动采购车数据提取模式"""
        self.current_mode = 'cart'
        self.function_window.destroy()
        self.function_window = None
        
        self.main_window = MainWindow(
            self.root,
            mode='cart',
            config=self.config,
            on_back=self.show_function_selection
        )
    
    def start_order_mode(self):
        """启动订单数据增强模式"""
        self.current_mode = 'order'
        self.function_window.destroy()
        self.function_window = None
        
        self.main_window = MainWindow(
            self.root,
            mode='order',
            config=self.config,
            on_back=self.show_function_selection
        )
    
    def on_closing(self):
        """处理窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.logger.info("应用程序关闭")
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("应用程序启动")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"应用程序运行错误: {e}")
            messagebox.showerror("错误", f"程序运行出错：{str(e)}")

def main():
    """主函数"""
    try:
        app = Application()
        app.run()
    except Exception as e:
        print(f"程序启动失败：{e}")
        input("按任意键退出...")

if __name__ == "__main__":
    main()