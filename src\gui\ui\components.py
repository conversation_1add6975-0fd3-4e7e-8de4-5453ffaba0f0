"""
UI组件模块
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, List, Any
import time

class ProgressBar:
    """进度条组件"""
    
    def __init__(self, parent, width=400, height=20):
        self.frame = tk.Frame(parent)
        self.width = width
        self.height = height
        
        # 创建进度条背景
        self.canvas = tk.Canvas(
            self.frame,
            width=width,
            height=height,
            bg="#f0f0f0",
            highlightthickness=1,
            highlightbackground="#ddd"
        )
        self.canvas.pack()
        
        # 创建进度条前景
        self.progress_rect = self.canvas.create_rectangle(
            0, 0, 0, height,
            fill="#2196F3",
            outline=""
        )
        
        # 创建文本标签
        self.text_label = self.canvas.create_text(
            width // 2, height // 2,
            text="0%",
            fill="#333",
            font=("Arial", 10)
        )
    
    def update_progress(self, percentage: float, text: str = ""):
        """更新进度"""
        if percentage < 0:
            percentage = 0
        elif percentage > 100:
            percentage = 100
        
        # 更新进度条宽度
        new_width = int(self.width * percentage / 100)
        self.canvas.coords(self.progress_rect, 0, 0, new_width, self.height)
        
        # 更新文本
        display_text = text if text else f"{percentage:.1f}%"
        self.canvas.itemconfig(self.text_label, text=display_text)
    
    def set_color(self, color: str):
        """设置进度条颜色"""
        self.canvas.itemconfig(self.progress_rect, fill=color)
    
    def reset(self):
        """重置进度条"""
        self.update_progress(0, "0%")
    
    def pack(self, **kwargs):
        """包装pack方法"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """包装grid方法"""
        self.frame.grid(**kwargs)

class LogViewer:
    """日志查看器组件"""
    
    def __init__(self, parent, height=200):
        self.frame = tk.Frame(parent)
        
        # 创建滚动条
        scrollbar = tk.Scrollbar(self.frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建文本框
        self.text_widget = tk.Text(
            self.frame,
            height=height,
            wrap=tk.WORD,
            yscrollcommand=scrollbar.set,
            bg="#f8f8f8",
            fg="#333",
            font=("Consolas", 9)
        )
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        scrollbar.config(command=self.text_widget.yview)
        
        # 配置文本标签
        self.text_widget.tag_config("info", foreground="#333")
        self.text_widget.tag_config("success", foreground="#4CAF50")
        self.text_widget.tag_config("warning", foreground="#FF9800")
        self.text_widget.tag_config("error", foreground="#f44336")
        self.text_widget.tag_config("debug", foreground="#9E9E9E")
        
        # 设置文本为只读
        self.text_widget.config(state=tk.DISABLED)
    
    def log(self, message: str, level: str = "info"):
        """添加日志消息"""
        self.text_widget.config(state=tk.NORMAL)
        
        # 添加时间戳
        timestamp = time.strftime("%H:%M:%S")
        self.text_widget.insert(tk.END, f"[{timestamp}] ", "debug")
        
        # 添加消息
        self.text_widget.insert(tk.END, f"{message}\n", level)
        
        # 自动滚动到底部
        self.text_widget.see(tk.END)
        
        # 限制日志行数
        line_count = int(self.text_widget.index('end-1c').split('.')[0])
        if line_count > 1000:
            self.text_widget.delete('1.0', '2.0')
        
        self.text_widget.config(state=tk.DISABLED)
    
    def log_info(self, message: str):
        """添加信息日志"""
        self.log(message, "info")
    
    def log_success(self, message: str):
        """添加成功日志"""
        self.log(message, "success")
    
    def log_warning(self, message: str):
        """添加警告日志"""
        self.log(message, "warning")
    
    def log_error(self, message: str):
        """添加错误日志"""
        self.log(message, "error")
    
    def log_debug(self, message: str):
        """添加调试日志"""
        self.log(message, "debug")
    
    def clear(self):
        """清空日志"""
        self.text_widget.config(state=tk.NORMAL)
        self.text_widget.delete('1.0', tk.END)
        self.text_widget.config(state=tk.DISABLED)
    
    def pack(self, **kwargs):
        """包装pack方法"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """包装grid方法"""
        self.frame.grid(**kwargs)

class StepIndicator:
    """步骤指示器组件"""
    
    def __init__(self, parent, steps: List[str]):
        self.frame = tk.Frame(parent)
        self.steps = steps
        self.current_step = 0
        self.step_labels = []
        
        self.create_indicators()
    
    def create_indicators(self):
        """创建步骤指示器"""
        for i, step_name in enumerate(self.steps):
            step_frame = tk.Frame(self.frame)
            step_frame.pack(side=tk.LEFT, padx=5)
            
            # 步骤编号
            step_number = tk.Label(
                step_frame,
                text=str(i + 1),
                width=3,
                height=1,
                bg="#e0e0e0",
                fg="#666",
                relief=tk.RAISED,
                bd=1,
                font=("Arial", 10, "bold")
            )
            step_number.pack()
            
            # 步骤名称
            step_label = tk.Label(
                step_frame,
                text=step_name,
                font=("Arial", 9),
                fg="#666"
            )
            step_label.pack()
            
            self.step_labels.append({
                "frame": step_frame,
                "number": step_number,
                "label": step_label
            })
            
            # 添加连接线（除了最后一个）
            if i < len(self.steps) - 1:
                connector = tk.Label(
                    self.frame,
                    text="→",
                    font=("Arial", 12),
                    fg="#ccc"
                )
                connector.pack(side=tk.LEFT, padx=2)
    
    def set_current_step(self, step_index: int):
        """设置当前步骤"""
        if 0 <= step_index < len(self.steps):
            # 重置之前的步骤
            for i in range(len(self.steps)):
                step_data = self.step_labels[i]
                if i < step_index:
                    # 已完成的步骤
                    step_data["number"].config(bg="#4CAF50", fg="white")
                    step_data["label"].config(fg="#4CAF50")
                elif i == step_index:
                    # 当前步骤
                    step_data["number"].config(bg="#2196F3", fg="white")
                    step_data["label"].config(fg="#2196F3")
                else:
                    # 未完成的步骤
                    step_data["number"].config(bg="#e0e0e0", fg="#666")
                    step_data["label"].config(fg="#666")
            
            self.current_step = step_index
    
    def set_step_completed(self, step_index: int):
        """设置步骤为已完成"""
        if 0 <= step_index < len(self.steps):
            step_data = self.step_labels[step_index]
            step_data["number"].config(bg="#4CAF50", fg="white")
            step_data["label"].config(fg="#4CAF50")
    
    def set_step_error(self, step_index: int):
        """设置步骤为错误状态"""
        if 0 <= step_index < len(self.steps):
            step_data = self.step_labels[step_index]
            step_data["number"].config(bg="#f44336", fg="white")
            step_data["label"].config(fg="#f44336")
    
    def pack(self, **kwargs):
        """包装pack方法"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """包装grid方法"""
        self.frame.grid(**kwargs)

class StatusPanel:
    """状态面板组件"""
    
    def __init__(self, parent):
        self.frame = tk.Frame(parent, relief=tk.SUNKEN, bd=1)
        
        # 状态标签
        self.status_label = tk.Label(
            self.frame,
            text="准备就绪",
            anchor=tk.W,
            padx=10,
            pady=5,
            font=("Arial", 10)
        )
        self.status_label.pack(fill=tk.X)
    
    def set_status(self, status: str, color: str = "black"):
        """设置状态"""
        self.status_label.config(text=status, fg=color)
    
    def set_ready(self):
        """设置为准备状态"""
        self.set_status("准备就绪", "#4CAF50")
    
    def set_busy(self, message: str = "处理中..."):
        """设置为忙碌状态"""
        self.set_status(message, "#FF9800")
    
    def set_error(self, message: str = "发生错误"):
        """设置为错误状态"""
        self.set_status(message, "#f44336")
    
    def set_success(self, message: str = "操作成功"):
        """设置为成功状态"""
        self.set_status(message, "#4CAF50")
    
    def pack(self, **kwargs):
        """包装pack方法"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """包装grid方法"""
        self.frame.grid(**kwargs)

class ActionButton:
    """动作按钮组件"""
    
    def __init__(self, parent, text: str, command: Callable, 
                 primary: bool = False, danger: bool = False, 
                 width: int = 100, height: int = 30):
        self.command = command
        
        # 设置颜色
        if primary:
            bg_color = "#2196F3"
            hover_color = "#1976D2"
        elif danger:
            bg_color = "#f44336"
            hover_color = "#d32f2f"
        else:
            bg_color = "#e0e0e0"
            hover_color = "#bdbdbd"
        
        # 创建按钮
        self.button = tk.Button(
            parent,
            text=text,
            width=width,
            height=height,
            bg=bg_color,
            fg="white" if primary or danger else "#333",
            relief=tk.FLAT,
            cursor="hand2",
            command=self._on_click,
            font=("Arial", 10)
        )
        
        # 绑定悬停事件
        self.button.bind("<Enter>", lambda e: self._on_enter(hover_color))
        self.button.bind("<Leave>", lambda e: self._on_leave(bg_color))
    
    def _on_click(self):
        """按钮点击事件"""
        try:
            self.command()
        except Exception as e:
            print(f"按钮点击错误: {e}")
    
    def _on_enter(self, color: str):
        """鼠标进入事件"""
        self.button.config(bg=color)
    
    def _on_leave(self, color: str):
        """鼠标离开事件"""
        self.button.config(bg=color)
    
    def config(self, **kwargs):
        """配置按钮属性"""
        self.button.config(**kwargs)
    
    def pack(self, **kwargs):
        """包装pack方法"""
        self.button.pack(**kwargs)
    
    def grid(self, **kwargs):
        """包装grid方法"""
        self.button.grid(**kwargs)
    
    def enable(self):
        """启用按钮"""
        self.button.config(state=tk.NORMAL)
    
    def disable(self):
        """禁用按钮"""
        self.button.config(state=tk.DISABLED)

class LoadingSpinner:
    """加载动画组件"""
    
    def __init__(self, parent, size: int = 20):
        self.frame = tk.Frame(parent)
        self.canvas = tk.Canvas(self.frame, width=size, height=size, bg="white")
        self.canvas.pack()
        
        self.size = size
        self.angle = 0
        self.is_spinning = False
        self.spinner_id = None
    
    def start(self):
        """开始动画"""
        if not self.is_spinning:
            self.is_spinning = True
            self._animate()
    
    def stop(self):
        """停止动画"""
        self.is_spinning = False
        if self.spinner_id:
            self.canvas.after_cancel(self.spinner_id)
            self.canvas.delete("all")
    
    def _animate(self):
        """动画循环"""
        if self.is_spinning:
            self.canvas.delete("all")
            
            # 绘制旋转的圆弧
            center = self.size // 2
            radius = center - 2
            
            for i in range(8):
                angle = self.angle + i * 45
                alpha = 1.0 - (i / 8.0)
                
                # 计算圆弧的起始和结束角度
                start_angle = angle - 10
                end_angle = angle + 10
                
                # 绘制圆弧
                self.canvas.create_arc(
                    2, 2, self.size - 2, self.size - 2,
                    start=start_angle, extent=end_angle - start_angle,
                    outline=f"#{int(255 * alpha):02x}{int(255 * alpha):02x}{int(255 * alpha):02x}",
                    width=2,
                    style=tk.ARC
                )
            
            self.angle += 10
            if self.angle >= 360:
                self.angle = 0
            
            self.spinner_id = self.canvas.after(50, self._animate)
    
    def pack(self, **kwargs):
        """包装pack方法"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """包装grid方法"""
        self.frame.grid(**kwargs)