#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Qt真实功能版本
"""

import sys
import os
import subprocess
import time

# 设置控制台编码
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qt_real():
    """测试Qt真实功能版本"""
    print("测试Qt真实功能版本...")
    
    # 检查PyQt6是否可用
    try:
        from PyQt6.QtWidgets import QApplication
        print("[OK] PyQt6 可用")
    except ImportError:
        print("[ERROR] PyQt6 不可用")
        return False
    
    # 检查必要的文件
    files_to_check = [
        "ui_app/qt_real.py",
        "start_debug_chrome.bat",
        "enrich_orders_with_images.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"[OK] {file_path} 存在")
        else:
            print(f"[ERROR] {file_path} 不存在")
    
    # 测试应用启动
    print("\n测试应用启动...")
    try:
        # 使用subprocess启动应用，但不等待它完成
        process = subprocess.Popen([
            sys.executable, "ui_app/qt_real.py"
        ], creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == 'win32' else 0)
        
        print("[OK] 应用启动成功")
        print("[OK] PID:", process.pid)
        
        # 等待几秒钟
        time.sleep(3)
        
        # 终止进程（测试用）
        process.terminate()
        print("[OK] 测试完成，已终止进程")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 应用启动失败: {e}")
        return False

def test_packaging():
    """测试打包功能"""
    print("\n测试打包功能...")
    
    # 检查打包脚本
    build_scripts = [
        "build_qt_app.bat",
        "build_minimal.bat"
    ]
    
    for script in build_scripts:
        if os.path.exists(script):
            print(f"[OK] {script} 存在")
        else:
            print(f"[ERROR] {script} 不存在")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print("[OK] PyInstaller 可用")
    except ImportError:
        print("[ERROR] PyInstaller 不可用，请安装: pip install pyinstaller")

def main():
    """主函数"""
    print("=" * 50)
    print("1688数据工具 - Qt版本测试")
    print("=" * 50)
    
    # 测试基本功能
    test_result = test_qt_real()
    
    # 测试打包
    test_packaging()
    
    print("\n" + "=" * 50)
    if test_result:
        print("[OK] Qt版本测试通过")
        print("[OK] 可以使用以下命令运行:")
        print("  python ui_app/qt_real.py")
        print("  python ui_app/qt_minimal.py")
        print("  python ui_app/qt_ultralight.py")
    else:
        print("[ERROR] Qt版本测试失败")
        print("[ERROR] 请检查依赖和文件")
    print("=" * 50)

if __name__ == "__main__":
    main()