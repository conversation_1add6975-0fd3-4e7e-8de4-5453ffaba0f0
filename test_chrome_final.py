#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome连接修复最终验证测试
重点验证核心功能是否正常工作
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_playwright_core():
    """测试Playwright核心功能"""
    print("测试Playwright核心功能...")
    
    try:
        from playwright.async_api import async_playwright
        
        async def test_connection():
            async with async_playwright() as playwright:
                # 测试playwright实例是否正常工作
                print("OK Playwright实例创建成功")
                return True
        
        result = asyncio.run(test_connection())
        return result
        
    except Exception as e:
        print(f"ERROR Playwright核心功能失败: {e}")
        return False

def test_gui_imports():
    """测试GUI相关导入"""
    print("\n测试GUI模块导入...")
    
    try:
        # 测试PyQt6导入
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import QThread, pyqtSignal
        print("OK PyQt6导入成功")
        
        # 测试GUI模块导入
        from src.gui.qt_real import RealFunctionApp, BrowserPrepWorker, RealWorker
        print("OK GUI功能模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"ERROR GUI导入失败: {e}")
        return False

def test_chrome_connection_logic():
    """测试Chrome连接逻辑"""
    print("\n测试Chrome连接逻辑...")
    
    try:
        # 导入Chrome管理器
        sys.path.append(str(PROJECT_ROOT / "src" / "gui" / "core"))
        from chrome_manager import ChromeManager
        print("OK Chrome管理器导入成功")
        
        # 测试连接逻辑类
        from src.gui.qt_real import BrowserPrepWorker
        print("OK Chrome连接工作线程导入成功")
        
        return True
        
    except Exception as e:
        print(f"ERROR Chrome连接逻辑测试失败: {e}")
        return False

def test_export_functionality():
    """测试导出功能"""
    print("\n测试导出功能...")
    
    try:
        # 测试导出模块
        from src.core.export_cart_excel import ExportExcelAutomation
        print("OK 导出自动化类导入成功")
        
        # 验证类有正确的接口
        automation = ExportExcelAutomation()
        required_methods = ['test_chrome_connection', 'connect_to_debug_browser', 'extract_cart_data']
        
        for method in required_methods:
            if hasattr(automation, method):
                print(f"OK 方法 {method} 存在")
            else:
                print(f"ERROR 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"ERROR 导出功能测试失败: {e}")
        return False

def test_async_context_manager():
    """测试async上下文管理器"""
    print("\n测试async上下文管理器...")
    
    try:
        from playwright.async_api import async_playwright
        
        async def test_context_manager():
            # 这是修复前失败的核心功能
            async with async_playwright() as p:
                # 检查playwright对象是否有正确的属性
                if hasattr(p, 'chromium') and hasattr(p, 'stop'):
                    print("OK async_playwright上下文管理器正常工作")
                    return True
                else:
                    print("ERROR async_playwright对象属性不完整")
                    return False
        
        result = asyncio.run(test_context_manager())
        return result
        
    except Exception as e:
        print(f"ERROR async上下文管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Chrome连接修复最终验证测试")
    print("=" * 60)
    
    tests = [
        ("Playwright核心功能", test_playwright_core),
        ("GUI模块导入", test_gui_imports),
        ("Chrome连接逻辑", test_chrome_connection_logic),
        ("导出功能", test_export_functionality),
        ("Async上下文管理器", test_async_context_manager)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("最终验证结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed >= 4:  # 允许有一项失败
        print("🎉 Chrome连接修复验证成功！核心功能已恢复正常")
        return True
    else:
        print("⚠️  部分功能仍有问题，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)