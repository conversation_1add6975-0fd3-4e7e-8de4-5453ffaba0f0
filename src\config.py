# -*- coding: utf-8 -*-
"""
1688自动化项目配置管理
统一管理所有配置参数，便于维护和部署
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional

class Config:
    """配置管理类"""
    
    # 基础路径配置
    PROJECT_ROOT = Path(__file__).parent
    CACHE_DIR = PROJECT_ROOT / "cache"
    DATA_DIR = PROJECT_ROOT / "data"
    REPORTS_DIR = PROJECT_ROOT / "reports"
    LOGS_DIR = PROJECT_ROOT / "logs"
    TEST_OUTPUT_DIR = PROJECT_ROOT / "test_output"
    
    # Chrome调试配置
    CHROME_DEBUG_PORT = 9222
    CHROME_USER_DATA_DIR = os.path.join(os.getenv('TEMP', '/tmp'), 'chrome_debug')
    CHROME_LAUNCH_TIMEOUT = 30000  # 30秒
    
    # 网络配置
    REQUEST_TIMEOUT = 30
    MAX_RETRIES = 3
    CONCURRENT_LIMIT = 10
    NETWORK_IDLE_TIMEOUT = 5000  # 5秒
    
    # 数据提取配置
    MIN_MATCH_RATIO = 0.7
    MAX_IMAGE_SIZE = (120, 120)
    SCROLL_DELAY = 1000  # 1秒
    WAIT_TIMEOUT = 10000  # 10秒
    
    # 匹配配置
    CART_ITEM_SELECTORS = {
        'shop_container': '[class*="shop-container--container--"]',
        'company_name': '[class*="shop-top--companyName--"]',
        'item_group': '[class*="item-group--item--"]',
        'main_image': '[class*="fancy-image"][class*="item-group--image--"] img',
        'base_title': '[class*="item-group--title--"]',
        'spec_container': '[class*="item--"]',
        'spec_image': '[class*="fancy-image"][class*="item--image--"] img',
        'spec_title': '[class*="item--titleText--"]',
        'quantity_input': 'span[class*="next-input"][class*="next-medium"] input',
        'price_info': '[class*="price--"]'
    }
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = LOGS_DIR / '1688_automation.log'
    
    # Excel配置
    EXCEL_COLUMNS = [
        '序号', '商品名称', '生产商名称', '基础标题', '规格标题',
        '主图链接', '规格图片链接', '图片链接', '数量', '发布价格',
        '优惠价格', '单价', '小计', '店铺序号', '商品序号'
    ]
    
    # 性能配置
    BATCH_SIZE = 50
    MEMORY_CLEANUP_INTERVAL = 100
    PERFORMANCE_LOG_INTERVAL = 50
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.CACHE_DIR,
            cls.DATA_DIR,
            cls.REPORTS_DIR,
            cls.LOGS_DIR,
            cls.TEST_OUTPUT_DIR,
            cls.CACHE_DIR / "images"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def load_user_config(cls, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载用户配置文件"""
        if config_path is None:
            config_path = cls.PROJECT_ROOT / "user_config.json"
        
        if not config_path.exists():
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载用户配置失败: {e}")
            return {}
    
    @classmethod
    def save_user_config(cls, config: Dict[str, Any], config_path: Optional[str] = None):
        """保存用户配置"""
        if config_path is None:
            config_path = cls.PROJECT_ROOT / "user_config.json"
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户配置失败: {e}")
    
    @classmethod
    def get_chrome_launch_args(cls) -> list:
        """获取Chrome启动参数"""
        return [
            f'--remote-debugging-port={cls.CHROME_DEBUG_PORT}',
            f'--user-data-dir={cls.CHROME_USER_DATA_DIR}',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-dev-shm-usage'
        ]
    
    @classmethod
    def get_env_config(cls) -> Dict[str, Any]:
        """获取环境变量配置"""
        return {
            'chrome_debug_port': int(os.getenv('CHROME_DEBUG_PORT', cls.CHROME_DEBUG_PORT)),
            'chrome_user_data_dir': os.getenv('CHROME_USER_DATA_DIR', cls.CHROME_USER_DATA_DIR),
            'request_timeout': int(os.getenv('REQUEST_TIMEOUT', cls.REQUEST_TIMEOUT)),
            'max_retries': int(os.getenv('MAX_RETRIES', cls.MAX_RETRIES)),
            'log_level': os.getenv('LOG_LEVEL', cls.LOG_LEVEL),
            'data_dir': os.getenv('DATA_DIR', str(cls.DATA_DIR)),
            'cache_dir': os.getenv('CACHE_DIR', str(cls.CACHE_DIR)),
            'reports_dir': os.getenv('REPORTS_DIR', str(cls.REPORTS_DIR))
        }
    
    @classmethod
    def merge_config(cls, base_config: Dict[str, Any], 
                    user_config: Dict[str, Any], 
                    env_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置（优先级：env > user > base）"""
        merged = base_config.copy()
        merged.update(user_config)
        merged.update(env_config)
        return merged

# 全局配置实例
config = Config()

# 初始化目录
config.create_directories()

# 获取最终配置
def get_config() -> Dict[str, Any]:
    """获取合并后的配置"""
    base_config = {
        'chrome_debug_port': config.CHROME_DEBUG_PORT,
        'chrome_user_data_dir': config.CHROME_USER_DATA_DIR,
        'request_timeout': config.REQUEST_TIMEOUT,
        'max_retries': config.MAX_RETRIES,
        'log_level': config.LOG_LEVEL,
        'data_dir': str(config.DATA_DIR),
        'cache_dir': str(config.CACHE_DIR),
        'reports_dir': str(config.REPORTS_DIR),
        'cart_item_selectors': config.CART_ITEM_SELECTORS,
        'excel_columns': config.EXCEL_COLUMNS
    }
    
    user_config = config.load_user_config()
    env_config = config.get_env_config()
    
    return config.merge_config(base_config, user_config, env_config)

if __name__ == "__main__":
    # 测试配置
    print("项目配置信息:")
    print(f"项目根目录: {config.PROJECT_ROOT}")
    print(f"Chrome调试端口: {config.CHROME_DEBUG_PORT}")
    print(f"缓存目录: {config.CACHE_DIR}")
    print(f"数据目录: {config.DATA_DIR}")
    print(f"报告目录: {config.REPORTS_DIR}")
    print(f"日志目录: {config.LOGS_DIR}")
    
    # 测试目录创建
    config.create_directories()
    print("✅ 目录创建成功")
    
    # 测试配置合并
    final_config = get_config()
    print(f"✅ 配置合并完成，共 {len(final_config)} 项配置")