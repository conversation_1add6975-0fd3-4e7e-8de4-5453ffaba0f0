---
description: Web scraping patterns for 1688.com automation
---
# 1688.com Web Scraping Guidelines

## Browser Configuration
- Use Playwright for browser automation
- Configure proper user agents and headers
- Disable proxy settings for direct connection
- Disable SSL verification for network issues
- Set appropriate timeouts (30+ seconds)

## Image Handling Strategy
Due to 1688.com's anti-scraping mechanisms that prevent direct image downloads:

### Screenshot Approach (Recommended)
```python
# Use Playwright's screenshot functionality to capture images
async def capture_product_image_screenshot(self, item, product_name="商品"):
    img_selectors = ['img', '.product-img img', '.item-img img', '.goods-img img', '.image img']
    
    for selector in img_selectors:
        img_element = await item.query_selector(selector)
        if img_element and await img_element.is_visible():
            screenshot_bytes = await img_element.screenshot(type='png', timeout=10000)
            # Process screenshot with PIL
            img = PILImage.open(io.BytesIO(screenshot_bytes))
            img.thumbnail((100, 100), PILImage.Resampling.LANCZOS)
            return img_byte_arr
```

### Fallback Download Approach
```python
# Traditional download with enhanced headers (less reliable)
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
    'Referer': 'https://www.1688.com/',
    'Connection': 'keep-alive'
}
```

## CSS Selector Patterns
Use multiple fallback selectors for robustness:
```python
# Product name selectors
selectors = [
    '.product-name',
    '.item-title',
    '.product-title',
    'h3.product-name'
]

# Price selectors
price_selectors = [
    '.price',
    '.item-price',
    '.product-price'
]

# Image selectors (for screenshots)
image_selectors = [
    'img',
    '.product-img img',
    '.item-img img',
    '.goods-img img',
    '.image img'
]
```

## Data Extraction Strategy
1. **Product Information**: Extract name, specifications, quantity
2. **Pricing Data**: Extract unit price and calculate subtotals
3. **Image Capture**: Use screenshot method instead of URL extraction
4. **Supplier Info**: Extract manufacturer/supplier names
5. **Error Handling**: Implement fallbacks for missing data

## Network Optimization
- Add request headers to mimic real browser
- Implement retry mechanisms for failed requests
- Handle network timeouts gracefully
- Use session management for persistent connections
- Implement exponential backoff for retries

## Page Navigation
- Wait for page load completion
- Handle dynamic content loading
- Scroll to load lazy-loaded images
- Handle login requirements (manual intervention)
- Navigate through pagination if needed

## Error Handling Patterns
- Try multiple selectors for each data point
- Provide default values for missing data
- Log errors for debugging
- Continue processing even if some items fail
- Implement graceful degradation

## Anti-Scraping Countermeasures
- Use screenshot method for images instead of direct downloads
- Implement realistic browser behavior
- Add random delays between requests
- Rotate user agents if needed
- Handle CAPTCHA and verification challenges
