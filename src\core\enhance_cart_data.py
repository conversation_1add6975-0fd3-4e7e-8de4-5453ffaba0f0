#!/usr/bin/env python3
# -*- coding: utf-8 -*-"""
Enhanced cart data extraction with better supplier information
and link recovery
"""

import json
import re
import os
import argparse
from typing import Dict, Any, Optional
from dataclasses import dataclass
import requests
from bs4 import BeautifulSoup
from pathlib import Path

# 定义项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
import sys
sys.path.insert(0, str(PROJECT_ROOT))

@dataclass
class ProductInfo:
    """Product information structure"""
    offer_id: Optional[str] = None
    title: str = ""
    price: float = 0.0
    supplier: str = ""
    image_url: str = ""
    product_url: str = ""
    description: str = ""


class EnhancedDataExtractor:
    """Enhanced data extraction with better information recovery"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': (
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                'AppleWebKit/537.36 (KHTML, like Gecko) '
                'Chrome/91.0.4472.124 Safari/537.36'
            )
        })

    def extract_offer_id_from_url(self, url: str) -> Optional[str]:
        """Extract offer ID from product URL"""
        if not url:
            return None
        patterns = [
            r'offer/(\d+)\.html',
            r'offerId=(\d+)',
            r'id=(\d+)'
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None

    def extract_supplier_info(self, html_content: str) -> str:
        """Extract supplier information from HTML content"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # Try multiple selectors for supplier information
        supplier_selectors = [
            '.company-name',
            '.shop-name',
            '.supplier-name',
            '.store-name',
            '[class*="company"]',
            '[class*="shop"]',
            '[class*="supplier"]'
        ]

        for selector in supplier_selectors:
            elements = soup.select(selector)
            if elements:
                supplier = elements[0].get_text(strip=True)
                if supplier and len(supplier) > 2:
                    return supplier

        return "未知供应商"

    def extract_product_details(self, offer_id: str) -> Optional[ProductInfo]:
        """Extract detailed product information from 1688"""
        if not offer_id:
            return None

        url = f"https://detail.1688.com/offer/{offer_id}.html"

        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extract title
                title_selectors = [
                    '.d-title', '.title', 'h1', '[class*="title"]'
                ]
                title = ""
                for selector in title_selectors:
                    elements = soup.select(selector)
                    if elements:
                        title = elements[0].get_text(strip=True)
                        break

                # Extract price
                price_selectors = ['.price', '.value', '[class*="price"]']
                price = 0.0
                for selector in price_selectors:
                    elements = soup.select(selector)
                    if elements:
                        price_text = elements[0].get_text(strip=True)
                        price_match = re.search(r'(\d+\.?\d*)', price_text)
                        if price_match:
                            price = float(price_match.group(1))
                            break

                # Extract supplier
                supplier = self.extract_supplier_info(response.text)

                # Extract image
                image_selectors = [
                    'img[src*="jpg"]', 'img[src*="jpeg"]', 'img[src*="png"]'
                ]
                image_url = ""
                for selector in image_selectors:
                    elements = soup.select(selector)
                    if elements:
                        img_src = elements[0].get('src', '')
                        if (
                                img_src and
                                ('jpg' in img_src or 'jpeg' in img_src or
                                 'png' in img_src)):
                            image_url = img_src
                            break

                return ProductInfo(
                    offer_id=offer_id,
                    title=title,
                    price=price,
                    supplier=supplier,
                    image_url=image_url,
                    product_url=url
                )
        except Exception as e:
            print(f"Error extracting details for offer {offer_id}: {e}")

        return None

    def enhance_cart_data(self, cart_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance cart data with missing information"""
        enhanced_data = cart_data.copy()

        if "商品数据" in enhanced_data:
            enhanced_products = []

            for product in enhanced_data["商品数据"]:
                enhanced_product = product.copy()

                # Extract offer ID from existing link
                offer_id = None
                if "商品链接" in product and product["商品链接"]:
                    offer_id = self.extract_offer_id_from_url(product["商品链接"])

                # If we have offer ID, try to get more details
                if offer_id:
                    print(f"Enhancing product with offer ID: {offer_id}")
                    details = self.extract_product_details(offer_id)
                    if details:
                        # Enhance with extracted information
                        if (
                                not enhanced_product.get("供应商") or
                                enhanced_product["供应商"] == "未知供应商"):
                            enhanced_product["供应商"] = details.supplier

                        if not enhanced_product.get("图片URL"):
                            enhanced_product["图片URL"] = details.image_url

                        # Update price if current price is 0
                        if (
                                enhanced_product.get("单价", 0) == 0 and
                                details.price > 0):
                            enhanced_product["单价"] = details.price
                            enhanced_product["小计"] = (
                                details.price * enhanced_product.get("数量", 1)
                            )

                enhanced_products.append(enhanced_product)

            enhanced_data["商品数据"] = enhanced_products

        return enhanced_data

    def calculate_enhancement_stats(
        self,
        original_data: Dict[str, Any],
        enhanced_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate statistics about data enhancement"""
        stats = {
            "total_products": 0,
            "products_with_links": 0,
            "products_with_suppliers": 0,
            "products_with_images": 0,
            "products_with_prices": 0,
            "enhancement_rate": 0.0
        }

        if "商品数据" in enhanced_data:
            products = enhanced_data["商品数据"]
            stats["total_products"] = len(products)

            for product in products:
                if product.get("商品链接"):
                    stats["products_with_links"] += 1
                if (
                        product.get("供应商") and
                        product["供应商"] != "未知供应商"):
                    stats["products_with_suppliers"] += 1
                if product.get("图片URL"):
                    stats["products_with_images"] += 1
                if product.get("单价", 0) > 0:
                    stats["products_with_prices"] += 1

            if stats["total_products"] > 0:
                stats["enhancement_rate"] = (
                    (
                        stats["products_with_suppliers"] +
                        stats["products_with_images"]
                    ) /
                    (stats["total_products"] * 2)  # 2 metrics per product
                ) * 100

        return stats


def main():
    """Main function to enhance cart data"""
    parser = argparse.ArgumentParser(description='Enhance 1688 cart data with more details.')
    parser.add_argument('-i', '--input', required=True, help='Path to the input JSON file.')
    parser.add_argument('-o', '--output', help='Path to the output JSON file. Defaults to input file with "_enhanced" suffix.')
    
    args = parser.parse_args()

    input_path = Path(args.input)
    
    if not input_path.is_absolute():
        input_path = PROJECT_ROOT / input_path

    if not input_path.exists():
        print(f"Input file not found: {input_path}")
        return

    if args.output:
        output_path = Path(args.output)
        if not output_path.is_absolute():
            output_path = PROJECT_ROOT / output_path
    else:
        output_path = input_path.parent / f"{input_path.stem}_enhanced{input_path.suffix}"

    # Load cart data
    with open(input_path, 'r', encoding='utf-8') as f:
        cart_data = json.load(f)

    print(f"Enhancing cart data from: {input_path}")
    extractor = EnhancedDataExtractor()

    # Show original stats
    original_stats = extractor.calculate_enhancement_stats(cart_data, cart_data)
    print("Original data:")
    print(f"  Total products: {original_stats['total_products']}")
    print(f"  Products with suppliers: {original_stats['products_with_suppliers']}")
    print(f"  Products with images: {original_stats['products_with_images']}")

    # Enhance data
    enhanced_data = extractor.enhance_cart_data(cart_data)

    # Show enhanced stats
    enhanced_stats = extractor.calculate_enhancement_stats(cart_data, enhanced_data)
    print("\nEnhanced data:")
    print(f"  Products with suppliers: {enhanced_stats['products_with_suppliers']}")
    print(f"  Products with images: {enhanced_stats['products_with_images']}")
    print(f"  Enhancement rate: {enhanced_stats['enhancement_rate']:.1f}%")

    # Save enhanced data
    os.makedirs(output_path.parent, exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(enhanced_data, f, ensure_ascii=False, indent=2)

    print(f"\nEnhanced data saved to: {output_path}")


if __name__ == "__main__":
    main()