"""
测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        
        # 测试核心模块
        from core.config import Config
        from core.error_handler import ErrorHandler
        from core.chrome_manager import ChromeManager
        from core.data_processor import DataProcessor
        
        print("OK 核心模块导入成功")
        
        # 测试UI模块
        from ui.main_window import MainWindow
        from ui.function_selection import FunctionSelectionWindow
        from ui.step_manager import StepManager, BaseStep
        
        print("OK UI模块导入成功")
        
        # 测试工具模块
        from utils.file_utils import FileUtils
        from utils.logger import setup_logger
        from utils.helpers import format_duration, format_file_size
        
        print("OK 工具模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"ERROR 导入失败：{e}")
        return False

def test_config():
    """测试配置"""
    try:
        print("\n测试配置...")
        
        from core.config import Config
        
        config = Config()
        print(f"OK 配置加载成功")
        print(f"   - 应用名称: {config.get('app_name')}")
        print(f"   - Chrome端口: {config.chrome_port}")
        print(f"   - 窗口大小: {config.window_width}x{config.window_height}")
        
        return True
        
    except Exception as e:
        print(f"ERROR 配置测试失败：{e}")
        return False

def test_error_handler():
    """测试错误处理器"""
    try:
        print("\n测试错误处理器...")
        
        from core.error_handler import ErrorHandler
        
        handler = ErrorHandler()
        error_message = handler.handle_error("FILE_NOT_FOUND", "测试文件不存在")
        
        print("OK 错误处理器测试成功")
        print(f"   - 错误消息长度: {len(error_message)} 字符")
        
        return True
        
    except Exception as e:
        print(f"ERROR 错误处理器测试失败：{e}")
        return False

def test_file_utils():
    """测试文件工具"""
    try:
        print("\n测试文件工具...")
        
        from utils.file_utils import FileUtils
        
        # 测试文件信息获取
        test_file = __file__
        file_info = FileUtils.get_file_info(test_file)
        
        print("OK 文件工具测试成功")
        print(f"   - 文件名: {file_info['name']}")
        print(f"   - 文件大小: {file_info['size']} 字节")
        print(f"   - 文件存在: {file_info['exists']}")
        
        return True
        
    except Exception as e:
        print(f"ERROR 文件工具测试失败：{e}")
        return False

def test_helpers():
    """测试辅助函数"""
    try:
        print("\n测试辅助函数...")
        
        from utils.helpers import format_duration, format_file_size, calculate_progress
        
        # 测试格式化函数
        duration_str = format_duration(3665)
        size_str = format_file_size(1024 * 1024 * 2.5)
        progress = calculate_progress(75, 100)
        
        print("OK 辅助函数测试成功")
        print(f"   - 持续时间格式化: {duration_str}")
        print(f"   - 文件大小格式化: {size_str}")
        print(f"   - 进度计算: {progress}%")
        
        return True
        
    except Exception as e:
        print(f"ERROR 辅助函数测试失败：{e}")
        return False

def test_step_manager():
    """测试步骤管理器"""
    try:
        print("\n测试步骤管理器...")
        
        from ui.step_manager import StepManager, BaseStep
        
        # 创建测试步骤
        class TestStep(BaseStep):
            def __init__(self, name):
                super().__init__(name, f"测试步骤{name}")
            
            def execute(self, progress_callback):
                progress_callback(100, "测试完成", "测试结果")
                self.is_completed = True
        
        # 创建步骤管理器
        manager = StepManager()
        manager.add_step(TestStep("步骤1"))
        manager.add_step(TestStep("步骤2"))
        
        print("OK 步骤管理器测试成功")
        print(f"   - 步骤总数: {manager.get_step_count()}")
        print(f"   - 当前步骤: {manager.get_current_step_index() + 1}")
        
        return True
        
    except Exception as e:
        print(f"ERROR 步骤管理器测试失败：{e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("1688采购车数据处理工具 - 测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_error_handler,
        test_file_utils,
        test_helpers,
        test_step_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("SUCCESS 所有测试通过！应用程序可以正常运行。")
    else:
        print("WARNING 部分测试失败，请检查相关模块。")
    
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)