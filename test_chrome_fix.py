#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome连接修复效果
验证Playwright导入和Chrome连接代码修复
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_playwright_import():
    """测试Playwright导入"""
    print("测试Playwright导入...")
    
    try:
        from playwright.async_api import async_playwright
        print("OK async_playwright导入成功")
        
        # 测试async_playwright函数
        async def test_async_playwright():
            async with async_playwright() as p:
                print("OK async_playwright上下文管理器工作正常")
                return True
        
        # 运行测试
        result = asyncio.run(test_async_playwright())
        return result
        
    except Exception as e:
        print(f"ERROR Playwright导入失败: {e}")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    
    try:
        from src.gui.qt_real import RealFunctionApp, BrowserPrepWorker, RealWorker
        print("OK GUI模块导入成功")
        return True
    except Exception as e:
        print(f"ERROR GUI模块导入失败: {e}")
        return False

def test_chrome_manager_import():
    """测试Chrome管理器导入"""
    print("\n测试Chrome管理器导入...")
    
    try:
        sys.path.append(str(PROJECT_ROOT / "src" / "gui" / "core"))
        from chrome_manager import ChromeManager
        print("OK Chrome管理器导入成功")
        return True
    except Exception as e:
        print(f"ERROR Chrome管理器导入失败: {e}")
        return False

def test_core_modules():
    """测试核心模块导入"""
    print("\n测试核心模块导入...")
    
    try:
        from src.core.enrich_orders_with_images import OrderEnricher
        from src.core.export_cart_excel import ExportExcelAutomation
        from src.core.extract_orders import OrderDataExtractor
        print("OK 核心模块导入成功")
        return True
    except Exception as e:
        print(f"ERROR 核心模块导入失败: {e}")
        return False

def test_syntax_check():
    """检查修复后的代码语法"""
    print("\n检查修复后的代码语法...")
    
    try:
        # 读取修复后的文件
        gui_file = PROJECT_ROOT / "src" / "gui" / "qt_real.py"
        with open(gui_file, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 编译检查语法
        compile(code, str(gui_file), 'exec')
        print("OK GUI代码语法检查通过")
        return True
    except SyntaxError as e:
        print(f"ERROR GUI代码语法错误: {e}")
        return False
    except Exception as e:
        print(f"ERROR GUI代码检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Chrome连接修复效果测试")
    print("=" * 50)
    
    tests = [
        ("Playwright导入", test_playwright_import),
        ("GUI模块导入", test_gui_import),
        ("Chrome管理器导入", test_chrome_manager_import),
        ("核心模块导入", test_core_modules),
        ("代码语法检查", test_syntax_check)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "OK 通过" if result else "ERROR 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("SUCCESS 所有测试通过！Chrome连接修复成功！")
        return True
    else:
        print("WARNING 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)