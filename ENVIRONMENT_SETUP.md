# 1688自动化项目 - Miniconda环境设置

## 环境概述

已为1688自动化项目创建了独立的Miniconda环境 `1688_automation`，包含所有必需的依赖。

## 已安装的依赖

### GUI框架
- **PyQt6 6.7.1** - 主要GUI框架
- **tkinter** - 备用GUI框架（Python标准库）

### 数据处理
- **pandas 2.3.1** - 数据处理和分析
- **openpyxl 3.1.5** - Excel文件操作

### 网络和图像
- **requests 2.32.4** - HTTP请求
- **Pillow 11.1.0** - 图像处理

### 浏览器自动化
- **playwright 1.54.0** - 浏览器自动化
- **Chromium** - Playwright浏览器

### 其他工具
- **asyncio** - 异步编程（Python标准库）
- **difflib** - 字符串匹配（Python标准库）

## 使用方法

### Windows系统

1. **使用启动脚本（推荐）**：
   ```batch
  双击运行 start_env.bat
   ```

2. **手动激活环境**：
   ```batch
   E:\miniconda\Scripts\activate.bat 1688_automation
   ```

### Linux/Mac系统

1. **使用启动脚本**：
   ```bash
   source start_env.sh
   ```

2. **手动激活环境**：
   ```bash
   conda activate 1688_automation
   ```

## 测试环境

激活环境后，可以运行以下命令测试：

```bash
# 测试GUI模式
python main_app.py

# 测试CLI模式
python export_cart_excel.py

# 测试PyQt6 GUI
python ui_app/qt_real.py

# 测试Chrome连接
python test_chrome_port.py
```

## 环境信息

- **环境名称**: `1688_automation`
- **Python版本**: 3.10.18
- **conda版本**: 25.5.1
- **环境路径**: `E:\conda\envs\1688_automation`

## 项目文件说明

### 主要程序
- `main_app.py` - 主程序入口（自动检测GUI/CLI）
- `export_cart_excel.py` - CLI版本采购车数据导出
- `ui_app/qt_real.py` - PyQt6 GUI版本
- `ui_app/main.py` - tkinter GUI版本
- `test_chrome_port.py` - Chrome调试端口测试

### 支持文件
- `start_debug_chrome.bat` - Chrome调试模式启动脚本
- `start_env.bat` - 环境启动脚本（Windows）
- `start_env.sh` - 环境启动脚本（Linux/Mac）

## 故障排除

### 1. 环境激活失败
```bash
# 重新初始化conda
conda init --all
# 重新打开终端
```

### 2. PyQt6导入失败
```bash
# 重新安装PyQt6
conda install -n 1688_automation pyqt=6.7.1 --force-reinstall
```

### 3. Playwright问题
```bash
# 重新安装playwright浏览器
conda run -n 1688_automation playwright install chromium
```

### 4. 包依赖问题
```bash
# 更新所有包
conda update -n 1688_automation --all
```

## 开发建议

1. **始终使用独立环境**：确保在 `1688_automation` 环境中工作
2. **GUI模式优先**：推荐使用PyQt6 GUI版本，功能更完整
3. **Chrome调试模式**：运行前确保启动Chrome调试模式
4. **网络连接**：确保能访问1688网站和相关API

## 性能优化

- 环境已包含优化的MKL数学库
- PyQt6使用硬件加速渲染
- Playwright使用高效的Chromium浏览器

## 安全考虑

- 独立环境避免包冲突
- 定期更新依赖包
- 使用Chrome调试模式避免账号安全风险