#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件内容显示修复效果
验证点击超链接时不会在程序中显示文件内容
"""

import sys
import os
import time
import json
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def create_test_files():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    # 创建目录
    reports_dir = PROJECT_ROOT / "reports"
    data_dir = PROJECT_ROOT / "data"
    reports_dir.mkdir(exist_ok=True)
    data_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 创建Excel文件
    import pandas as pd
    excel_file = reports_dir / f"test_excel_{timestamp}.xlsx"
    df = pd.DataFrame({
        '商品名称': ['测试商品1', '测试商品2'],
        '价格': [99.99, 199.99],
        '数量': [1, 2]
    })
    df.to_excel(str(excel_file), index=False)
    print(f"✅ 创建Excel文件: {excel_file}")
    
    # 创建JSON文件
    json_file = data_dir / f"test_json_{timestamp}.json"
    json_data = {
        "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
        "测试数据": [
            {"商品": "测试商品1", "价格": 99.99},
            {"商品": "测试商品2", "价格": 199.99}
        ],
        "说明": "这是一个测试JSON文件，点击链接时不应该在程序中显示这些内容"
    }
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    print(f"✅ 创建JSON文件: {json_file}")
    
    # 创建文本文件
    txt_file = data_dir / f"test_text_{timestamp}.txt"
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("这是一个测试文本文件\n")
        f.write("包含多行内容\n")
        f.write("点击链接时不应该在程序中显示这些内容\n")
        f.write("应该用默认程序打开\n")
    print(f"✅ 创建文本文件: {txt_file}")
    
    return [excel_file, json_file, txt_file]

def test_safe_textbrowser():
    """测试SafeTextBrowser功能"""
    print("\n🔒 测试SafeTextBrowser功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import SafeTextBrowser
        from PyQt6.QtCore import QUrl, QVariant
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建SafeTextBrowser实例
        browser = SafeTextBrowser()
        
        # 测试loadResource方法
        test_urls = [
            QUrl("file:///D:/test.xlsx"),
            QUrl("file:///D:/test.json"),
            QUrl("http://example.com/image.png"),
            QUrl("https://example.com/style.css")
        ]
        
        for url in test_urls:
            result = browser.loadResource(0, url)  # 0 = QTextDocument.UnknownResource
            if url.scheme() == 'file':
                if isinstance(result, QVariant) and result.isNull():
                    print(f"✅ {url.toString()}: 正确阻止加载")
                else:
                    print(f"❌ {url.toString()}: 未能阻止加载")
            else:
                print(f"ℹ️ {url.toString()}: 非文件协议，允许加载")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_with_files():
    """测试GUI中的文件链接"""
    print("\n🖥️ 启动GUI测试文件链接...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 创建测试文件
        test_files = create_test_files()
        
        # 添加包含文件路径的日志消息
        window.add_log("测试文件已创建，请点击下面的链接:", "info")
        for file_path in test_files:
            if file_path.suffix == '.xlsx':
                window.add_log(f"Excel文件: {file_path}", "success")
            elif file_path.suffix == '.json':
                window.add_log(f"JSON文件: {file_path}", "success")
            else:
                window.add_log(f"文本文件: {file_path}", "success")
        
        window.add_log("", "info")
        window.add_log("🎯 测试说明:", "info")
        window.add_log("1. 点击上面的文件链接", "info")
        window.add_log("2. 文件应该用默认程序打开", "info")
        window.add_log("3. 程序日志中不应该显示文件内容", "info")
        window.add_log("4. 如果看到乱码或文件内容，说明修复失败", "warning")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI已启动")
        print("💡 请在GUI中:")
        print("   1. 点击各种文件类型的链接")
        print("   2. 观察程序日志是否保持干净")
        print("   3. 确认文件用默认程序打开")
        print("   4. 关闭GUI窗口完成测试")
        
        # 运行GUI事件循环
        app.exec()
        
        # 清理测试文件
        for file_path in test_files:
            try:
                if file_path.exists():
                    file_path.unlink()
                    print(f"🧹 清理: {file_path}")
            except:
                print(f"⚠️ 无法删除: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("文件内容显示修复测试")
    print("=" * 40)
    
    print("🎯 修复说明:")
    print("1. 创建SafeTextBrowser类")
    print("2. 重写loadResource方法")
    print("3. 阻止file://协议的资源加载")
    print("4. 保持超链接点击功能正常")
    
    # 1. 测试SafeTextBrowser功能
    safe_ok = test_safe_textbrowser()
    
    # 2. 询问是否启动GUI测试
    if safe_ok:
        choice = input("\n是否启动GUI测试? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            test_gui_with_files()
        else:
            print("跳过GUI测试")
    
    print(f"\n🎉 修复效果:")
    print("- 超链接仍然可以点击")
    print("- 文件用默认程序打开")
    print("- 程序日志保持干净")
    print("- 不会显示文件内容或乱码")

if __name__ == "__main__":
    main()
