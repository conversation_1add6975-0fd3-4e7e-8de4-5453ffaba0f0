#!/usr/bin/env python3
"""
调试当前页面的Shadow DOM结构
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright
import requests

async def debug_current_page():
    """调试当前页面"""
    
    print("🔍 调试当前页面的Shadow DOM结构")
    print("=" * 50)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print(f"❌ Chrome调试接口不可用")
            return
        
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return
    
    # 连接到Chrome
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")
            
            # 获取所有页面
            all_pages = []
            for context in browser.contexts:
                for page in context.pages:
                    all_pages.append(page)
            
            print(f"\n📄 找到 {len(all_pages)} 个页面:")
            for i, page in enumerate(all_pages):
                url = page.url
                title = await page.title()
                print(f"   页面 {i+1}: {title}")
                print(f"           URL: {url}")
                
                # 如果是1688相关页面，进行详细分析
                if '1688.com' in url:
                    print(f"   🎯 分析1688页面...")
                    await analyze_page(page)
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def analyze_page(page):
    """分析页面结构"""
    try:
        # 基本页面信息
        url = page.url
        title = await page.title()
        
        print(f"      📍 URL: {url}")
        print(f"      📄 标题: {title}")
        
        # 检查是否是订单页面
        is_order_page = any(keyword in url for keyword in ['order', 'trade', 'buyer'])
        print(f"      📦 是否为订单页面: {'是' if is_order_page else '否'}")
        
        # 检查页面内容
        page_text = await page.evaluate("document.body.textContent")
        has_order_text = "订单" in page_text
        print(f"      📝 页面包含'订单'文字: {'是' if has_order_text else '否'}")
        
        # 检查关键容器
        containers = await page.evaluate("""
            () => {
                const results = {};
                
                // 检查订单容器
                const orderContainer = document.querySelector('.order-list-container');
                results.orderContainer = !!orderContainer;
                if (orderContainer) {
                    results.orderContainerText = orderContainer.textContent.trim().substring(0, 100);
                }
                
                // 检查翻页容器
                const pageContainer = document.querySelector('.page#page');
                results.pageContainer = !!pageContainer;
                if (pageContainer) {
                    results.pageContainerText = pageContainer.textContent.trim();
                }
                
                return results;
            }
        """)
        
        print(f"      📦 订单容器(.order-list-container): {'找到' if containers.get('orderContainer') else '未找到'}")
        if containers.get('orderContainerText'):
            print(f"         内容预览: {containers['orderContainerText']}...")
        
        print(f"      📄 翻页容器(.page#page): {'找到' if containers.get('pageContainer') else '未找到'}")
        if containers.get('pageContainerText'):
            print(f"         内容: {containers['pageContainerText']}")
        
        # 检查Shadow DOM
        shadow_info = await page.evaluate("""
            () => {
                function findShadowHosts(root = document) {
                    const hosts = [];
                    const allElements = root.querySelectorAll('*');
                    
                    for (const el of allElements) {
                        if (el.shadowRoot) {
                            hosts.push({
                                tagName: el.tagName,
                                className: el.className,
                                id: el.id,
                                mode: el.shadowRoot.mode
                            });
                            
                            // 递归查找嵌套的Shadow DOM
                            const nestedHosts = findShadowHosts(el.shadowRoot);
                            hosts.push(...nestedHosts);
                        }
                    }
                    
                    return hosts;
                }
                
                return findShadowHosts();
            }
        """)
        
        print(f"      🌟 Shadow DOM宿主: {len(shadow_info)} 个")
        if shadow_info:
            for i, host in enumerate(shadow_info[:3]):  # 只显示前3个
                print(f"         {i+1}. {host['tagName']}.{host['className']} #{host['id']}")
        
        # 如果有Shadow DOM，尝试查找订单项
        if shadow_info:
            order_items = await page.evaluate("""
                () => {
                    function querySelectorDeep(selector, root = document) {
                        const elements = [];
                        
                        // 在当前层级查找
                        const found = root.querySelectorAll(selector);
                        elements.push(...found);
                        
                        // 递归查找所有 Shadow DOM
                        const allElements = root.querySelectorAll('*');
                        for (const el of allElements) {
                            if (el.shadowRoot) {
                                const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                                elements.push(...shadowElements);
                            }
                        }
                        
                        return elements;
                    }
                    
                    const selectors = ['order-item', 'order-item[data-tracker]', '[data-tracker*="order"]'];
                    const results = {};
                    
                    for (const selector of selectors) {
                        const elements = querySelectorDeep(selector);
                        results[selector] = {
                            count: elements.length,
                            sample: elements.length > 0 ? {
                                tagName: elements[0].tagName,
                                dataTracker: elements[0].getAttribute('data-tracker'),
                                textLength: elements[0].textContent.trim().length
                            } : null
                        };
                    }
                    
                    return results;
                }
            """)
            
            print(f"      🎯 Shadow DOM中的订单项:")
            for selector, data in order_items.items():
                print(f"         {selector}: {data['count']} 个")
                if data['sample']:
                    sample = data['sample']
                    tracker = sample['dataTracker'][:30] + '...' if sample['dataTracker'] else 'None'
                    print(f"            样本: {sample['tagName']} (tracker: {tracker}, 文本: {sample['textLength']}字符)")
        
        print()
        
    except Exception as e:
        print(f"      ❌ 分析页面失败: {e}")

if __name__ == "__main__":
    asyncio.run(debug_current_page())
