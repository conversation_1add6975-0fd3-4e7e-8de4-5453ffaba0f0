#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试商品SKU提取功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.extract_orders import OrderDataExtractor

async def test_simple_sku():
    """简单测试商品SKU提取"""
    print("🧪 开始简单测试商品SKU提取功能...")
    
    # 创建提取器实例
    extractor = OrderDataExtractor()
    
    # 模拟多个商品SKU的测试数据（同一订单多个商品）
    test_data = [
        # 订单1的商品1
        {
            'order_id': '*****************',
            'title': '女装连衣裙 (红色, L码)',
            'product_specs': '红色, L码',
            'price': 89.90,
            'quantity': 1,
            'total_amount': 89.90,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'order_time': '2024-01-15 10:30:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/dress_red_l.jpg',
            'detail_url': 'https://example.com/product1'
        },
        # 订单1的商品2
        {
            'order_id': '*****************',
            'title': '女装连衣裙 (蓝色, M码)',
            'product_specs': '蓝色, M码',
            'price': 89.90,
            'quantity': 2,
            'total_amount': 179.80,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'order_time': '2024-01-15 10:30:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/dress_blue_m.jpg',
            'detail_url': 'https://example.com/product1'
        },
        # 订单1的商品3
        {
            'order_id': '*****************',
            'title': '女装连衣裙 (黑色, S码)',
            'product_specs': '黑色, S码',
            'price': 89.90,
            'quantity': 1,
            'total_amount': 89.90,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'order_time': '2024-01-15 10:30:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/dress_black_s.jpg',
            'detail_url': 'https://example.com/product1'
        },
        # 订单2的商品1
        {
            'order_id': '*****************',
            'title': '男士T恤 (白色)',
            'product_specs': '白色',
            'price': 45.00,
            'quantity': 3,
            'total_amount': 135.00,
            'shop_name': '另一家供应商',
            'order_time': '2024-01-16 14:20:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/tshirt_white.jpg',
            'detail_url': 'https://example.com/product2'
        },
        # 订单2的商品2
        {
            'order_id': '*****************',
            'title': '男士T恤 (灰色)',
            'product_specs': '灰色',
            'price': 45.00,
            'quantity': 2,
            'total_amount': 90.00,
            'shop_name': '另一家供应商',
            'order_time': '2024-01-16 14:20:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/tshirt_gray.jpg',
            'detail_url': 'https://example.com/product2'
        }
    ]
    
    print(f"📊 测试数据: {len(test_data)} 个商品SKU")
    
    # 测试保存到Excel
    output_path = PROJECT_ROOT / "reports" / "test_sku_simple.xlsx"
    await extractor.save_to_excel(test_data, str(output_path))
    
    print("✅ 测试完成!")
    print(f"📄 测试结果已保存到: {output_path}")
    
    # 验证数据结构
    print("\n🔍 数据结构验证:")
    print("=" * 60)
    
    # 按订单分组显示
    orders = {}
    for item in test_data:
        order_id = item['order_id']
        if order_id not in orders:
            orders[order_id] = []
        orders[order_id].append(item)
    
    for order_id, products in orders.items():
        print(f"\n📦 订单号: {order_id}")
        print(f"   卖家: {products[0]['shop_name']}")
        print(f"   下单时间: {products[0]['order_time']}")
        print(f"   商品数量: {len(products)} 个SKU")
        
        for i, product in enumerate(products):
            print(f"   商品 {i+1}:")
            print(f"     名称: {product['title']}")
            print(f"     规格: {product['product_specs']}")
            print(f"     单价: ￥{product['price']}")
            print(f"     数量: {product['quantity']}")
            print(f"     小计: ￥{product['total_amount']}")
    
    # 统计信息
    print("\n📈 统计信息:")
    print("=" * 60)
    unique_orders = len(orders)
    unique_suppliers = len(set(item['shop_name'] for item in test_data))
    total_amount = sum(item['total_amount'] for item in test_data)
    total_quantity = sum(item['quantity'] for item in test_data)
    
    print(f"  商品SKU总数: {len(test_data)}")
    print(f"  订单数量: {unique_orders}")
    print(f"  供应商数量: {unique_suppliers}")
    print(f"  商品总数量: {total_quantity}")
    print(f"  总金额: ￥{total_amount:.2f}")
    
    print("\n💡 数据特点:")
    print("  ✅ 每个商品SKU都有独立记录")
    print("  ✅ 同一订单的不同规格商品分别记录")
    print("  ✅ 订单信息（卖家、时间等）复制到每个商品记录")
    print("  ✅ 支持商品规格信息（颜色、尺寸等）")

if __name__ == "__main__":
    asyncio.run(test_simple_sku())
