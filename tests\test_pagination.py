"""
测试翻页功能
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from playwright.async_api import async_playwright
from src.core.extract_orders import OrderDataExtractor


@pytest.mark.asyncio
async def test_pagination_debug():
    """测试翻页调试功能"""
    extractor = OrderDataExtractor()
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 导航到一个有翻页的测试页面
            print("🔍 测试翻页调试功能...")
            
            # 这里可以用任何有翻页的网站进行测试
            # 或者创建一个模拟的HTML页面
            test_html = """
            <!DOCTYPE html>
            <html>
            <head><title>Test Pagination</title></head>
            <body>
                <div class="page" id="page">
                    <lu-pagination mode total="146" per="10" current="1"></lu-pagination>
                    <button class="ui-page" data-current="1" aria-label="第1页">1</button>
                    <button class="ui-page" data-current="2" aria-label="第2页">2</button>
                    <button class="ui-page ui-page-next" id="right" aria-label="下一页">下一页</button>
                </div>
            </body>
            </html>
            """
            
            await page.set_content(test_html)
            await page.wait_for_timeout(1000)
            
            # 测试调试功能
            await extractor.debug_pagination_container(page)
            
            print("✅ 翻页调试测试完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            raise
        finally:
            await browser.close()


@pytest.mark.asyncio
async def test_next_page_functionality():
    """测试下一页功能"""
    extractor = OrderDataExtractor()

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        try:
            print("🔍 测试下一页功能...")

            # 创建一个模拟的HTML页面，包含可点击的下一页按钮
            test_html = """
            <!DOCTYPE html>
            <html>
            <head><title>Test Pagination</title></head>
            <body>
                <div class="page" id="page">
                    <lu-pagination mode total="146" per="10" current="1"></lu-pagination>
                    <button class="ui-page" data-current="1" aria-label="第1页">1</button>
                    <button class="ui-page" data-current="2" aria-label="第2页">2</button>
                    <button class="ui-page ui-page-next" id="right" aria-label="下一页" onclick="goToNextPage()">下一页</button>
                </div>
                <div id="current-page-info">当前页: 1</div>

                <script>
                function goToNextPage() {
                    // 模拟翻页逻辑
                    const currentPageInfo = document.getElementById('current-page-info');
                    const luPagination = document.querySelector('lu-pagination');
                    const currentPage = parseInt(luPagination.getAttribute('current'));
                    const nextPage = currentPage + 1;

                    // 更新页面信息
                    luPagination.setAttribute('current', nextPage.toString());
                    currentPageInfo.textContent = '当前页: ' + nextPage;

                    // 如果到达最后一页，禁用下一页按钮
                    if (nextPage >= 15) { // 假设总共15页 (146/10 = 14.6)
                        document.getElementById('right').disabled = true;
                    }
                }
                </script>
            </body>
            </html>
            """

            await page.set_content(test_html)
            await page.wait_for_timeout(1000)

            # 测试翻页功能
            result = await extractor.go_to_next_page(page)

            if result:
                print("✅ 翻页成功")

                # 验证页面是否真的翻页了
                current_page_text = await page.locator('#current-page-info').text_content()
                print(f"📄 页面信息: {current_page_text}")

                # 检查lu-pagination的current属性
                current_attr = await page.locator('lu-pagination').get_attribute('current')
                print(f"📊 当前页码: {current_attr}")

                if current_attr == "2":
                    print("✅ 翻页验证成功：页码已更新到第2页")
                else:
                    print(f"⚠️ 翻页验证失败：期望页码2，实际页码{current_attr}")
            else:
                print("❌ 翻页失败")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            raise
        finally:
            await browser.close()


@pytest.mark.asyncio
async def test_price_extraction():
    """测试单价提取功能"""
    extractor = OrderDataExtractor()

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        try:
            print("🔍 测试单价提取功能...")

            # 创建一个模拟的HTML页面，包含实际的单价结构
            test_html = """
            <!DOCTYPE html>
            <html>
            <head><title>Test Price Extraction</title></head>
            <body>
                <div class="order-item">
                    <div class="product-info">
                        <div class="actual-unit-price">¥ 68.00</div>
                        <div class="product-title">测试商品1</div>
                    </div>
                </div>
                <div class="order-item">
                    <div class="product-info">
                        <div class="actual-unit-price">¥ 81.50</div>
                        <div class="product-title">测试商品2</div>
                    </div>
                </div>
            </body>
            </html>
            """

            await page.set_content(test_html)
            await page.wait_for_timeout(1000)

            # 测试价格提取
            prices = await page.evaluate("""
                () => {
                    const priceElements = document.querySelectorAll('.actual-unit-price');
                    return Array.from(priceElements).map(el => el.textContent.trim());
                }
            """)

            print(f"📊 找到的价格: {prices}")

            for i, price_text in enumerate(prices):
                print(f"   商品{i+1}: {price_text}")

                # 提取数字
                import re
                price_match = re.search(r'[\d.]+', price_text)
                if price_match:
                    price_value = float(price_match.group())
                    print(f"   提取的数值: {price_value}")
                else:
                    print(f"   ⚠️ 无法提取数值")

            print("✅ 单价提取测试完成")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            raise
        finally:
            await browser.close()


@pytest.mark.asyncio
async def test_seller_extraction():
    """测试卖家名称提取功能"""
    extractor = OrderDataExtractor()

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        try:
            print("🔍 测试卖家名称提取功能...")

            # 创建一个模拟的HTML页面，包含实际的卖家信息结构
            test_html = """
            <!DOCTYPE html>
            <html>
            <head><title>Test Seller Extraction</title></head>
            <body>
                <div class="order-container">
                    <div class="order-header">
                        <div class="company-name">测试供应商有限公司</div>
                        <div class="order-info">订单信息</div>
                    </div>
                    <div class="product-list">
                        <div class="product-item">
                            <div class="actual-unit-price">¥ 68.00</div>
                            <div class="product-title">商品1</div>
                        </div>
                        <div class="product-item">
                            <div class="actual-unit-price">¥ 81.50</div>
                            <div class="product-title">商品2</div>
                        </div>
                    </div>
                </div>

                <div class="order-container">
                    <div class="order-header">
                        <a href="/company/12345" class="seller-name">另一个供应商</a>
                        <div class="order-info">订单信息</div>
                    </div>
                    <div class="product-list">
                        <div class="product-item">
                            <div class="actual-unit-price">¥ 25.00</div>
                            <div class="product-title">商品3</div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """

            await page.set_content(test_html)
            await page.wait_for_timeout(1000)

            # 测试卖家名称提取
            sellers = await page.evaluate("""
                () => {
                    const sellerElements = document.querySelectorAll('.company-name, .seller-name');
                    return Array.from(sellerElements).map(el => ({
                        text: el.textContent.trim(),
                        className: el.className,
                        tagName: el.tagName
                    }));
                }
            """)

            print(f"📊 找到的卖家信息: {sellers}")

            for i, seller_info in enumerate(sellers):
                print(f"   卖家{i+1}: {seller_info['text']} (类名: {seller_info['className']}, 标签: {seller_info['tagName']})")

            print("✅ 卖家名称提取测试完成")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            raise
        finally:
            await browser.close()


if __name__ == "__main__":
    asyncio.run(test_pagination_debug())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_next_page_functionality())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_price_extraction())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_seller_extraction())
