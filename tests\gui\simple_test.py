"""
简化命令行测试程序
"""

import sys
import os
from pathlib import Path
import time

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_core_modules():
    """测试核心模块"""
    print("1688采购车数据处理工具 - 核心模块测试")
    print("=" * 50)
    
    try:
        # 测试配置模块
        from core.config import Config
        config = Config()
        print(f"OK 配置模块: {config.get('app_name')}")
        
        # 测试错误处理模块
        from core.error_handler import ErrorHandler
        handler = ErrorHandler()
        error_msg = handler.handle_error("FILE_NOT_FOUND", "测试")
        print(f"OK 错误处理模块: 错误消息长度 {len(error_msg)}")
        
        # 测试文件工具模块
        from utils.file_utils import FileUtils
        file_info = FileUtils.get_file_info(__file__)
        print(f"OK 文件工具模块: 处理文件 {file_info['name']}")
        
        # 测试辅助函数模块
        from utils.helpers import format_duration, format_file_size
        duration = format_duration(3665)
        size = format_file_size(1024 * 1024)
        print(f"OK 辅助函数模块: {duration}, {size}")
        
        # 测试Chrome管理器模块
        from core.chrome_manager import ChromeManager
        chrome_manager = ChromeManager()
        chrome_path = chrome_manager.find_chrome_path()
        print(f"OK Chrome管理器模块: {chrome_path or '未找到Chrome'}")
        
        # 测试数据处理器模块
        from core.data_processor import DataProcessor
        processor = DataProcessor()
        print("OK 数据处理器模块: 初始化成功")
        
        print("\nSUCCESS 所有核心模块测试通过!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\nERROR 测试失败: {e}")
        print("=" * 50)
        return False

def show_project_info():
    """显示项目信息"""
    print("\n项目信息:")
    print("-" * 30)
    print(f"项目路径: {Path(__file__).parent}")
    print(f"Python版本: {sys.version}")
    
    # 检查主要文件
    main_files = [
        "main.py",
        "demo.py", 
        "config.json",
        "requirements.txt",
        "README.md"
    ]
    
    print("\n主要文件:")
    for file in main_files:
        file_path = Path(__file__).parent / file
        status = "存在" if file_path.exists() else "不存在"
        print(f"  {file}: {status}")
    
    # 检查目录结构
    directories = ["core", "ui", "utils", "data"]
    print("\n目录结构:")
    for directory in directories:
        dir_path = Path(__file__).parent / directory
        status = "存在" if dir_path.exists() else "不存在"
        print(f"  {directory}/: {status}")

def main():
    """主函数"""
    print("1688采购车数据处理工具")
    print("命令行版本测试程序")
    print("=" * 50)
    
    # 显示项目信息
    show_project_info()
    
    # 测试核心模块
    if test_core_modules():
        print("\n使用说明:")
        print("1. GUI版本: python main.py (需要tkinter)")
        print("2. 演示版本: python demo.py (需要tkinter)")
        print("3. 核心测试: python test_core.py")
        print("4. 命令行测试: python test_cli.py")
        print("\n注意: 完整的GUI功能需要安装tkinter模块")
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())