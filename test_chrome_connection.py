#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome连接功能的修复效果
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

async def test_chrome_connection():
    """测试Chrome连接功能"""
    print("开始测试Chrome连接功能...")
    
    try:
        from playwright.async_api import async_playwright
        
        debug_port = 9222
        max_retries = 5
        initial_delay = 2
        max_delay = 10
        
        print(f"尝试连接到Chrome调试端口 {debug_port}")
        
        for attempt in range(max_retries):
            try:
                print(f"连接尝试 {attempt + 1}/{max_retries}...")
                
                async with async_playwright() as playwright:
                    browser = await playwright.chromium.connect_over_cdp(
                        f"http://localhost:{debug_port}", 
                        timeout=5000
                    )
                    
                    print("成功连接到Chrome调试实例!")
                    
                    # 检查页面
                    if browser.contexts and browser.contexts[0].pages:
                        print(f"找到 {len(browser.contexts[0].pages)} 个打开的页面:")
                        for i, page in enumerate(browser.contexts[0].pages):
                            print(f"   {i+1}. {page.url}")
                            
                            # 检查是否是1688页面
                            if '1688.com' in page.url:
                                print(f"找到1688页面: {page.url}")
                    
                    await browser.close()
                    print("已断开Chrome连接")
                    return True
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                # 分析错误类型
                if "503" in error_msg:
                    print(f"Chrome服务暂时不可用 (503)")
                    print("可能Chrome正在启动中...")
                elif "econnrefused" in error_msg or "connection refused" in error_msg:
                    print(f"连接被拒绝，Chrome可能未启动")
                elif "timeout" in error_msg:
                    print(f"连接超时")
                else:
                    print(f"连接失败: {e}")
                
                if attempt < max_retries - 1:
                    # 指数退避策略
                    delay = min(initial_delay * (2 ** attempt), max_delay)
                    print(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    print("无法连接到Chrome调试实例")
                    print("\n故障排除指南:")
                    print("1. 运行 start_debug_chrome.bat 启动Chrome调试模式")
                    print("2. 确保端口9222未被其他程序占用")
                    print("3. 在Chrome中登录1688并打开采购车页面")
                    print("4. 等待Chrome完全启动后再试")
                    return False
        
    except ImportError:
        print("未安装playwright，请运行: pip install playwright")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False

def test_chrome_process():
    """测试Chrome进程状态"""
    print("\n检查Chrome进程状态...")
    
    import subprocess
    
    try:
        # 检查端口9222是否被占用
        result = subprocess.run(
            'netstat -aon | findstr :9222 | findstr LISTEN',
            capture_output=True,
            text=True,
            shell=True,
            encoding='gbk'
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print("端口9222已被占用")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        print(f"   进程ID: {pid}")
                        
                        # 获取进程名称
                        try:
                            task_result = subprocess.run(
                                f'tasklist /FI "PID eq {pid}" /FO CSV',
                                capture_output=True,
                                text=True,
                                shell=True,
                                encoding='gbk'
                            )
                            if task_result.returncode == 0:
                                lines = task_result.stdout.strip().split('\n')
                                if len(lines) > 1:
                                    # 跳过标题行，获取进程信息
                                    process_info = lines[1]
                                    if '"' in process_info:
                                        parts = process_info.split('"')
                                        if len(parts) >= 3:
                                            process_name = parts[1]
                                            print(f"   进程名称: {process_name}")
                        except:
                            pass
        else:
            print("端口9222未被占用，Chrome可能未启动")
            
    except Exception as e:
        print(f"检查进程状态时出错: {e}")

async def main():
    """主测试函数"""
    print("Chrome连接功能测试开始")
    print("=" * 50)
    
    # 测试Chrome进程状态
    test_chrome_process()
    
    # 测试Chrome连接
    success = await test_chrome_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("Chrome连接功能测试通过!")
    else:
        print("Chrome连接功能测试失败，请检查上述错误信息")
    
    return success

if __name__ == "__main__":
    # 运行异步测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)