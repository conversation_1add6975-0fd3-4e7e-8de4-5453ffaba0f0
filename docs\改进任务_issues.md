# 改进任务 Issue 清单（可直接迁移到平台）

说明：本文件将 `docs/改进任务清单.md` 中的建议拆分为可跟踪的 Issue 形式，便于在任意平台（GitHub/Gitee/本地）创建任务卡片。每个 Issue 含标题、描述、建议方案、验收标准与优先级。建议保持本文件与概览清单并存：

- 概览与背景：`docs/改进任务清单.md`
- 可执行任务：`docs/改进任务_issues.md`（本文件）

---

## Issue 1｜API 监听实现与异步模型不匹配（高）
- 标题：修复异步 Playwright 回调中使用 `response.json()` 的问题
- 描述：异步环境下在同步回调中直接 `response.json()` 无法 `await`，导致 API 监听基本不可用。
- 建议方案：
  - 将响应处理封装为异步任务（`asyncio.create_task`），在任务中 `await response.json()`；或用 `page.route/context.route` 统一拦截并收集。
  - 用事件/任务完成条件替代固定 `sleep/wait_for_timeout`。
- 验收标准：
  - 能稳定捕获目标订单接口 JSON；
  - 移除固定等待，API 数据在多页抓取不重复不遗漏。
- 参考：`src/core/extract_orders.py`（API 监听段落）

## Issue 2｜订单页判定字符串不一致（高）
- 标题：统一订单页 URL 识别策略
- 描述：当前混用 `buyer_order_list` 与 `buyer-order-list`，可能造成误判或漏判。
- 建议方案：集中管理 `order_page_patterns`，所有判定与导航统一基于该集合。
- 验收标准：非订单页能正确导航，订单页不误导航。
- 参考：`src/core/extract_orders.py`（导航与判定处）

## Issue 3｜监听器累积导致重复处理（高）
- 标题：清理或隔离多页抓取中的 `response` 回调
- 描述：每页/每轮注册 `page.on("response")` 未移除，导致回调累积、重复解析。
- 建议方案：
  - 单轮抓取注册监听并结束时移除；
  - 或仅注册一次，内部基于轮次/页码隔离处理。
- 验收标准：多页抓取时无重复解析同一响应。

## Issue 4｜DOM 抽取过度依赖 Shadow DOM（高）
- 标题：为非 Shadow DOM 结构提供等价抽取路径
- 描述：无 `shadowRoot` 的订单项被跳过，导致抽取失败。
- 建议方案：对 Light DOM（元素本身）走与 Shadow DOM 相同的字段抽取流程。
- 验收标准：无论是否存在 Shadow DOM 均能抽取到记录。

## Issue 5｜深度选择器性能风险（中）
- 标题：优化 `querySelectorDeep` 的搜索范围与缓存
- 描述：每次全量遍历并递归 Shadow DOM，且多选择器重复执行，复杂度高。
- 建议方案：
  - 先锁定订单容器再深搜；
  - 缓存/复用已发现的 `shadowRoot`；
  - 仅在必要时穿透。
- 验收标准：典型页面 DOM 解析耗时降低（建议目标 ≥30%）。

## Issue 6｜Excel 列处理可能重复（中）
- 标题：调整列重命名与补列顺序避免重复列
- 描述：先补中文列再 `rename` 英文→中文，可能产生重复列名。
- 建议方案：先 `rename`，再按目标中文列补缺；或仅对源列补列。
- 验收标准：导出 Excel 列唯一，无重复/空列。

## Issue 7｜参数未完全生效（中）
- 标题：统一 CLI 参数与行为（all-pages/max-pages/debug-port/headless）
- 描述：默认强制多页；未支持 `--debug-port`；`--headless` 在 CDP 模式无效且未说明。
- 建议方案：
  - 尊重 `--all-pages/--max-pages`；
  - 增加 `--debug-port` 并应用到 CDP 连接；
  - 明确 `--headless` 语义（仅自启浏览器时有效）或隐藏该参数。
- 验收标准：CLI 标志真实控制行为，帮助信息与实现一致。

## Issue 8｜异步上下文中的同步 I/O（中）
- 标题：图片下载与 PIL 处理改为非阻塞
- 描述：`download_image` 使用 `requests` 与 PIL 同步阻塞，拖慢事件循环。
- 建议方案：
  - 使用 `asyncio.to_thread` 将下载与图像处理移至线程池；
  - 或改用 `httpx.AsyncClient`/`aiohttp`。
- 验收标准：多图片下载不阻塞主循环，整体耗时明显下降。

## Issue 9｜API 过滤条件过宽（中）
- 标题：细化订单相关接口过滤与识别
- 描述：`/list` 过于宽泛，易误收集非订单数据。
- 建议方案：
  - 精确匹配订单接口路径；
  - 或基于响应 JSON 结构特征判断。
- 验收标准：收集的响应高度相关，无明显噪声。

## Issue 10｜统计指标不一致（低）
- 标题：统一统计口径并完善失败计数
- 描述：`failed` 未维护；`dom_success` 覆盖与累加混用；`total_orders` 最终覆盖。
- 建议方案：外层统一汇总，子步骤仅返回结果；在异常路径累加失败计数。
- 验收标准：日志与统计自洽，端到端数量一致。

## Issue 11｜数量解析鲁棒性（低）
- 标题：数量文本解析容错（如 “x2”“数量: 2件”）
- 描述：直接 `parseInt` 可靠性不足。
- 建议方案：先用正则提取数字再转换，覆盖常见格式。
- 验收标准：常见数量格式均正确解析。

## Issue 12｜详情链接赋值缺失（低）
- 标题：完善 `detail_url` 提取逻辑
- 描述：Excel 详情页列普遍为空。
- 建议方案：从商品标题链接、图片父节点、行操作入口等位置提取并填充。
- 验收标准：详情页列非空率显著提升（建议目标 ≥80%）。

## Issue 13｜图片与超链接同格体验（低）
- 标题：分离图片与文本超链的呈现
- 描述：图片可能遮挡“原图”文字链接。
- 建议方案：将“原图”“详情页”文本链接放相邻列，或新增专用链接列。
- 验收标准：图片与文本均清晰可用，点击无障碍。

## Issue 14｜网络与代理设置一致性（低）
- 标题：统一代理、超时、重试策略
- 描述：版本接口禁用代理，图片下载未禁用，策略不一致。
- 建议方案：集中配置网络策略，统一对外请求行为。
- 验收标准：在有/无代理环境结果一致可控。

## Issue 15｜日志级别与性能（低）
- 标题：收敛默认日志输出，保留 DEBUG 细节
- 描述：DOM 解析阶段日志过多影响性能与可读性。
- 建议方案：
  - 将逐商品/逐字段日志降为 DEBUG；
  - 默认 INFO 输出关键路径与统计。
- 验收标准：常规运行日志简洁，DEBUG 模式具备完整细节。

---

迁移提示：
- 可将每个 Issue 复制到 GitHub/Gitee Issue 中；
- 若需要按里程碑/迭代管理，可将 Issue 1-4 作为首个迭代目标；
- 背景与更多细节请参阅 `docs/改进任务清单.md`。


