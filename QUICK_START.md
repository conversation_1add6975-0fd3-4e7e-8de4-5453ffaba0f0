# 1688自动化项目 - 完整设置指南

## 🎉 环境设置完成！

项目现在拥有一个完全独立的Miniconda环境，包含所有必需的依赖。

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）

**Windows用户：**
```batch
双击运行 start_env.bat
```

**Linux/Mac用户：**
```bash
source start_env.sh
```

### 方法2：手动激活环境

```bash
# Windows
E:\miniconda\Scripts\activate.bat 1688_automation

# Linux/Mac
conda activate 1688_automation
```

### 方法3：直接使用环境Python

```bash
# Windows
E:/conda/envs/1688_automation/python.exe main_app.py

# Linux/Mac
~/miniconda3/envs/1688_automation/bin/python main_app.py
```

## 📦 环境包含的依赖

### GUI框架
- **PyQt6 6.7.1** - 现代GUI框架
- **tkinter** - 传统GUI框架

### 数据处理
- **pandas 2.3.1** - 数据分析
- **openpyxl 3.1.5** - Excel处理

### 网络和自动化
- **requests 2.32.4** - HTTP请求
- **Pillow 11.1.0** - 图像处理
- **playwright 1.54.0** - 浏览器自动化
- **Chromium** - 浏览器引擎

### 开发工具
- **asyncio** - 异步编程
- **difflib** - 字符串匹配

## 🎯 可用的运行模式

### 1. 主程序（自动检测）
```bash
python main_app.py
```
- 自动检测最佳运行模式
- 优先使用GUI，回退到CLI

### 2. GUI模式
```bash
python main_app.py --mode gui
# 或者
python ui_app/qt_real.py
```

### 3. CLI模式
```bash
python main_app.py --mode cli
# 或者
python export_cart_excel.py
```

### 4. 直接功能调用
```bash
# 采购车数据提取
python main_app.py --direct cart

# 订单数据增强
python main_app.py --direct enhance --input orders.xlsx --output enhanced.xlsx

# 订单数据抓取
python main_app.py --direct extract --url "https://detail.1688.com/offer/123.html"

# 数据验证
python main_app.py --direct validate --type project
```

## 🔧 使用前准备

### 1. Chrome调试模式
在运行数据提取功能前，请先启动Chrome调试模式：
```bash
# Windows
start_debug_chrome.bat

# 或者手动启动
"E:\conda\envs\1688_automation\python.exe" test_chrome_port.py
```

### 2. 登录1688
在Chrome调试模式中：
1. 登录您的1688账号
2. 导航到采购车页面（如需提取采购车数据）
3. 保持Chrome运行

## 🧪 测试环境

运行环境测试脚本：
```bash
python test_environment.py
```

## 📝 项目文件说明

### 核心程序
- `main_app.py` - 主程序入口
- `export_cart_excel.py` - CLI版本
- `ui_app/qt_real.py` - PyQt6 GUI版本
- `ui_app/main.py` - tkinter GUI版本

### 工具脚本
- `test_chrome_port.py` - Chrome连接测试
- `start_debug_chrome.bat` - Chrome调试启动
- `test_environment.py` - 环境测试

### 文档
- `CLAUDE.md` - 项目说明
- `ENVIRONMENT_SETUP.md` - 环境设置详情

## 🎨 GUI功能特性

### PyQt6 GUI版本
- 现代化界面设计
- 实时进度显示
- 完整的错误处理
- 支持拖拽文件
- 日志记录功能

### 功能模块
1. **采购车数据提取** - 从1688购物车提取数据
2. **订单数据增强** - 为Excel订单添加图片和链接
3. **订单数据抓取** - 从1688详情页抓取数据
4. **数据验证** - 验证数据完整性和一致性

## 🔍 故障排除

### 常见问题

1. **Chrome连接失败**
   ```bash
   # 测试Chrome连接
   python test_chrome_port.py
   
   # 手动启动Chrome
   start_debug_chrome.bat
   ```

2. **GUI无法启动**
   ```bash
   # 检查PyQt6
   python -c "from PyQt6.QtWidgets import QApplication; print('OK')"
   
   # 使用CLI模式
   python main_app.py --mode cli
   ```

3. **编码问题**
   ```bash
   # 确保使用UTF-8编码
   set PYTHONIOENCODING=utf-8
   ```

4. **依赖缺失**
   ```bash
   # 重新安装依赖
   conda install -n 1688_automation --all
   ```

## 📊 性能优化

- 独立环境避免冲突
- 硬件加速的GUI渲染
- 优化的数学库（MKL）
- 高效的浏览器自动化

## 🔒 安全考虑

- 独立环境隔离风险
- Chrome调试模式保持会话安全
- 不存储敏感信息
- 网络请求使用标准库

## 🎓 下一步

1. **运行测试**：`python test_environment.py`
2. **启动GUI**：`python main_app.py`
3. **体验功能**：尝试不同的数据提取和增强功能
4. **查看文档**：阅读`CLAUDE.md`了解更多详情

---

## 📞 支持

如果遇到问题：
1. 检查环境测试结果
2. 查看错误日志
3. 尝试CLI模式
4. 重新运行环境设置

---

**🎉 恭喜！您的1688自动化项目环境已完全配置好！**