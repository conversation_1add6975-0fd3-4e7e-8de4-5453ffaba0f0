"""
简化的命令行测试程序 - 避免编码问题
"""

import sys
import os
from pathlib import Path
import time
import json

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_core_modules():
    """测试核心模块"""
    print("1688采购车数据处理工具 - 核心模块测试")
    print("=" * 50)
    
    tests_passed = 0
    tests_total = 0
    
    try:
        # 测试配置模块
        tests_total += 1
        from core.config import Config
        config = Config()
        print(f"OK 配置模块: {config.get('app_name')}")
        tests_passed += 1
        
        # 测试错误处理模块
        tests_total += 1
        from core.error_handler import ErrorHandler
        handler = ErrorHandler()
        error_msg = handler.handle_error("FILE_NOT_FOUND", "测试")
        print(f"OK 错误处理模块: 错误消息长度 {len(error_msg)}")
        tests_passed += 1
        
        # 测试文件工具模块
        tests_total += 1
        from utils.file_utils import FileUtils
        file_info = FileUtils.get_file_info(__file__)
        print(f"OK 文件工具模块: 处理文件 {file_info['name']}")
        tests_passed += 1
        
        # 测试辅助函数模块
        tests_total += 1
        from utils.helpers import format_duration, format_file_size
        duration = format_duration(3665)
        size = format_file_size(1024 * 1024)
        print(f"OK 辅助函数模块: {duration}, {size}")
        tests_passed += 1
        
        # 测试Chrome管理器模块
        tests_total += 1
        from core.chrome_manager import ChromeManager
        chrome_manager = ChromeManager()
        chrome_path = chrome_manager.find_chrome_path()
        print(f"OK Chrome管理器模块: {chrome_path or '未找到Chrome'}")
        tests_passed += 1
        
        # 测试数据处理器模块
        tests_total += 1
        from core.data_processor import DataProcessor
        processor = DataProcessor()
        print("OK 数据处理器模块: 初始化成功")
        tests_passed += 1
        
        print(f"\nSUCCESS {tests_passed}/{tests_total} 核心模块测试通过!")
        print("=" * 50)
        
        return tests_passed == tests_total
        
    except Exception as e:
        print(f"\nERROR 测试失败: {e}")
        print("=" * 50)
        return False

def test_chrome_features():
    """测试Chrome相关功能"""
    print("\n" + "=" * 50)
    print("测试Chrome相关功能")
    print("=" * 50)
    
    try:
        from core.chrome_manager import ChromeManager
        
        manager = ChromeManager()
        
        # 测试查找Chrome路径
        chrome_path = manager.find_chrome_path()
        print(f"OK Chrome路径: {chrome_path}")
        
        # 测试检查端口占用
        port_in_use = manager.is_port_in_use(9222)
        print(f"OK 端口9222占用状态: {port_in_use}")
        
        # 测试终止进程
        print("OK 正在清理现有Chrome进程...")
        manager.kill_chrome_processes()
        time.sleep(2)
        
        print("OK Chrome功能测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR Chrome功能测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n" + "=" * 50)
    print("测试数据处理功能")
    print("=" * 50)
    
    try:
        from core.data_processor import DataProcessor
        
        processor = DataProcessor()
        
        # 测试数据格式化
        test_data = {
            "name": "测试商品",
            "price": "99.99",
            "quantity": "5个",
            "supplier": "测试供应商"
        }
        
        processed = processor.process_item(test_data)
        print("OK 数据处理测试:")
        print(f"  原始数据: {test_data}")
        print(f"  处理后: {processed}")
        
        print("OK 数据处理测试通过")
        return True
        
    except Exception as e:
        print(f"ERROR 数据处理测试失败: {e}")
        return False

def show_project_status():
    """显示项目状态"""
    print("\n项目状态:")
    print("-" * 30)
    print(f"项目路径: {Path(__file__).parent}")
    print(f"Python版本: {sys.version}")
    
    # 检查主要文件
    main_files = [
        "main.py",
        "demo.py", 
        "config.json",
        "requirements.txt",
        "README.md"
    ]
    
    print("\n主要文件:")
    for file in main_files:
        file_path = Path(__file__).parent / file
        status = "存在" if file_path.exists() else "不存在"
        print(f"  {file}: {status}")
    
    # 检查目录结构
    directories = ["core", "ui", "utils", "data"]
    print("\n目录结构:")
    for directory in directories:
        dir_path = Path(__file__).parent / directory
        status = "存在" if dir_path.exists() else "不存在"
        print(f"  {directory}/: {status}")

def main():
    """主函数"""
    print("1688采购车数据处理工具")
    print("简化测试程序")
    print("=" * 50)
    
    # 显示项目状态
    show_project_status()
    
    # 测试核心模块
    core_ok = test_core_modules()
    
    # 测试Chrome功能
    chrome_ok = test_chrome_features()
    
    # 测试数据处理
    data_ok = test_data_processing()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"  核心模块: {'通过' if core_ok else '失败'}")
    print(f"  Chrome功能: {'通过' if chrome_ok else '失败'}")
    print(f"  数据处理: {'通过' if data_ok else '失败'}")
    
    if core_ok and chrome_ok and data_ok:
        print("\n所有测试通过！项目状态良好。")
        print("\n使用说明:")
        print("1. GUI版本: python main.py (需要tkinter)")
        print("2. 演示版本: python demo.py (需要tkinter)")
        print("3. 核心测试: python simple_test.py")
        print("\n注意: 完整的GUI功能需要安装tkinter模块")
        return 0
    else:
        print("\n部分测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    sys.exit(main())