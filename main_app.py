#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688采购车数据处理工具 - 兼容版本
支持GUI和CLI模式，自动检测环境
"""

import sys
import os
import argparse
import json
import logging
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_pyqt_availability():
    """检查PyQt6是否可用"""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        # 尝试创建一个简单的应用程序来验证
        import sys
        app = QApplication(sys.argv)
        app.quit()
        return True
    except ImportError:
        return False
    except Exception as e:
        print(f"⚠️ PyQt6导入失败: {e}")
        return False

def launch_gui_mode():
    """启动GUI模式"""
    try:
        print("🖥️  启动GUI模式...")
        # 导入并运行PyQt6 GUI主程序
        from src.gui.qt_real import main as gui_main
        gui_main()
        return True
    except Exception as e:
        print(f"❌ GUI模式启动失败: {e}")
        return False

def launch_cli_mode():
    """启动CLI模式"""
    try:
        print("💻 启动命令行模式...")
        from src.cli.cli_app import main as cli_main
        cli_main()
    except Exception as e:
        print(f"❌ 命令行模式启动失败: {e}")
        return False

def launch_direct_function(func_name, **kwargs):
    """直接执行特定功能"""
    print(f"直接执行功能: {func_name}")
    
    # 构建基础路径
    base_path = Path(__file__).parent
    
    if func_name == "cart":
        # 采购车数据提取
        script = base_path / "src" / "core" / "export_cart_excel.py"
        cmd = f"python {script}"
        
        if kwargs.get('output'):
            cmd += f" --output \"{kwargs['output']}\""
            
        os.system(cmd)
        
    elif func_name == "enhance":
        # 订单数据增强
        script = base_path / "src" / "core" / "enrich_orders_with_images.py"
        cmd = f"python {script}" 
        
        if kwargs.get('input'):
            cmd += f" --input \"{kwargs['input']}\""
        if kwargs.get('output'):
            cmd += f" --output \"{kwargs['output']}\""
            
        os.system(cmd)
        
    elif func_name == "extract":
        # 订单数据抓取
        script = base_path / "src" / "core" / "extract_orders.py"
        cmd = f"python {script}"
        
        if kwargs.get('url'):
            cmd += f" --url \"{kwargs['url']}\""
        if kwargs.get('output'):
            cmd += f" --output \"{kwargs['output']}\""
        if kwargs.get('headless'):
            cmd += " --headless"
            
        os.system(cmd)
        
    elif func_name == "validate":
        # 数据验证
        validation_scripts = {
            'project': 'scripts/validate_project.py',
            'cart': 'scripts/validate_optimization.py',
            'orders': 'scripts/validate_order_data.py',
            'tests': 'tests/run_tests.py'
        }
        
        script_name = kwargs.get('type', 'project')
        if script_name in validation_scripts:
            script = base_path / validation_scripts[script_name]
            os.system(f"python {script}")
        else:
            print(f"❌ 未知的验证类型: {script_name}")
            
    else:
        print(f"❌ 未知功能: {func_name}")

def show_help():
    """显示帮助信息"""
    print("""
1688采购车数据处理工具 - 使用指南

启动模式:
    python main_app.py              # 自动检测最佳模式
    python main_app.py --mode gui   # 强制GUI模式
    python main_app.py --mode cli   # 强制CLI模式
    python main_app.py --direct     # 直接执行功能

直接执行功能:
    python main_app.py --direct cart                    # 采购车数据提取
    python main_app.py --direct cart --output file.xlsx  # 指定输出文件
    python main_app.py --direct enhance                 # 订单数据增强
    python main_app.py --direct enhance --input in.xlsx --output out.xlsx
    python main_app.py --direct extract                 # 订单数据抓取
    python main_app.py --direct extract --headless      # 无头模式
    python main_app.py --direct validate                # 数据验证

环境选项:
    --mode MODE        # 启动模式 (gui/cli/auto)
    --direct FUNC      # 直接执行功能
    --output FILE      # 输出文件路径
    --input FILE       # 输入文件路径
    --url URL          # 目标URL
    --debug-port PORT  # 调试端口
    --headless         # 无头模式
    --type TYPE        # 验证类型
    --show-help        # 显示帮助

提示:
    * 程序会自动检测tkinter可用性
    * 推荐使用自动检测模式
    * 如需GUI，请确保已安装tkinter
    """)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='1688采购车数据处理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--mode', choices=['gui', 'cli', 'auto'], 
                       default='auto', help='启动模式')
    parser.add_argument('--direct', choices=['cart', 'enhance', 'extract', 'validate'],
                       help='直接执行特定功能')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--input', help='输入文件路径')
    parser.add_argument('--url', help='目标URL')
    parser.add_argument('--debug-port', type=int, help='调试端口')
    parser.add_argument('--headless', action='store_true', help='无头模式')
    parser.add_argument('--type', help='验证类型')
    parser.add_argument('--show-help', action='store_true', help='显示帮助')
    
    args = parser.parse_args()
    
    # 显示帮助
    if args.show_help:
        show_help()
        return
    
    # 显示程序信息
    print("1688采购车数据处理工具")
    print("=" * 50)
    
    # 直接执行功能
    if args.direct:
        launch_direct_function(args.direct, **vars(args))
        return
    
    # 根据模式启动
    if args.mode == 'gui':
        if not launch_gui_mode():
            print("GUI模式启动失败")
            sys.exit(1)
    elif args.mode == 'cli':
        if not launch_cli_mode():
            print("CLI模式启动失败")
            sys.exit(1)
    else:  # auto模式
        print("自动检测环境...")
        
        if check_pyqt_availability():
            print("检测到PyQt6，使用GUI模式")
            if not launch_gui_mode():
                print("GUI模式启动失败。请检查错误日志。")
                # Temporarily disabled CLI fallback to focus on GUI issues.
                # launch_cli_mode()
        else:
            print("未检测到PyQt6，使用CLI模式")
            print("如需GUI版本，请安装PyQt6:")
            print("   pip install PyQt6")
            print()
            launch_cli_mode()

if __name__ == "__main__":
    main()