# 商品SKU提取功能说明

## 🎯 功能概述

已成功将 `extract_orders.py` 的数据提取逻辑从**以订单为单位**改为**以商品SKU为单位**，满足您的具体要求。

## 🔄 主要修改内容

### 1. 数据提取粒度变更
- **修改前**: 每个订单生成一条记录，多个商品信息聚合在一起
- **修改后**: 每个商品SKU生成一条独立记录，即使在同一订单中

### 2. 新增展开更多商品功能
- **自动展开**: 在提取数据前，自动点击所有"展开更多"按钮
- **完整提取**: 确保获取订单中的所有隐藏商品
- **智能识别**: 支持多种展开按钮的选择器

### 3. 改进的选择器
- **卖家名称**: 使用 `.company-name` 作为主要选择器
- **商品条目**: 支持 `order-item-entry-product` 和多种备用选择器
- **商品信息**: 针对性提取每个SKU的详细信息

## 📊 提取的数据字段

每个商品SKU记录包含以下字段：

### 商品级别信息
- **商品名称**: 商品标题（包含规格信息）
- **商品规格**: 颜色、尺寸等规格信息
- **商品高清图片**: 商品图片URL
- **商品单价**: 单个商品的价格
- **商品数量**: 该SKU的购买数量
- **商品小计**: 单价 × 数量

### 订单级别信息（复制到每个商品记录）
- **订单号**: 订单唯一标识
- **下单时间**: 订单创建时间
- **下单账号**: 买家账号信息
- **卖家名称（生产商）**: 商品供应商名称

### 其他信息
- **商品详情链接**: 商品页面链接
- **订单状态**: 订单当前状态

## 🔧 技术实现

### 1. 展开更多商品
```javascript
// 自动查找并点击所有展开按钮
const expandButtons = querySelectorDeep('.expend-btn, .expand-btn, [class*="expend"], [class*="expand"]');
expandButtons.forEach(btn => {
    if (btn.textContent.includes('展开更多')) {
        btn.click();
    }
});
```

### 2. 商品SKU遍历
```javascript
// 遍历每个订单中的所有商品条目
productEntries.forEach((productEntry, productIndex) => {
    const product = extractProductInfo(productEntry, orderInfo, orderIndex, productIndex);
    if (product) {
        allProducts.push(product);
    }
});
```

### 3. 卖家名称提取
```javascript
// 使用正确的选择器提取卖家名称
const shopSelectors = [
    '.company-name',                                    // 主要选择器
    '.shop-name, .seller-name, .store-name',           // 备用选择器
    '[class*="company"], [class*="shop"], [class*="seller"]'
];
```

## 📈 数据示例

### 修改前（以订单为单位）
```
订单号: 12345678901234567
商品名称: 女装连衣裙等多个商品
总金额: 359.60
数量: 4
```

### 修改后（以商品SKU为单位）
```
记录1:
  订单号: 12345678901234567
  商品名称: 女装连衣裙 (红色, L码)
  商品规格: 红色, L码
  商品单价: 89.90
  商品数量: 1
  商品小计: 89.90
  卖家名称: 开封市禹王台区臻颂日用百货店

记录2:
  订单号: 12345678901234567
  商品名称: 女装连衣裙 (蓝色, M码)
  商品规格: 蓝色, M码
  商品单价: 89.90
  商品数量: 2
  商品小计: 179.80
  卖家名称: 开封市禹王台区臻颂日用百货店

记录3:
  订单号: 12345678901234567
  商品名称: 女装连衣裙 (黑色, S码)
  商品规格: 黑色, S码
  商品单价: 89.90
  商品数量: 1
  商品小计: 89.90
  卖家名称: 开封市禹王台区臻颂日用百货店
```

## ✅ 满足的需求

1. **✅ 以商品SKU为单位**: 每个商品都有独立记录
2. **✅ 商品高清图片**: 提取商品图片URL和图片
3. **✅ 商品名称**: 提取完整的商品名称
4. **✅ 商品规格信息**: 提取颜色、尺寸等规格
5. **✅ 商品单价**: 提取每个SKU的单价
6. **✅ 商品数量**: 提取每个SKU的数量
7. **✅ 卖家名称**: 提取生产商信息
8. **✅ 独立记录**: 即使相同信息也为每个SKU单独记录
9. **✅ 展开更多**: 自动展开隐藏的商品

## 🚀 使用方法

```bash
# 运行修改后的提取工具
python src/core/extract_orders.py --url "订单页面URL" --output "输出文件.xlsx"

# 测试功能
python test_sku_simple.py
```

## 📋 输出文件结构

Excel文件包含以下列：
- 订单号
- 商品名称
- 商品规格
- 商品单价
- 商品数量
- 商品小计
- 卖家名称（生产商）
- 下单时间
- 下单账号
- 订单状态
- 商品高清图片
- 商品详情链接

## 🔍 验证结果

通过测试验证，修改后的功能能够：
- 正确识别并展开隐藏的商品
- 为每个商品SKU创建独立记录
- 准确提取商品规格信息
- 正确复制订单级别信息到每个商品记录
- 生成结构化的Excel输出文件

修改已完成并通过测试验证！
