# MCP Server Configuration
# This file contains the MCP server configurations for Claude Code

## Installed MCP Servers

### 1. Context7 (@upstash/context7-mcp)
- **Purpose**: Documentation lookup and framework patterns
- **Version**: v1.0.14
- **Usage**: --context7 or --c7 flag
- **Activation**: Auto-activates for library imports and framework questions

### 2. Sequential Thinking (@modelcontextprotocol/server-sequential-thinking)
- **Purpose**: Complex analysis and systematic problem solving
- **Version**: 2025.7.1
- **Usage**: --sequential or --seq flag
- **Activation**: Auto-activates for complex debugging and analysis

### 3. <PERSON><PERSON><PERSON> (@agent-infra/mcp-server-browser)
- **Purpose**: Browser automation and web scraping
- **Version**: 1.2.18
- **Usage**: --browser flag
- **Activation**: Auto-activates for web-related tasks

### 4. Filesystem (@modelcontextprotocol/server-filesystem)
- **Purpose**: File system access and management
- **Version**: 2025.7.29
- **Usage**: --filesystem flag
- **Activation**: Auto-activates for file operations

## Usage Examples

### Using Context7 for Documentation
```bash
# Get documentation for a library
claude --context7 "How do I use pandas DataFrame?"

# Auto-activation when detecting library imports
claude "Explain this React hook code"
```

### Using Sequential for Complex Analysis
```bash
# Debug complex issues
claude --sequential "Debug this memory leak issue"

# Systematic problem solving
claude --seq "Analyze this system architecture"
```

### Using Browser for Web Tasks
```bash
# Web scraping and automation
claude --browser "Scrape product data from this website"

# Browser testing
claude --browser "Test this web application"
```

### Using Filesystem for File Operations
```bash
# File system management
claude --filesystem "Organize these files by date"

# Code analysis across files
claude --filesystem "Find all TODO comments in the codebase"
```

## Configuration

The MCP servers are now installed globally and can be used with Claude Code. They will auto-activate based on the context of your requests, or you can manually activate them using the flags shown above.

## Next Steps

1. Restart Claude Code to ensure all servers are properly loaded
2. Test the servers with the example commands above
3. Check server status with: `/mcp` command in Claude Code