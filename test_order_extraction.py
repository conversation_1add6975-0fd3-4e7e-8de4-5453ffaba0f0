#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试订单数据抓取功能
"""

import asyncio
import sys
import requests
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def check_chrome_debug():
    """检查Chrome调试接口状态"""
    print("🔍 检查Chrome调试接口状态...")
    
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Chrome调试接口正常")
            print(f"   浏览器: {version_info.get('Browser', 'Unknown')}")
            print(f"   WebSocket: {version_info.get('webSocketDebuggerUrl', 'Unknown')}")
            return True
        else:
            print(f"❌ Chrome调试接口异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return False

async def test_order_page_access():
    """测试订单页面访问"""
    print("\n🌐 测试订单页面访问...")
    
    try:
        from playwright.async_api import async_playwright
        
        # 获取WebSocket URL
        debug_port = 9222
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例")
            
            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                print(f"📄 找到 {len(browser.contexts[0].pages)} 个页面:")
                for i, p in enumerate(browser.contexts[0].pages):
                    print(f"   {i+1}. {p.url}")
                    if '1688.com' in p.url:
                        page = p
                        print(f"   ✓ 选择此页面作为操作页面")
                        break
            
            if not page:
                print("❌ 未找到1688页面")
                print("💡 请在Chrome中打开1688网站并登录")
                await browser.close()
                return False
            
            # 测试导航到订单页面
            order_url = "https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html?tradeStatus=waitbuyerreceive&spm=a260k.home2025.topmenu.dmyorder&page=1&pageSize=10"
            print(f"\n🔄 测试导航到订单页面...")
            print(f"   目标URL: {order_url}")
            
            try:
                await page.goto(order_url, timeout=30000, wait_until="domcontentloaded")
                await page.wait_for_timeout(3000)
                
                current_url = page.url
                page_title = await page.title()
                
                print(f"✅ 导航成功")
                print(f"   当前URL: {current_url}")
                print(f"   页面标题: {page_title}")
                
                # 检查页面内容
                if '订单' in page_title or 'order' in current_url.lower():
                    print("✅ 确认已到达订单页面")
                    
                    # 检查是否有订单数据
                    try:
                        # 等待页面加载完成
                        await page.wait_for_timeout(5000)
                        
                        # 检查页面中是否有订单相关元素
                        order_elements = await page.query_selector_all('[class*="order"], [class*="trade"]')
                        print(f"📊 找到 {len(order_elements)} 个可能的订单元素")
                        
                        if len(order_elements) == 0:
                            print("⚠️ 页面中没有找到订单元素，可能是空订单页面")
                        else:
                            print("✅ 页面包含订单相关元素")
                            
                    except Exception as e:
                        print(f"⚠️ 检查页面元素时出错: {e}")
                        
                else:
                    print("⚠️ 可能未正确到达订单页面")
                
                await browser.close()
                return True
                
            except Exception as e:
                print(f"❌ 导航失败: {e}")
                await browser.close()
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_script_execution():
    """测试脚本执行"""
    print("\n🚀 测试订单抓取脚本执行...")
    
    import subprocess
    
    script_path = PROJECT_ROOT / "src" / "core" / "extract_orders.py"
    command = [sys.executable, str(script_path), "--help"]
    
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 脚本可以正常执行")
            print("📋 帮助信息:")
            print(result.stdout)
            return True
        else:
            print(f"❌ 脚本执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 脚本执行出错: {e}")
        return False

async def main():
    """主测试函数"""
    print("订单数据抓取功能测试")
    print("=" * 50)
    
    # 1. 检查Chrome调试接口
    chrome_ok = check_chrome_debug()
    
    if not chrome_ok:
        print("\n💡 解决建议:")
        print("1. 运行 start_debug_chrome.bat 启动Chrome调试模式")
        print("2. 等待Chrome完全启动（20-30秒）")
        print("3. 在Chrome中登录1688账号")
        print("4. 重新运行此测试")
        return
    
    # 2. 测试脚本执行
    script_ok = test_script_execution()
    
    # 3. 测试订单页面访问
    page_ok = await test_order_page_access()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   Chrome调试接口: {'✅ 正常' if chrome_ok else '❌ 异常'}")
    print(f"   脚本执行: {'✅ 正常' if script_ok else '❌ 异常'}")
    print(f"   订单页面访问: {'✅ 正常' if page_ok else '❌ 异常'}")
    
    if chrome_ok and script_ok and page_ok:
        print("\n🎉 所有测试通过！订单抓取功能应该可以正常工作")
        print("\n💡 使用方法:")
        print("1. 在GUI中选择'订单数据抓取'功能")
        print("2. 确认Chrome已登录1688")
        print("3. 点击开始处理")
    else:
        print("\n⚠️ 部分测试失败，请检查上述问题")

if __name__ == "__main__":
    asyncio.run(main())
