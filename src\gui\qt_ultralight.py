#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688采购车数据处理工具 - 超轻量版
使用PyQt6的基础模块，最小化依赖，适合打包
"""

import sys
import os
import time
from pathlib import Path

# 尝试导入PyQt6的基础模块
try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QLabel, QPushButton, QProgressBar, QTextEdit, QRadioButton,
        QButtonGroup, QFileDialog, QMessageBox
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal
    from PyQt6.QtGui import QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    print("PyQt6不可用，将使用控制台版本")

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

if PYQT_AVAILABLE:
    class SimpleWorker(QThread):
        """简化的工作线程"""
        progress = pyqtSignal(int)
        status = pyqtSignal(str)
        log = pyqtSignal(str, str)
        done = pyqtSignal()
        
        def __init__(self, mode, file_path=None):
            super().__init__()
            self.mode = mode
            self.file_path = file_path
            self.running = True
            
        def stop(self):
            self.running = False
            
        def run(self):
            try:
                if self.mode == 'cart':
                    self.process_cart()
                else:
                    self.process_order()
            except Exception as e:
                self.log.emit(f"错误: {str(e)}", "error")
            finally:
                self.done.emit()
        
        def process_cart(self):
            """处理采购车数据"""
            self.log.emit("开始采购车数据提取...", "info")
            steps = ["连接浏览器", "提取数据", "处理图片", "生成报告"]
            
            for i, step in enumerate(steps):
                if not self.running: break
                self.log.emit(f"步骤 {i+1}: {step}", "info")
                for j in range(25):
                    if not self.running: break
                    self.progress.emit(i * 25 + j)
                    self.status.emit(f"处理中: {step}")
                    self.msleep(40)
            
            if self.running:
                self.log.emit("采购车数据处理完成!", "success")
                self.status.emit("完成")
        
        def process_order(self):
            """处理订单数据"""
            if not self.file_path:
                self.log.emit("请选择Excel文件", "warning")
                return
                
            self.log.emit(f"处理文件: {os.path.basename(self.file_path)}", "info")
            steps = ["读取文件", "分析数据", "匹配商品", "增强数据", "保存结果"]
            
            for i, step in enumerate(steps):
                if not self.running: break
                self.log.emit(f"步骤 {i+1}: {step}", "info")
                for j in range(20):
                    if not self.running: break
                    self.progress.emit(i * 20 + j)
                    self.status.emit(f"处理中: {step}")
                    self.msleep(50)
            
            if self.running:
                self.log.emit("订单数据处理完成!", "success")
                self.status.emit("完成")

    class UltraLightApp(QMainWindow):
        """超轻量应用界面"""
        
        def __init__(self):
            super().__init__()
            self.worker = None
            self.setup_ui()
            
        def setup_ui(self):
            """设置界面"""
            self.setWindowTitle("1688数据工具")
            self.setFixedSize(450, 350)
            
            # 主部件
            widget = QWidget()
            self.setCentralWidget(widget)
            layout = QVBoxLayout(widget)
            layout.setSpacing(8)
            layout.setContentsMargins(10, 10, 10, 10)
            
            # 标题
            title = QLabel("1688采购车数据处理工具")
            title.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(title)
            
            # 功能选择
            func_layout = QHBoxLayout()
            self.radio_group = QButtonGroup()
            
            cart_radio = QRadioButton("采购车提取")
            cart_radio.setChecked(True)
            self.radio_group.addButton(cart_radio)
            cart_radio.mode = 'cart'
            func_layout.addWidget(cart_radio)
            
            order_radio = QRadioButton("订单增强")
            self.radio_group.addButton(order_radio)
            order_radio.mode = 'order'
            func_layout.addWidget(order_radio)
            
            layout.addLayout(func_layout)
            
            # 进度
            self.progress = QProgressBar()
            self.progress.setFixedHeight(20)
            layout.addWidget(self.progress)
            
            self.status = QLabel("准备就绪")
            self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(self.status)
            
            # 日志
            self.log_text = QTextEdit()
            self.log_text.setFixedHeight(100)
            self.log_text.setReadOnly(True)
            layout.addWidget(self.log_text)
            
            # 按钮
            btn_layout = QHBoxLayout()
            
            self.start_btn = QPushButton("开始")
            self.start_btn.clicked.connect(self.start_work)
            self.start_btn.setFixedWidth(60)
            btn_layout.addWidget(self.start_btn)
            
            self.stop_btn = QPushButton("停止")
            self.stop_btn.clicked.connect(self.stop_work)
            self.stop_btn.setEnabled(False)
            self.stop_btn.setFixedWidth(60)
            btn_layout.addWidget(self.stop_btn)
            
            self.clear_btn = QPushButton("清空")
            self.clear_btn.clicked.connect(self.clear_log)
            self.clear_btn.setFixedWidth(60)
            btn_layout.addWidget(self.clear_btn)
            
            self.exit_btn = QPushButton("退出")
            self.exit_btn.clicked.connect(self.close)
            self.exit_btn.setFixedWidth(60)
            btn_layout.addWidget(self.exit_btn)
            
            layout.addLayout(btn_layout)
        
        def start_work(self):
            """开始工作"""
            checked = self.radio_group.checkedButton()
            mode = checked.mode if checked else 'cart'
            file_path = None
            
            if mode == 'order':
                file_path, _ = QFileDialog.getOpenFileName(
                    self, "选择Excel文件", "", "Excel (*.xlsx *.xls)"
                )
                if not file_path:
                    self.add_log("未选择文件", "warning")
                    return
            
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress.setValue(0)
            
            self.worker = SimpleWorker(mode, file_path)
            self.worker.progress.connect(self.progress.setValue)
            self.worker.status.connect(self.status.setText)
            self.worker.log.connect(self.add_log)
            self.worker.done.connect(self.work_done)
            self.worker.start()
        
        def stop_work(self):
            """停止工作"""
            if self.worker:
                self.worker.stop()
            self.add_log("已停止", "warning")
            self.reset_buttons()
        
        def work_done(self):
            """工作完成"""
            self.reset_buttons()
            if self.worker:
                self.worker.deleteLater()
                self.worker = None
        
        def reset_buttons(self):
            """重置按钮"""
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
        
        def clear_log(self):
            """清空日志"""
            self.log_text.clear()
            self.add_log("日志已清空", "info")
        
        def add_log(self, msg, msg_type="info"):
            """添加日志"""
            timestamp = time.strftime("%H:%M:%S")
            color = {"success": "#4CAF50", "warning": "#FF9800", "error": "#f44336"}.get(msg_type, "#000")
            self.log_text.append(f'<span style="color: {color}">[{timestamp}] {msg}</span>')
        
        def closeEvent(self, event):
            """关闭事件"""
            if self.worker and self.worker.isRunning():
                reply = QMessageBox.question(
                    self, "确认退出", "任务运行中，确定退出？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.Yes:
                    self.worker.stop()
                    self.worker.wait()
                else:
                    event.ignore()
                    return
            event.accept()

else:
    # 控制台版本
    class ConsoleApp:
        """控制台版本应用"""
        
        def __init__(self):
            self.running = True
            
        def show_menu(self):
            """显示菜单"""
            print("\n" + "="*40)
            print("1688采购车数据处理工具 - 控制台版")
            print("="*40)
            print("1. 采购车数据提取")
            print("2. 订单数据增强")
            print("3. 退出")
            print("="*40)
            
        def get_choice(self):
            """获取用户选择"""
            while True:
                try:
                    choice = input("\n请选择功能 (1-3): ").strip()
                    if choice in ['1', '2', '3']:
                        return choice
                    print("无效选择，请重新输入")
                except KeyboardInterrupt:
                    return '3'
        
        def process_cart(self):
            """处理采购车数据"""
            print("\n开始采购车数据提取...")
            steps = ["连接浏览器", "提取数据", "处理图片", "生成报告"]
            
            for i, step in enumerate(steps, 1):
                if not self.running:
                    break
                print(f"步骤 {i}: {step}")
                for j in range(1, 26):
                    if not self.running:
                        break
                    progress = (i-1) * 25 + j
                    print(f"\r进度: {progress}%", end="", flush=True)
                    time.sleep(0.04)
            
            if self.running:
                print("\n采购车数据处理完成!")
        
        def process_order(self):
            """处理订单数据"""
            file_path = input("\n请输入Excel文件路径: ").strip()
            if not file_path or not os.path.exists(file_path):
                print("文件不存在或未选择")
                return
            
            print(f"处理文件: {os.path.basename(file_path)}")
            steps = ["读取文件", "分析数据", "匹配商品", "增强数据", "保存结果"]
            
            for i, step in enumerate(steps, 1):
                if not self.running:
                    break
                print(f"步骤 {i}: {step}")
                for j in range(1, 21):
                    if not self.running:
                        break
                    progress = (i-1) * 20 + j
                    print(f"\r进度: {progress}%", end="", flush=True)
                    time.sleep(0.05)
            
            if self.running:
                print("\n订单数据处理完成!")
        
        def run(self):
            """运行应用"""
            print("1688采购车数据处理工具")
            print("提示: 按 Ctrl+C 可以随时停止")
            
            while self.running:
                try:
                    self.show_menu()
                    choice = self.get_choice()
                    
                    if choice == '1':
                        self.process_cart()
                    elif choice == '2':
                        self.process_order()
                    elif choice == '3':
                        print("感谢使用!")
                        break
                        
                except KeyboardInterrupt:
                    print("\n\n检测到中断信号...")
                    if input("确定要退出吗? (y/N): ").lower() == 'y':
                        break
                except Exception as e:
                    print(f"发生错误: {e}")


def main():
    """主函数"""
    if PYQT_AVAILABLE:
        app = QApplication(sys.argv)
        app.setApplicationName("1688数据工具")
        
        window = UltraLightApp()
        window.show()
        
        sys.exit(app.exec())
    else:
        console_app = ConsoleApp()
        console_app.run()


if __name__ == "__main__":
    main()