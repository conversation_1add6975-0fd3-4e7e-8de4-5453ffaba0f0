# 1688 订单抓取模块 改进任务清单

本清单基于对 `src/core/extract_orders.py` 的代码审阅，梳理出高优先级到低优先级的优化项，旨在提升稳定性、性能与可维护性。每一项包含任务描述、建议方案与验收标准。

## 高优先级（先修）

### 1) API 监听实现与异步模型不匹配
- 任务描述: 在异步 Playwright 环境中于同步回调执行 `response.json()`，无法 `await`，存在不可用风险。
- 建议: 将响应处理放入异步任务（如 `asyncio.create_task`）中并 `await response.json()`；或采用 `page.route`/`context.route` 对特定接口统一收集。
- 参考: 63:69:`src/core/extract_orders.py`。
- 验收标准:
  - 能稳定捕获目标订单接口的 JSON；
  - 移除显式时间等待，改为基于任务/事件的完成条件；
  - 多页抓取时无重复/丢失。

### 2) 订单页判定字符串不一致
- 任务描述: 页面判定使用了 `buyer_order_list`（下划线），其它位置使用 `buyer-order-list`（中划线），造成误判。
- 建议: 统一以同一模式匹配（建议共用 `order_page_patterns`）。
- 参考: 996:1004:`src/core/extract_orders.py`；1501:1507:`src/core/extract_orders.py`。
- 验收标准: 在订单页不误跳转；在非订单页能正确导航。

### 3) 监听器累积导致重复处理
- 任务描述: 多页循环中多次注册 `page.on("response", ...)`，未移除，回调累积。
- 建议: 为每次抓取注册临时监听并在该轮结束后移除；或在任务级别只注册一次并做轮次隔离。
- 验收标准: 抓取多页时不会出现重复解析同一响应。

### 4) DOM 抽取过度依赖 Shadow DOM
- 任务描述: 若订单元素无 `shadowRoot` 即跳过，导致 Light DOM 结构时无法抽取。
- 建议: 为 Light DOM 提供同等的字段抽取路径（以元素自身为根搜索）。
- 参考: 654:663:`src/core/extract_orders.py`。
- 验收标准: 不论是否存在 Shadow DOM 均可抽取到记录。

## 中优先级

### 5) 深度选择器性能风险
- 任务描述: `querySelectorDeep` 每次遍历 `document.querySelectorAll('*')` 并递归 shadowRoot，多个选择器重复调用，复杂度高。
- 建议: 先锁定订单容器后再深搜；缓存/复用已发现的 shadowRoot；必要时才穿透。
- 验收标准: 典型页面 DOM 解析耗时显著下降（如 ≥30%）。

### 6) Excel 列处理存在重复列隐患
- 任务描述: 先补目标中文列，再 `rename` 英文→中文，可能造成重名列。
- 建议: 先 `rename`，再补缺失列；或仅对源列补列。
- 参考: 1270:1278:`src/core/extract_orders.py`。
- 验收标准: 输出 Excel 列唯一，无重复/空列。

### 7) 参数未完全生效（行为与说明不一致）
- 任务描述: 默认强制启用多页抓取；未暴露 `--debug-port`；`--headless` 对 CDP 模式无效。
- 建议: 尊重 `--all-pages/--max-pages`；增加 `--debug-port`；明确 `--headless` 语义或移除。
- 参考: 1452:1456:`src/core/extract_orders.py`。
- 验收标准: CLI 标志能真实控制行为，帮助信息与实现一致。

### 8) 异步上下文中的同步 I/O
- 任务描述: `download_image` 在 `async` 函数中使用 `requests` 与 PIL 同步阻塞，影响事件循环。
- 建议: 使用 `asyncio.to_thread` 包裹下载与图像处理，或改用异步 HTTP 客户端（`httpx`/`aiohttp`）。
- 参考: 281:336:`src/core/extract_orders.py`。
- 验收标准: 下载过程不阻塞主事件循环；多图片下载耗时明显下降。

### 9) API 过滤条件过宽
- 任务描述: 使用 `'/order','/trade','/buyer','/list'` 过滤，`/list` 过于宽泛，易误收集。
- 建议: 精确匹配订单接口路径或基于响应 JSON 特征判定。
- 验收标准: 收集到的响应均为目标订单数据，无明显噪声。

## 低优先级

### 10) 统计指标与口径不一致
- 任务描述: `failed` 未维护；`dom_success` 既覆盖又累加；`total_orders` 最终覆盖。
- 建议: 由外层统一汇总；子步骤仅返回结果；失败计数在明确异常处累加。
- 验收标准: 统计项与日志一致，端到端数量自洽。

### 11) 数量解析鲁棒性
- 任务描述: 直接 `parseInt(quantityText)` 对 "x2"、"数量: 2件" 等文本不稳。
- 建议: 先用正则提取数字再转换。
- 验收标准: 常见格式均能正确转为整数。

### 12) 详情链接赋值缺失
- 任务描述: `detail_url` 基本未填充，Excel 列常为空。
- 建议: 从商品标题链接、图片父节点或行操作入口解析填充。
- 验收标准: 详情页列非空率显著提升（如 ≥80%）。

### 13) 图片与超链接同格显示体验
- 任务描述: H 列同时承载图片与文字超链，易被覆盖。
- 建议: 将“原图”/“详情页”超链放相邻列，或增加单独的链接列。
- 验收标准: 用户可直接点击文本链接，图片不遮挡文本。

### 14) 代理/网络设置一致性
- 任务描述: 版本接口请求显式禁用代理，图片下载未禁用，策略不一致。
- 建议: 统一网络策略（代理、超时、重试），并集中配置。
- 验收标准: 在有/无代理环境下行为一致、可控。

### 15) 日志量与性能
- 任务描述: DOM 解析阶段日志较多（逐商品/逐字段输出）。
- 建议: 基于日志级别收敛调试输出，默认 INFO 降低噪声。
- 验收标准: 常规运行日志简洁，DEBUG 模式保留细节。

---

## 建议执行顺序
1) 1、2、3、4（高优先级稳定性问题）
2) 6、7、8、5（正确性与性能）
3) 9、10、11、12、13、14、15（长尾质量与体验）

## 回归与验证建议
- 单页与多页订单抓取用例各 2 个；
- 含 Shadow DOM 与非 Shadow DOM 的页面各 1 个；
- 断网/慢网/图片 404 场景；
- 启用/禁用代理、不同 `--max-pages` 与 `--debug-port`；
- 验证输出 Excel 列名、图片插入、超链接可点击性与统计口径自洽。

---

最后更新：自动生成于审阅 `src/core/extract_orders.py` 后。如需细化到子任务与工时评估，可在本清单基础上拆分为 issue 卡片。


