# -*- coding: utf-8 -*-
"""
1688自动化项目日志工具
统一的日志记录和管理
"""

import logging
import os
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from functools import wraps

from config import Config
from exceptions import AutomationException

class Logger:
    """日志管理器"""
    
    def __init__(self, name: str, log_file: Optional[str] = None):
        self.name = name
        self.log_file = log_file or str(Config.LOG_FILE)
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
            
        # 创建格式化器
        formatter = logging.Formatter(Config.LOG_FORMAT)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        if self.log_file:
            log_dir = Path(self.log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        extra = kwargs.get('extra', {})
        extra.update({
            'timestamp': datetime.now().isoformat(),
            'logger_name': self.name
        })
        self.logger.info(message, extra=extra)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        extra = kwargs.get('extra', {})
        extra.update({
            'timestamp': datetime.now().isoformat(),
            'logger_name': self.name
        })
        self.logger.warning(message, extra=extra)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        extra = kwargs.get('extra', {})
        extra.update({
            'timestamp': datetime.now().isoformat(),
            'logger_name': self.name
        })
        self.logger.error(message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        extra = kwargs.get('extra', {})
        extra.update({
            'timestamp': datetime.now().isoformat(),
            'logger_name': self.name
        })
        self.logger.debug(message, extra=extra)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        extra = kwargs.get('extra', {})
        extra.update({
            'timestamp': datetime.now().isoformat(),
            'logger_name': self.name
        })
        self.logger.critical(message, extra=extra)
    
    def exception(self, message: str, **kwargs):
        """记录异常日志"""
        extra = kwargs.get('extra', {})
        extra.update({
            'timestamp': datetime.now().isoformat(),
            'logger_name': self.name
        })
        self.logger.exception(message, extra=extra)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, logger: Logger):
        self.logger = logger
        self.metrics = {}
        
    def start_timer(self, operation: str):
        """开始计时"""
        self.metrics[operation] = {
            'start_time': datetime.now(),
            'end_time': None,
            'duration': None
        }
    
    def end_timer(self, operation: str):
        """结束计时"""
        if operation in self.metrics:
            self.metrics[operation]['end_time'] = datetime.now()
            duration = (self.metrics[operation]['end_time'] - 
                       self.metrics[operation]['start_time']).total_seconds()
            self.metrics[operation]['duration'] = duration
            
            self.logger.info(f"操作 '{operation}' 完成，耗时: {duration:.2f}秒")
            
            if duration > 10:  # 超过10秒记录警告
                self.logger.warning(f"操作 '{operation}' 耗时过长: {duration:.2f}秒")
    
    def log_metrics(self):
        """记录性能指标"""
        self.logger.info("性能指标汇总:")
        for operation, metric in self.metrics.items():
            if metric['duration'] is not None:
                self.logger.info(f"  {operation}: {metric['duration']:.2f}秒")

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger: Logger):
        self.logger = logger
        self.error_count = 0
        self.error_types = {}
    
    def handle_error(self, error: Exception, context: str = None):
        """处理错误"""
        self.error_count += 1
        
        error_type = type(error).__name__
        self.error_types[error_type] = self.error_types.get(error_type, 0) + 1
        
        error_info = {
            'error_type': error_type,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'error_count': self.error_count
        }
        
        # 记录错误日志
        if isinstance(error, AutomationException):
            self.logger.error(f"自动化错误: {error.message}", extra={'error_info': error_info})
        else:
            self.logger.exception(f"未预期的错误: {str(error)}", extra={'error_info': error_info})
        
        return error_info
    
    def log_error_summary(self):
        """记录错误汇总"""
        self.logger.info(f"错误汇总: 总共 {self.error_count} 个错误")
        for error_type, count in self.error_types.items():
            self.logger.info(f"  {error_type}: {count} 次")

# 装饰器
def log_performance(logger: Logger):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            operation_name = f"{func.__module__}.{func.__name__}"
            logger.info(f"开始执行: {operation_name}")
            
            monitor = PerformanceMonitor(logger)
            monitor.start_timer(operation_name)
            
            try:
                result = func(*args, **kwargs)
                monitor.end_timer(operation_name)
                logger.info(f"成功完成: {operation_name}")
                return result
            except Exception as e:
                monitor.end_timer(operation_name)
                error_handler = ErrorHandler(logger)
                error_handler.handle_error(e, operation_name)
                raise
        
        return wrapper
    return decorator

def handle_errors(logger: Logger):
    """错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            operation_name = f"{func.__module__}.{func.__name__}"
            error_handler = ErrorHandler(logger)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, operation_name)
                raise
        
        return wrapper
    return decorator

# 全局日志实例
def get_logger(name: str) -> Logger:
    """获取日志实例"""
    return Logger(name)

def get_performance_monitor(logger: Logger) -> PerformanceMonitor:
    """获取性能监控器"""
    return PerformanceMonitor(logger)

def get_error_handler(logger: Logger) -> ErrorHandler:
    """获取错误处理器"""
    return ErrorHandler(logger)

# 使用示例
if __name__ == "__main__":
    # 测试日志功能
    logger = get_logger("test_logger")
    
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    logger.debug("这是一条调试日志")
    
    # 测试性能监控
    monitor = get_performance_monitor(logger)
    monitor.start_timer("test_operation")
    import time
    time.sleep(1)
    monitor.end_timer("test_operation")
    monitor.log_metrics()
    
    # 测试错误处理
    error_handler = get_error_handler(logger)
    try:
        raise ValueError("测试错误")
    except Exception as e:
        error_handler.handle_error(e, "测试上下文")
    
    error_handler.log_error_summary()
    
    print("✅ 日志系统测试完成")