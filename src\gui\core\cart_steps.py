"""
采购车模式步骤实现
"""

# 延迟导入tkinter，避免在没有GUI的环境中出错
def _get_tkinter():
    try:
        import tkinter as tk
        from tkinter import filedialog, messagebox
        return tk, filedialog, messagebox
    except ImportError:
        return None, None, None
from typing import Callable, Optional, Any
import threading
import time
import json
import os
from pathlib import Path

from .chrome_manager import ChromeManager
from ..ui.step_manager import BaseStep
from ..utils.file_utils import FileUtils
from ..utils.helpers import format_duration, format_file_size
from ..core.config import Config

class ChromeStep(BaseStep):
    """Chrome启动步骤"""
    
    def __init__(self, mode: str = 'cart'):
        super().__init__("启动Chrome调试模式", "启动Chrome浏览器")
        self.mode = mode
        self.chrome_manager = ChromeManager()
        self.config = Config()
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """执行Chrome启动"""
        def worker():
            try:
                progress_callback(0, "正在启动Chrome浏览器...")
                
                # 检查Chrome是否已安装
                chrome_path = self.chrome_manager.find_chrome_path()
                if not chrome_path:
                    self.error_message = "未找到Chrome浏览器，请确保已安装Chrome"
                    progress_callback(-1, self.error_message, None)
                    return
                
                progress_callback(20, f"找到Chrome：{chrome_path}")
                
                # 检查Chrome是否已经在运行
                if self.chrome_manager.test_connection():
                    progress_callback(50, "Chrome已在运行，正在连接...")
                    progress_callback(100, "Chrome连接成功，请在Chrome中登录1688账号", None)
                    self.is_completed = True
                    return
                
                # 终止现有Chrome进程
                progress_callback(40, "正在清理现有Chrome进程...")
                self.chrome_manager.kill_chrome_processes()
                time.sleep(2)
                
                # 启动Chrome
                progress_callback(60, "正在启动Chrome浏览器...")
                success = self.chrome_manager.start_chrome()
                
                if not success:
                    self.error_message = "Chrome启动失败，请检查Chrome是否正常安装"
                    progress_callback(-1, self.error_message, None)
                    return
                
                progress_callback(80, "正在验证Chrome连接...")
                
                # 等待Chrome完全启动
                time.sleep(3)
                
                # 测试连接
                if self.chrome_manager.test_connection():
                    progress_callback(100, "Chrome启动成功，请在Chrome中登录1688账号", None)
                    self.is_completed = True
                else:
                    self.error_message = "Chrome连接失败，请手动启动Chrome调试模式"
                    progress_callback(-1, self.error_message, None)
                
            except Exception as e:
                self.error_message = f"Chrome启动失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

class ExtractCartDataStep(BaseStep):
    """采购车数据提取步骤"""
    
    def __init__(self):
        super().__init__("提取采购车数据", "从1688采购车页面提取数据")
        self.chrome_manager = ChromeManager()
        self.extracted_data = None
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """提取采购车数据"""
        def worker():
            try:
                progress_callback(0, "正在连接Chrome...")
                
                # 检查Chrome连接
                if not self.chrome_manager.test_connection():
                    self.error_message = "无法连接到Chrome，请确保Chrome已启动调试模式"
                    progress_callback(-1, self.error_message, None)
                    return
                
                progress_callback(20, "正在获取Chrome标签页...")
                
                # 获取标签页列表
                tabs = self.chrome_manager.get_chrome_tabs()
                cart_tab = None
                
                # 查找采购车页面
                for tab in tabs:
                    if 'cart.1688.com' in tab.get('url', ''):
                        cart_tab = tab
                        break
                
                if not cart_tab:
                    self.error_message = "未找到1688采购车页面，请在Chrome中打开采购车页面"
                    progress_callback(-1, self.error_message, None)
                    return
                
                progress_callback(40, "正在激活采购车页面...")
                
                # 激活采购车标签页
                tab_id = cart_tab['id']
                
                # 导航到采购车页面
                self.chrome_manager.navigate_to(tab_id, "https://cart.1688.com/cart.htm")
                time.sleep(3)
                
                progress_callback(60, "正在提取采购车数据...")
                
                # 提取数据
                cart_data = self.extract_cart_data_from_page(tab_id)
                
                if cart_data:
                    progress_callback(80, f"成功提取{len(cart_data)}条商品数据")
                    
                    # 保存数据
                    self.save_extracted_data(cart_data)
                    
                    progress_callback(100, f"数据提取完成，共{len(cart_data)}条商品记录", cart_data)
                    self.extracted_data = cart_data
                    self.is_completed = True
                else:
                    self.error_message = "未提取到采购车数据，请确保采购车页面已完全加载"
                    progress_callback(-1, self.error_message, None)
                
            except Exception as e:
                self.error_message = f"数据提取失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def extract_cart_data_from_page(self, tab_id: str) -> Optional[list]:
        """从页面提取采购车数据"""
        try:
            # JavaScript代码提取采购车数据
            js_code = '''
            (function() {
                try {
                    // 等待页面加载
                    if (document.readyState !== 'complete') {
                        return { error: "页面未完全加载" };
                    }
                    
                    // 查找商品元素
                    var cartItems = document.querySelectorAll('.cart-item, .item, [class*="cart"], [class*="item"]');
                    var products = [];
                    
                    cartItems.forEach(function(item, index) {
                        try {
                            // 提取商品名称
                            var nameElement = item.querySelector('.name, .title, .product-name, [class*="name"], [class*="title"]') || 
                                             item.querySelector('a') ||
                                             item.querySelector('h3, h4, h5');
                            var name = nameElement ? nameElement.textContent.trim() : '';
                            
                            // 提取商品价格
                            var priceElement = item.querySelector('.price, .money, [class*="price"], [class*="money"]') ||
                                             item.querySelector('[class*="amount"], [class*="cost"]');
                            var price = priceElement ? priceElement.textContent.trim() : '';
                            
                            // 提取商品数量
                            var quantityElement = item.querySelector('.quantity, .num, [class*="quantity"], [class*="num"]') ||
                                                 item.querySelector('input[type="number"]');
                            var quantity = quantityElement ? quantityElement.textContent.trim() : '1';
                            
                            // 提取商品图片
                            var imgElement = item.querySelector('img');
                            var imageUrl = imgElement ? imgElement.src : '';
                            
                            // 提取商品链接
                            var linkElement = item.querySelector('a');
                            var productUrl = linkElement ? linkElement.href : '';
                            
                            // 提取供应商信息
                            var supplierElement = item.querySelector('.shop, .store, [class*="shop"], [class*="store"]') ||
                                                 item.querySelector('.supplier, [class*="supplier"]');
                            var supplier = supplierElement ? supplierElement.textContent.trim() : '';
                            
                            if (name) {
                                products.push({
                                    index: index + 1,
                                    name: name,
                                    price: price,
                                    quantity: quantity,
                                    imageUrl: imageUrl,
                                    productUrl: productUrl,
                                    supplier: supplier
                                });
                            }
                        } catch (e) {
                            console.log('提取商品信息失败:', e);
                        }
                    });
                    
                    if (products.length === 0) {
                        // 尝试其他选择器
                        var allElements = document.querySelectorAll('*');
                        var potentialProducts = [];
                        
                        allElements.forEach(function(el) {
                            var text = el.textContent.trim();
                            if (text && text.length > 10 && text.length < 200) {
                                // 可能是商品名称
                                var hasPriceNearby = false;
                                var parent = el.parentElement;
                                for (var i = 0; i < 3 && parent; i++) {
                                    var parentText = parent.textContent;
                                    if (parentText.includes('¥') || parentText.includes('元') || parentText.includes('$')) {
                                        hasPriceNearby = true;
                                        break;
                                    }
                                    parent = parent.parentElement;
                                }
                                
                                if (hasPriceNearby) {
                                    potentialProducts.push({
                                        index: potentialProducts.length + 1,
                                        name: text,
                                        price: '',
                                        quantity: '1',
                                        imageUrl: '',
                                        productUrl: '',
                                        supplier: ''
                                    });
                                }
                            }
                        });
                        
                        products = potentialProducts.slice(0, 50); // 限制数量
                    }
                    
                    return {
                        success: true,
                        products: products,
                        count: products.length,
                        timestamp: new Date().toISOString()
                    };
                    
                } catch (e) {
                    return { error: e.toString() };
                }
            })();
            '''
            
            result = self.chrome_manager.execute_javascript(tab_id, js_code)
            
            if result and isinstance(result, dict):
                if 'error' in result:
                    print(f"提取数据错误：{result['error']}")
                    return None
                
                if result.get('success') and result.get('products'):
                    return result['products']
            
            return None
            
        except Exception as e:
            print(f"提取采购车数据失败：{e}")
            return None
    
    def save_extracted_data(self, cart_data: list):
        """保存提取的数据"""
        try:
            # 创建数据目录
            data_dir = Path(__file__).parent.parent / "data"
            data_dir.mkdir(exist_ok=True)
            
            # 保存JSON文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            json_file = data_dir / f"cart_data_{timestamp}.json"
            
            save_data = {
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
                "source": "1688_cart",
                "total_products": len(cart_data),
                "products": cart_data
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            print(f"采购车数据已保存到：{json_file}")
            
        except Exception as e:
            print(f"保存数据失败：{e}")

class ProcessCartDataStep(BaseStep):
    """处理采购车数据步骤"""
    
    def __init__(self):
        super().__init__("处理和增强数据", "处理数据并下载商品图片")
        self.processed_data = None
        self.config = Config()
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """处理数据"""
        def worker():
            try:
                progress_callback(0, "正在准备处理数据...")
                
                # 获取上一步的数据
                # 这里应该从上一步获取数据，现在模拟数据
                cart_data = self.get_cart_data()
                
                if not cart_data:
                    self.error_message = "未找到采购车数据，请先提取数据"
                    progress_callback(-1, self.error_message, None)
                    return
                
                total_items = len(cart_data)
                processed_items = []
                
                progress_callback(10, f"开始处理{total_items}条商品数据")
                
                for i, item in enumerate(cart_data):
                    try:
                        # 处理单个商品
                        processed_item = self.process_single_item(item)
                        processed_items.append(processed_item)
                        
                        # 下载商品图片
                        if item.get('imageUrl'):
                            self.download_product_image(item['imageUrl'], i)
                        
                        # 更新进度
                        progress = 10 + (i + 1) / total_items * 80
                        progress_callback(progress, f"正在处理第{i+1}/{total_items}个商品...")
                        
                        # 添加小延迟避免过快请求
                        time.sleep(0.1)
                        
                    except Exception as e:
                        print(f"处理商品{i+1}失败：{e}")
                        # 添加失败的商品到结果中
                        processed_items.append({
                            **item,
                            "error": str(e),
                            "imageDownloaded": False
                        })
                
                progress_callback(90, "正在整理处理结果...")
                
                # 保存处理结果
                self.processed_data = processed_items
                self.save_processed_data(processed_items)
                
                success_count = len([item for item in processed_items if not item.get('error')])
                progress_callback(100, f"数据处理完成，成功处理{success_count}/{total_items}个商品", processed_items)
                self.is_completed = True
                
            except Exception as e:
                self.error_message = f"数据处理失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def get_cart_data(self) -> Optional[list]:
        """获取采购车数据"""
        try:
            # 从数据文件中读取
            data_dir = Path(__file__).parent.parent / "data"
            json_files = list(data_dir.glob("cart_data_*.json"))
            
            if json_files:
                # 读取最新的文件
                latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('products', [])
            
            return None
            
        except Exception as e:
            print(f"获取采购车数据失败：{e}")
            return None
    
    def process_single_item(self, item: dict) -> dict:
        """处理单个商品"""
        processed = item.copy()
        
        # 标准化价格
        price = item.get('price', '0')
        if isinstance(price, str):
            # 提取数字
            import re
            price_match = re.search(r'[\d,]+\.?\d*', price)
            if price_match:
                processed['price'] = price_match.group().replace(',', '')
            else:
                processed['price'] = '0'
        
        # 标准化数量
        quantity = item.get('quantity', '1')
        if isinstance(quantity, str):
            import re
            quantity_match = re.search(r'\d+', quantity)
            if quantity_match:
                processed['quantity'] = quantity_match.group()
            else:
                processed['quantity'] = '1'
        
        # 添加处理时间戳
        processed['processedAt'] = time.strftime("%Y-%m-%dT%H:%M:%S")
        
        return processed
    
    def download_product_image(self, image_url: str, index: int):
        """下载商品图片"""
        try:
            import requests
            from pathlib import Path
            
            # 创建缓存目录
            cache_dir = Path(__file__).parent.parent / "cache" / "images"
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            file_extension = Path(image_url).suffix.lower()
            if not file_extension:
                file_extension = '.jpg'
            
            filename = f"product_{index:04d}{file_extension}"
            file_path = cache_dir / filename
            
            # 下载图片
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"图片下载成功：{filename}")
            
        except Exception as e:
            print(f"下载图片失败：{e}")
    
    def save_processed_data(self, processed_data: list):
        """保存处理后的数据"""
        try:
            # 创建数据目录
            data_dir = Path(__file__).parent.parent / "data"
            data_dir.mkdir(exist_ok=True)
            
            # 保存JSON文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            json_file = data_dir / f"processed_cart_data_{timestamp}.json"
            
            save_data = {
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
                "source": "1688_cart_processed",
                "total_products": len(processed_data),
                "products": processed_data
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            print(f"处理后的数据已保存到：{json_file}")
            
        except Exception as e:
            print(f"保存处理数据失败：{e}")

class SaveCartResultStep(BaseStep):
    """保存采购车结果步骤"""
    
    def __init__(self):
        super().__init__("保存结果", "保存Excel报告文件")
        self.saved_file_path = None
        self.config = Config()
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """保存结果"""
        def worker():
            try:
                progress_callback(0, "正在准备保存文件...")
                
                # 获取处理后的数据
                processed_data = self.get_processed_data()
                
                if not processed_data:
                    self.error_message = "未找到处理后的数据，请先处理数据"
                    progress_callback(-1, self.error_message, None)
                    return
                
                progress_callback(20, "正在生成Excel文件...")
                
                # 生成Excel文件
                excel_file = self.generate_excel_file(processed_data)
                
                if excel_file:
                    progress_callback(80, "正在保存文件...")
                    
                    # 保存文件
                    self.saved_file_path = excel_file
                    
                    progress_callback(100, f"文件已保存：{excel_file}", excel_file)
                    self.is_completed = True
                else:
                    self.error_message = "生成Excel文件失败"
                    progress_callback(-1, self.error_message, None)
                
            except Exception as e:
                self.error_message = f"保存文件失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def get_processed_data(self) -> Optional[list]:
        """获取处理后的数据"""
        try:
            # 从数据文件中读取
            data_dir = Path(__file__).parent.parent / "data"
            json_files = list(data_dir.glob("processed_cart_data_*.json"))
            
            if json_files:
                # 读取最新的文件
                latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('products', [])
            
            return None
            
        except Exception as e:
            print(f"获取处理数据失败：{e}")
            return None
    
    def generate_excel_file(self, processed_data: list) -> Optional[str]:
        """生成Excel文件"""
        try:
            import pandas as pd
            from datetime import datetime
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"采购车数据_{timestamp}.xlsx"
            
            # 选择保存路径
            file_path = FileUtils.select_save_file(
                title="保存采购车数据",
                default_name=default_filename
            )
            
            if not file_path:
                return None
            
            # 准备数据
            df_data = []
            for item in processed_data:
                df_item = {
                    "序号": item.get('index', ''),
                    "商品名称": item.get('name', ''),
                    "价格": item.get('price', ''),
                    "数量": item.get('quantity', ''),
                    "供应商": item.get('supplier', ''),
                    "商品链接": item.get('productUrl', ''),
                    "图片链接": item.get('imageUrl', ''),
                    "处理时间": item.get('processedAt', ''),
                    "状态": "成功" if not item.get('error') else "失败"
                }
                
                if item.get('error'):
                    df_item["错误信息"] = item['error']
                
                df_data.append(df_item)
            
            # 创建DataFrame
            df = pd.DataFrame(df_data)
            
            # 保存Excel文件
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            print(f"Excel文件已保存：{file_path}")
            return file_path
            
        except Exception as e:
            print(f"生成Excel文件失败：{e}")
            return None