#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试展开更多商品功能
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.extract_orders import OrderDataExtractor

async def test_expand_functionality():
    """测试展开更多商品功能"""
    print("🧪 开始测试展开更多商品功能...")
    
    # 创建模拟HTML页面来测试展开功能
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试展开功能</title>
        <style>
            .order-item { border: 1px solid #ccc; margin: 10px; padding: 10px; }
            .company-name { font-weight: bold; color: #333; }
            .expend-btn { cursor: pointer; color: blue; text-decoration: underline; }
            .hidden-products { display: none; }
            .product-entry { border: 1px solid #eee; margin: 5px; padding: 5px; }
        </style>
    </head>
    <body>
        <div class="order-list-container">
            <!-- 订单1 -->
            <order-item data-tracker="12345678901234567">
                <template shadowrootmode="open">
                    <div class="order-item">
                        <div class="company-name">开封市禹王台区臻颂日用百货店</div>
                        <div class="order-time">2024-01-15 10:30:00</div>
                        
                        <!-- 默认显示的商品 -->
                        <order-item-entry-product>
                            <template shadowrootmode="open">
                                <div class="product-entry">
                                    <div class="product-name">测试商品1</div>
                                    <div class="sku-info-item">红色</div>
                                    <div class="sku-info-item">L码</div>
                                    <div class="actual-unit-price">￥29.90</div>
                                    <div class="quantity-amount">2</div>
                                    <img src="https://example.com/image1.jpg" alt="商品图片">
                                </div>
                            </template>
                        </order-item-entry-product>
                        
                        <!-- 展开按钮 -->
                        <span class="expend-btn" onclick="expandMore(this)">
                            展开更多<q-icon type="down" size="12"></q-icon>
                        </span>
                        
                        <!-- 隐藏的商品（点击展开后显示） -->
                        <div class="hidden-products">
                            <order-item-entry-product>
                                <template shadowrootmode="open">
                                    <div class="product-entry">
                                        <div class="product-name">测试商品2</div>
                                        <div class="sku-info-item">蓝色</div>
                                        <div class="actual-unit-price">￥45.00</div>
                                        <div class="quantity-amount">1</div>
                                        <img src="https://example.com/image2.jpg" alt="商品图片">
                                    </div>
                                </template>
                            </order-item-entry-product>
                        </div>
                    </div>
                </template>
            </order-item>
            
            <!-- 订单2 -->
            <order-item data-tracker="98765432109876543">
                <template shadowrootmode="open">
                    <div class="order-item">
                        <div class="company-name">测试供应商B</div>
                        <div class="order-time">2024-01-16 14:20:00</div>
                        
                        <order-item-entry-product>
                            <template shadowrootmode="open">
                                <div class="product-entry">
                                    <div class="product-name">测试商品3</div>
                                    <div class="sku-info-item">绿色</div>
                                    <div class="sku-info-item">M码</div>
                                    <div class="actual-unit-price">￥35.50</div>
                                    <div class="quantity-amount">3</div>
                                    <img src="https://example.com/image3.jpg" alt="商品图片">
                                </div>
                            </template>
                        </order-item-entry-product>
                    </div>
                </template>
            </order-item>
        </div>
        
        <script>
            function expandMore(btn) {
                const hiddenProducts = btn.parentElement.querySelector('.hidden-products');
                if (hiddenProducts) {
                    hiddenProducts.style.display = 'block';
                    btn.textContent = '收起';
                }
            }
        </script>
    </body>
    </html>
    """
    
    # 使用Playwright测试
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # 设置HTML内容
        await page.set_content(test_html)
        await page.wait_for_timeout(1000)
        
        # 创建提取器并测试展开功能
        extractor = OrderDataExtractor()
        
        print("📊 测试展开功能...")
        await extractor.expand_all_order_items(page)
        
        print("📊 测试DOM提取...")
        products = await extractor.extract_orders_from_dom(page)
        
        await browser.close()
        
        print(f"✅ 提取完成，找到 {len(products)} 个商品SKU")
        
        # 显示提取结果
        for i, product in enumerate(products):
            print(f"\n商品 {i+1}:")
            print(f"  订单号: {product.get('order_id', 'N/A')}")
            print(f"  商品名称: {product.get('title', 'N/A')}")
            print(f"  商品规格: {product.get('product_specs', 'N/A')}")
            print(f"  单价: {product.get('price', 'N/A')}")
            print(f"  数量: {product.get('quantity', 'N/A')}")
            print(f"  卖家: {product.get('shop_name', 'N/A')}")
            print(f"  图片: {product.get('image_url', 'N/A')}")

if __name__ == "__main__":
    asyncio.run(test_expand_functionality())
