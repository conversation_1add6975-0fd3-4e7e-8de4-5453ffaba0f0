#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版订单数据验证脚本
"""

import json
import os
import sys
import pandas as pd

# 设置控制台编码
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())


def validate_project_structure():
    """验证项目结构"""
    print("[VALIDATION] 验证项目结构...")
    
    required_files = [
        'export_cart_excel.py',
        'extract_orders.py',
        'enrich_orders_with_images.py',
        'start_debug_chrome.bat',
        'requirements.txt'
    ]
    
    required_dirs = [
        'data',
        'cache',
        'reports',
        '@Docs'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    for dir in required_dirs:
        if not os.path.exists(dir):
            missing_dirs.append(dir)
    
    if missing_files:
        print(f"[ERROR] 缺少文件: {missing_files}")
    
    if missing_dirs:
        print(f"[ERROR] 缺少目录: {missing_dirs}")
    
    if not missing_files and not missing_dirs:
        print("[OK] 项目结构完整")
        return True
    
    return False


def validate_core_functionality():
    """验证核心功能"""
    print("\n[VALIDATION] 验证核心功能...")
    
    # 检查主要脚本是否可读
    scripts = [
        'export_cart_excel.py',
        'extract_orders.py',
        'enrich_orders_with_images.py'
    ]
    
    for script in scripts:
        try:
            with open(script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基本语法检查
            if 'import' in content and 'def' in content:
                print(f"[OK] {script} - 基本结构正常")
            else:
                print(f"[WARNING] {script} - 结构可能不完整")
                
        except Exception as e:
            print(f"[ERROR] {script} - 读取失败: {e}")
    
    return True


def validate_dependencies():
    """验证依赖"""
    print("\n[VALIDATION] 验证依赖...")
    
    if not os.path.exists('requirements.txt'):
        print("[ERROR] requirements.txt 不存在")
        return False
    
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            deps = f.read().strip().split('\n')
        
        print(f"[OK] 发现 {len(deps)} 个依赖:")
        for dep in deps:
            print(f"  - {dep}")
        
        return True
    except Exception as e:
        print(f"[ERROR] 读取依赖失败: {e}")
        return False


def validate_documentation():
    """验证文档"""
    print("\n[VALIDATION] 验证文档...")
    
    docs = [
        '@Docs/使用指南.md',
        '@Docs/1688订单页爬取方法.md',
        'README.md',
        'CLAUDE.md'
    ]
    
    for doc in docs:
        if os.path.exists(doc):
            print(f"[OK] {doc}")
        else:
            print(f"[WARNING] {doc} 不存在")
    
    return True


def main():
    """主验证函数"""
    print("=== 项目验证报告 ===")
    
    validation_results = []
    
    # 执行各项验证
    validation_results.append(("项目结构", validate_project_structure()))
    validation_results.append(("核心功能", validate_core_functionality()))
    validation_results.append(("依赖管理", validate_dependencies()))
    validation_results.append(("文档完整性", validate_documentation()))
    
    print("\n=== 验证结果汇总 ===")
    
    passed_count = 0
    for test_name, passed in validation_results:
        status = "[PASS]" if passed else "[FAIL]"
        print(f"{test_name}: {status}")
        if passed:
            passed_count += 1
    
    total_score = (passed_count / len(validation_results)) * 100
    print(f"\n总体得分: {total_score:.1f}% ({passed_count}/{len(validation_results)})")
    
    if total_score >= 80:
        print("\n[SUCCESS] 项目验证通过！")
        print("\n建议的后续操作:")
        print("1. 运行 python validate_optimization.py 验证采购车功能")
        print("2. 运行 python export_cart_excel.py 测试数据提取")
        print("3. 运行 python extract_orders.py 测试订单抓取")
    else:
        print("\n[WARNING] 项目验证未完全通过，建议修复相关问题")
    
    return total_score >= 80


if __name__ == "__main__":
    main()