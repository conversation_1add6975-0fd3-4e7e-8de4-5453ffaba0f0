@echo off
REM 1688自动化项目环境启动脚本
REM 此脚本会激活独立的conda环境并运行项目

echo 正在激活1688自动化项目环境...

REM 激活conda环境
call E:\miniconda\Scripts\activate.bat 1688_automation

echo 环境已激活！
echo Python版本：
python --version

echo.
echo 可用的包：
python -c "import sys; print('Python:', sys.version.split()[0])"
python -c "from PyQt6.QtWidgets import QApplication; print('PyQt6: 已安装')"
python -c "import tkinter; print('tkinter: 已安装')"
python -c "import pandas, openpyxl, requests, PIL; print('pandas, openpyxl, requests, PIL: 已安装')"
python -c "import playwright; print('playwright: 已安装')"

echo.
echo 现在可以运行项目了！
echo 例如：
echo   python main_app.py
echo   python export_cart_excel.py
echo   python ui_app/qt_real.py

REM 保持命令行窗口打开
cmd /k