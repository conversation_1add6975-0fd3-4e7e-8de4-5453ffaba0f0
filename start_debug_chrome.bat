@echo off
echo Starting Chrome Debug Mode...
echo.

echo Checking for processes using port 9222...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :9222 ^| findstr LISTENING') do (
    echo Found process %%a using port 9222, terminating...
    taskkill /F /PID %%a 2>nul
)
timeout /t 2 >nul 2>&1

echo Starting Chrome with debug port...
echo In Chrome, please:
echo 1. Login to 1688.com
echo 2. Navigate to cart page
echo 3. Ensure products and username are visible
echo.

start chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug_1688" --disable-web-security --no-first-run --no-default-browser-check

echo.
echo [OK] Chrome started, debug port: 9222
echo [TIP] Complete login and navigation, then return to Python script and press Enter to continue
echo.
timeout /t 10 >nul 2>&1
