#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688订单数据增强工具 - 从Excel中提取订单信息，匹配商品图片和详情链接
支持多种匹配策略：offerId匹配、URL匹配、模糊标题匹配
"""

import json
import re
import os
import asyncio
import aiohttp
import pandas as pd
import openpyxl
from openpyxl.drawing.image import Image as ExcelImage
from openpyxl.styles import Font, PatternFill, Alignment
from PIL import Image
from io import BytesIO
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
import difflib
import hashlib
from pathlib import Path

# 设置控制台编码
import sys
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 定义项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

@dataclass
class ProductMatch:
    """商品匹配结果"""
    offer_id: Optional[str] = None
    title: str = ""
    detail_url: str = ""
    image_url: str = ""
    supplier: str = ""
    match_type: str = ""  # offer_id, url, title, none
    confidence: float = 0.0


class ImageCache:
    """图片缓存管理"""

    def __init__(self, cache_dir: Optional[str] = None):
        if cache_dir:
            self.cache_dir = Path(cache_dir)
        else:
            self.cache_dir = PROJECT_ROOT / "cache" / "images"
        os.makedirs(self.cache_dir, exist_ok=True)

    def get_cache_path(self, url: str) -> str:
        """获取图片缓存路径"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        ext = self._get_image_ext(url)
        return os.path.join(self.cache_dir, f"{url_hash}.{ext}")

    def _get_image_ext(self, url: str) -> str:
        """从URL获取图片扩展名"""
        if '.jpg' in url.lower() or '.jpeg' in url.lower():
            return 'jpg'
        elif '.png' in url.lower():
            return 'png'
        elif '.webp' in url.lower():
            return 'webp'
        else:
            return 'jpg'

    def is_cached(self, url: str) -> bool:
        """检查图片是否已缓存"""
        return os.path.exists(self.get_cache_path(url))

    def get_cached_image(self, url: str) -> Optional[bytes]:
        """获取缓存的图片数据"""
        cache_path = self.get_cache_path(url)
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    return f.read()
            except Exception:
                return None
        return None

    def cache_image(self, url: str, image_data: bytes) -> str:
        """缓存图片数据"""
        cache_path = self.get_cache_path(url)
        try:
            with open(cache_path, 'wb') as f:
                f.write(image_data)
            return cache_path
        except Exception:
            return ""


class OrderEnricher:
    """订单数据增强器"""

    def __init__(self):
        self.cache = ImageCache()
        self.session = None
        self.cart_data = {}
        self.stats = {
            'total_orders': 0,
            'matched_by_offer_id': 0,
            'matched_by_url': 0,
            'matched_by_title': 0,
            'no_match': 0,
            'images_downloaded': 0,
            'images_cached': 0
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': (
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                    'AppleWebKit/537.36 (KHTML, like Gecko) '
                    'Chrome/91.0.4472.124 Safari/537.36'
                )
            }
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    def load_cart_data(self, cart_data_path: str):
        """加载采购车数据"""
        try:
            with open(cart_data_path, 'r', encoding='utf-8') as f:
                self.cart_data = json.load(f)
            print(f"✅ 成功加载采购车数据: {len(self.cart_data.get('商品数据', []))} 个商品")
        except Exception as e:
            print(f"❌ 加载采购车数据失败: {e}")
            self.cart_data = {}

    def extract_offer_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取offerId"""
        if not url:
            return None

        patterns = [
            r'offer/(\d+)\.html',
            r'offerId=(\d+)',
            r'id=(\d+)',
            r'offer/(\d+)',
            r'/(\d{7,})'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None

    def match_by_offer_id(
            self, order_title: str, order_url: str = "" 
    ) -> Optional[ProductMatch]:
        """通过offerId匹配商品"""
        if not self.cart_data or '商品数据' not in self.cart_data:
            return None

        # 尝试从订单URL提取offerId
        order_offer_id = self.extract_offer_id_from_url(order_url)

        if order_offer_id:
            # 在采购车数据中查找相同offerId的商品
            for product in self.cart_data['商品数据']:
                product_offer_id = self.extract_offer_id_from_url(
                    product.get('商品链接', '')
                )
                if product_offer_id == order_offer_id:
                    return ProductMatch(
                        offer_id=order_offer_id,
                        title=product.get('品名', ''),
                        detail_url=product.get('商品链接', ''),
                        image_url=product.get('图片URL', ''),
                        supplier=product.get('生产商名称', ''),
                        match_type='offer_id',
                        confidence=1.0
                    )

        return None

    def match_by_url(self, order_url: str) -> Optional[ProductMatch]:
        """通过URL匹配商品"""
        if not order_url or not self.cart_data or '商品数据' not in self.cart_data:
            return None

        # 标准化URL进行比较
        order_url_clean = order_url.split('?')[0].rstrip('/')

        for product in self.cart_data['商品数据']:
            product_url = product.get('商品链接', '')
            if not product_url:
                continue

            product_url_clean = product_url.split('?')[0].rstrip('/')

            if order_url_clean == product_url_clean:
                return ProductMatch(
                    offer_id=self.extract_offer_id_from_url(product_url),
                    title=product.get('品名', ''),
                    detail_url=product_url,
                    image_url=product.get('图片URL', ''),
                    supplier=product.get('生产商名称', ''),
                    match_type='url',
                    confidence=0.95
                )

        return None

    def match_by_title(self, order_title: str) -> Optional[ProductMatch]:
        """通过标题模糊匹配商品"""
        if not order_title or not self.cart_data or '商品数据' not in self.cart_data:
            return None

        best_match = None
        best_ratio = 0.7  # 最低匹配阈值

        for product in self.cart_data['商品数据']:
            product_title = product.get('品名', '')
            if not product_title:
                continue

            # 计算标题相似度
            ratio = difflib.SequenceMatcher(
                None, order_title.lower(), product_title.lower()
            ).ratio()

            if ratio > best_ratio and ratio > best_ratio:
                best_ratio = ratio
                best_match = ProductMatch(
                    offer_id=self.extract_offer_id_from_url(product.get('商品链接', '')),
                    title=product_title,
                    detail_url=product.get('商品链接', ''),
                    image_url=product.get('图片URL', ''),
                    supplier=product.get('生产商名称', ''),
                    match_type='title',
                    confidence=ratio
                )

        return best_match

    async def download_image(self, url: str) -> Optional[bytes]:
        """下载图片"""
        if not url:
            return None

        # 检查缓存
        cached_data = self.cache.get_cached_image(url)
        if cached_data:
            self.stats['images_cached'] += 1
            return cached_data

        try:
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    image_data = await response.read()
                    # 缓存图片
                    self.cache.cache_image(url, image_data)
                    self.stats['images_downloaded'] += 1
                    return image_data
        except Exception as e:
            print(f"⚠️ 下载图片失败 {url}: {e}")

        return None

    def resize_image(
            self, image_data: bytes, max_size: Tuple[int, int] = (120, 120)
    ) -> bytes:
        """调整图片大小"""
        try:
            img = Image.open(BytesIO(image_data))
            img.thumbnail(max_size, Image.Resampling.LANCZOS)

            output = BytesIO()
            img.save(output, format='JPEG', quality=85)
            return output.getvalue()
        except Exception as e:
            print(f"⚠️ 调整图片大小失败: {e}")
            return image_data

    async def find_best_match(
            self, order_title: str, order_url: str = "" 
    ) -> ProductMatch:
        """查找最佳匹配"""
        # 1. 尝试offerId匹配
        match = self.match_by_offer_id(order_title, order_url)
        if match:
            self.stats['matched_by_offer_id'] += 1
            return match

        # 2. 尝试URL匹配
        match = self.match_by_url(order_url)
        if match:
            self.stats['matched_by_url'] += 1
            return match

        # 3. 尝试标题匹配
        match = self.match_by_title(
            order_title
        )
        if match:
            self.stats['matched_by_title'] += 1
            return match

        # 无匹配
        self.stats['no_match'] += 1
        return ProductMatch(match_type='none', confidence=0.0)

    async def process_order(
            self, row: Dict[str, any]
    ) -> Dict[str, any]:
        """处理单个订单"""
        enriched_row = row.copy()

        # 提取订单信息
        order_title = (row.get('商品名称', '') or row.get('品名', '')
                       or row.get('产品名称', ''))
        order_url = (row.get('商品链接', '') or row.get('详情链接', '')
                     or row.get('链接', ''))

        if not order_title:
            return enriched_row

        # 查找匹配
        match = await self.find_best_match(order_title, order_url)

        # 添加匹配结果
        enriched_row['图片原图'] = match.image_url if match.image_url else ""
        enriched_row['详情链接'] = match.detail_url if match.detail_url else order_url
        enriched_row['匹配方式'] = match.match_type
        enriched_row['匹配置信度'] = f"{match.confidence:.2f}"
        enriched_row['供应商'] = (
            match.supplier if match.supplier
            else row.get('供应商', '')
        )

        # 下载并处理图片
        if match.image_url:
            image_data = await self.download_image(match.image_url)
            if image_data:
                resized_image = self.resize_image(image_data)
                enriched_row['商品图片数据'] = resized_image

        return enriched_row

    async def enrich_orders(
            self, input_path: str, output_path: str,
            cart_data_path: Optional[str] = None):
        """增强订单数据"""
        # 加载采购车数据
        if cart_data_path and os.path.exists(cart_data_path):
            self.load_cart_data(cart_data_path)

        # 读取Excel文件
        print(f"📖 读取订单文件: {input_path}")
        df = pd.read_excel(input_path)
        self.stats['total_orders'] = len(df)

        print(f"📊 开始处理 {len(df)} 个订单...")

        # 处理每个订单
        enriched_rows = []
        for index, row in df.iterrows():
            if index % 10 == 0:
                print(f"⏳ 处理进度: {index}/{len(df)}")

            enriched_row = await self.process_order(row.to_dict())
            enriched_rows.append(enriched_row)

        # 创建新的DataFrame
        enriched_df = pd.DataFrame(enriched_rows)

        # 保存到Excel
        await self.save_to_excel(enriched_df, output_path)

        # 打印统计信息
        self.print_stats()

    async def save_to_excel(self, df: pd.DataFrame, output_path: str):
        """保存到Excel文件"""
        print(f"💾 保存到Excel: {output_path}")

        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "订单数据"

        # 写入表头
        columns = df.columns.tolist()
        for col_idx, col_name in enumerate(columns, 1):
            cell = ws.cell(row=1, column=col_idx, value=col_name)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(
                start_color="CCCCCC", end_color="CCCCCC", fill_type="solid"
            )
            cell.alignment = Alignment(horizontal="center")

        # 写入数据
        for row_idx, row in enumerate(df.itertuples(index=False), 2):
            for col_idx, value in enumerate(row, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)

                # 如果是图片数据，插入图片
                if (
                    isinstance(value, bytes) and len(value) > 100 and
                    col_idx > 0
                ):
                    try:
                        img = ExcelImage(BytesIO(value))
                        img.width = 80
                        img.height = 80
                        ws.add_image(img, cell.coordinate)
                    except Exception as e:
                        print(f"⚠️ 插入图片失败: {e}")

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (ValueError, TypeError):
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(output_path)
        print(f"✅ Excel文件保存成功: {output_path}")

    def print_stats(self):
        """打印统计信息"""
        print("\n📊 处理统计:")
        print(f"  总订单数: {self.stats['total_orders']}")
        print(f"  offerId匹配: {self.stats['matched_by_offer_id']}")
        print(f"  URL匹配: {self.stats['matched_by_url']}")
        print(f"  标题匹配: {self.stats['matched_by_title']}")
        print(f"  无匹配: {self.stats['no_match']}")
        print(f"  图片下载: {self.stats['images_downloaded']}")
        print(f"  图片缓存: {self.stats['images_cached']}")

        if self.stats['total_orders'] > 0:
            match_rate = (
                (
                    self.stats['matched_by_offer_id'] +
                    self.stats['matched_by_url'] +
                    self.stats['matched_by_title']
                ) / self.stats['total_orders']
            ) * 100
            print(f"  匹配率: {match_rate:.1f}%")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='1688订单数据增强工具')
    parser.add_argument('--input', '-i', required=True, help='输入Excel文件路径')
    parser.add_argument('--output', '-o', help='输出Excel文件路径（默认：添加_enriched后缀）')
    parser.add_argument('--cart-data', '-c', help='采购车数据文件路径')

    args = parser.parse_args()

    # 设置输出路径
    if not args.output:
        base_name = os.path.splitext(args.input)[0]
        args.output = f"{base_name}_enriched.xlsx"

    print("🚀 开始1688订单数据增强...")
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    if args.cart_data:
        print(f"采购车数据: {args.cart_data}")

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        return

    try:
        async with OrderEnricher() as enricher:
            await enricher.enrich_orders(
                args.input, args.output, args.cart_data
            )

        print("\n🎉 订单数据增强完成!")
        print(f"📄 输出文件: {args.output}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())