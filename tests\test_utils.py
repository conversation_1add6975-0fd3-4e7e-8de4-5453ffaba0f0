# -*- coding: utf-8 -*-
"""
1688自动化项目测试套件 - 工具模块测试
"""

import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

import sys
sys.path.append('..')

from utils.helpers import (
    NetworkUtils, ImageUtils, DataUtils, FileUtils, 
    StringUtils, ValidationUtils, CacheUtils
)
from utils.exceptions import NetworkError, ImageProcessingError, ValidationError

class TestNetworkUtils(unittest.TestCase):
    """网络工具测试"""
    
    def test_is_valid_url(self):
        """测试URL验证"""
        self.assertTrue(NetworkUtils.is_valid_url('https://example.com'))
        self.assertTrue(NetworkUtils.is_valid_url('http://test.com/path'))
        self.assertFalse(NetworkUtils.is_valid_url('not_a_url'))
        self.assertFalse(NetworkUtils.is_valid_url(''))
    
    def test_extract_offer_id(self):
        """测试OfferID提取"""
        self.assertEqual(NetworkUtils.extract_offer_id('https://detail.1688.com/offer/123456.html'), '123456')
        self.assertEqual(NetworkUtils.extract_offer_id('https://example.com?offerId=789012'), '789012')
        self.assertIsNone(NetworkUtils.extract_offer_id('https://example.com/no-offer'))
    
    @patch('requests.get')
    def test_download_image_sync(self, mock_get):
        """测试同步图片下载"""
        mock_response = MagicMock()
        mock_response.content = b'fake_image_data'
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response
        
        result = NetworkUtils.download_image_sync('https://example.com/image.jpg')
        self.assertEqual(result, b'fake_image_data')
    
    @patch('requests.get')
    def test_download_image_sync_error(self, mock_get):
        """测试同步图片下载错误"""
        mock_get.side_effect = Exception("Network error")
        
        with self.assertRaises(NetworkError):
            NetworkUtils.download_image_sync('https://example.com/image.jpg')

class TestImageUtils(unittest.TestCase):
    """图片工具测试"""
    
    def test_resize_image(self):
        """测试图片调整"""
        # 创建测试图片数据
        from PIL import Image
        import io
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (200, 200), color='red')
        output = io.BytesIO()
        img.save(output, format='PNG')
        image_data = output.getvalue()
        
        # 调整大小
        resized_data = ImageUtils.resize_image(image_data, (100, 100))
        self.assertIsInstance(resized_data, bytes)
        self.assertLess(len(resized_data), len(image_data))
    
    def test_resize_image_error(self):
        """测试图片调整错误"""
        with self.assertRaises(ImageProcessingError):
            ImageUtils.resize_image(b'invalid_image_data')
    
    def test_get_image_info(self):
        """测试获取图片信息"""
        # 创建测试图片数据
        from PIL import Image
        import io
        
        img = Image.new('RGB', (100, 100), color='blue')
        output = io.BytesIO()
        img.save(output, format='PNG')
        image_data = output.getvalue()
        
        info = ImageUtils.get_image_info(image_data)
        self.assertEqual(info['size'], (100, 100))
        self.assertEqual(info['width'], 100)
        self.assertEqual(info['height'], 100)
        self.assertEqual(info['mode'], 'RGB')

class TestDataUtils(unittest.TestCase):
    """数据处理工具测试"""
    
    def test_clean_text(self):
        """测试文本清理"""
        self.assertEqual(DataUtils.clean_text('  测试  文本  '), '测试 文本')
        self.assertEqual(DataUtils.clean_text(''), '')
        self.assertEqual(DataUtils.clean_text(None), '')
    
    def test_extract_price(self):
        """测试价格提取"""
        self.assertEqual(DataUtils.extract_price('￥29.9'), 29.9)
        self.assertEqual(DataUtils.extract_price('¥19.99'), 19.99)
        self.assertEqual(DataUtils.extract_price('价格:15元'), 15.0)
        self.assertEqual(DataUtils.extract_price('无价格'), 0.0)
    
    def test_extract_quantity(self):
        """测试数量提取"""
        self.assertEqual(DataUtils.extract_quantity('数量:5'), 5)
        self.assertEqual(DataUtils.extract_quantity('10个'), 10)
        self.assertEqual(DataUtils.extract_quantity('无数量'), 0)
    
    def test_validate_cart_item(self):
        """测试购物车商品验证"""
        valid_item = {
            '序号': 1,
            '品名': '测试商品',
            '数量': 5
        }
        self.assertTrue(DataUtils.validate_cart_item(valid_item))
        
        invalid_item = {
            '序号': 1,
            '品名': '测试商品',
            '数量': 0
        }
        self.assertFalse(DataUtils.validate_cart_item(invalid_item))
    
    def test_normalize_cart_data(self):
        """测试购物车数据标准化"""
        raw_data = [
            {
                '序号': 1,
                '品名': '  测试商品  ',
                '数量': 5,
                '规格': '  默认规格  '
            },
            {
                '序号': 2,
                '品名': '无效商品',
                '数量': 0
            }
        ]
        
        normalized = DataUtils.normalize_cart_data(raw_data)
        self.assertEqual(len(normalized), 1)
        self.assertEqual(normalized[0]['品名'], '测试商品')
        self.assertEqual(normalized[0]['规格'], '默认规格')
        self.assertEqual(normalized[0]['生产商名称'], '未知供应商')

class TestStringUtils(unittest.TestCase):
    """字符串工具测试"""
    
    def test_fuzzy_match(self):
        """测试模糊匹配"""
        self.assertTrue(StringUtils.fuzzy_match('测试文本', '测试文本', 0.8))
        self.assertTrue(StringUtils.fuzzy_match('测试文本', '测试文本', 1.0))
        self.assertFalse(StringUtils.fuzzy_match('完全不同', '测试文本', 0.8))
    
    def test_extract_number(self):
        """测试数字提取"""
        self.assertEqual(StringUtils.extract_number('价格123.45元'), 123.45)
        self.assertEqual(StringUtils.extract_number('数量10个'), 10.0)
        self.assertEqual(StringUtils.extract_number('无数字'), 0.0)
    
    def test_format_price(self):
        """测试价格格式化"""
        self.assertEqual(StringUtils.format_price(29.9), '29.90')
        self.assertEqual(StringUtils.format_price(15), '15.00')
        self.assertEqual(StringUtils.format_price(0), '0.00')

class TestFileUtils(unittest.TestCase):
    """文件工具测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_ensure_directory(self):
        """测试目录创建"""
        test_dir = Path(self.temp_dir) / 'test' / 'nested'
        result = FileUtils.ensure_directory(test_dir)
        self.assertTrue(result.exists())
        self.assertTrue(result.is_dir())
    
    def test_save_and_load_json(self):
        """测试JSON文件操作"""
        test_data = {'test': 'data', 'number': 123}
        test_file = Path(self.temp_dir) / 'test.json'
        
        FileUtils.save_json(test_data, test_file)
        self.assertTrue(test_file.exists())
        
        loaded_data = FileUtils.load_json(test_file)
        self.assertEqual(loaded_data, test_data)
    
    def test_generate_filename(self):
        """测试文件名生成"""
        filename = FileUtils.generate_filename('test', '.xlsx')
        self.assertTrue(filename.startswith('test_'))
        self.assertTrue(filename.endswith('.xlsx'))
        self.assertIn('_', filename)

class TestValidationUtils(unittest.TestCase):
    """验证工具测试"""
    
    def test_validate_excel_data(self):
        """测试Excel数据验证"""
        valid_data = [
            {'序号': 1, '品名': '商品1', '数量': 5},
            {'序号': 2, '品名': '商品2', '数量': 3}
        ]
        required_columns = ['序号', '品名', '数量']
        
        self.assertTrue(ValidationUtils.validate_excel_data(valid_data, required_columns))
        
        invalid_data = [
            {'序号': 1, '品名': '商品1'}  # 缺少数量
        ]
        self.assertFalse(ValidationUtils.validate_excel_data(invalid_data, required_columns))
    
    def test_validate_url(self):
        """测试URL验证"""
        self.assertTrue(ValidationUtils.validate_url('https://example.com'))
        self.assertFalse(ValidationUtils.validate_url('not_a_url'))
    
    def test_validate_image_url(self):
        """测试图片URL验证"""
        self.assertTrue(ValidationUtils.validate_image_url('https://example.com/image.jpg'))
        self.assertTrue(ValidationUtils.validate_image_url('https://example.com/image.png'))
        self.assertFalse(ValidationUtils.validate_image_url('https://example.com/file.txt'))
        self.assertFalse(ValidationUtils.validate_image_url('invalid_url'))

class TestCacheUtils(unittest.TestCase):
    """缓存工具测试"""
    
    def test_get_cache_key(self):
        """测试缓存键生成"""
        key1 = CacheUtils.get_cache_key('https://example.com/image1.jpg')
        key2 = CacheUtils.get_cache_key('https://example.com/image2.jpg')
        
        self.assertIsInstance(key1, str)
        self.assertIsInstance(key2, str)
        self.assertNotEqual(key1, key2)
    
    def test_get_cache_path(self):
        """测试缓存路径生成"""
        cache_key = 'test_key'
        cache_dir = Path('/tmp/cache')
        
        cache_path = CacheUtils.get_cache_path(cache_dir, cache_key, '.jpg')
        self.assertEqual(cache_path.name, 'test_key.jpg')
        self.assertEqual(cache_path.parent, cache_dir)

if __name__ == '__main__':
    unittest.main()