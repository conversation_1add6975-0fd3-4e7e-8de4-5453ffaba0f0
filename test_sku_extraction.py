#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试商品SKU提取功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.extract_orders import OrderDataExtractor

async def test_sku_extraction():
    """测试商品SKU提取功能"""
    print("🧪 开始测试商品SKU提取功能...")
    
    # 创建提取器实例
    extractor = OrderDataExtractor()
    
    # 模拟测试数据
    test_data = [
        {
            'order_id': '*****************',
            'title': '测试商品1 (红色, L码)',
            'product_specs': '红色, L码',
            'price': 29.90,
            'quantity': 2,
            'total_amount': 59.80,
            'shop_name': '测试供应商A',
            'order_time': '2024-01-15 10:30:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/image1.jpg',
            'detail_url': 'https://example.com/product1'
        },
        {
            'order_id': '*****************',
            'title': '测试商品2 (蓝色)',
            'product_specs': '蓝色',
            'price': 45.00,
            'quantity': 1,
            'total_amount': 45.00,
            'shop_name': '测试供应商A',
            'order_time': '2024-01-15 10:30:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/image2.jpg',
            'detail_url': 'https://example.com/product2'
        },
        {
            'order_id': '*****************',
            'title': '测试商品3 (绿色, M码)',
            'product_specs': '绿色, M码',
            'price': 35.50,
            'quantity': 3,
            'total_amount': 106.50,
            'shop_name': '测试供应商B',
            'order_time': '2024-01-16 14:20:00',
            'buyer_account': '<EMAIL>',
            'image_url': 'https://example.com/image3.jpg',
            'detail_url': 'https://example.com/product3'
        }
    ]
    
    print(f"📊 测试数据: {len(test_data)} 个商品SKU")
    
    # 测试保存到Excel
    output_path = PROJECT_ROOT / "reports" / "test_sku_extraction.xlsx"
    await extractor.save_to_excel(test_data, str(output_path))
    
    print("✅ 测试完成!")
    print(f"📄 测试结果已保存到: {output_path}")
    
    # 验证数据结构
    print("\n🔍 数据结构验证:")
    for i, item in enumerate(test_data):
        print(f"  商品 {i+1}:")
        print(f"    订单号: {item['order_id']}")
        print(f"    商品名称: {item['title']}")
        print(f"    商品规格: {item['product_specs']}")
        print(f"    单价: {item['price']}")
        print(f"    数量: {item['quantity']}")
        print(f"    小计: {item['total_amount']}")
        print(f"    卖家: {item['shop_name']}")
        print()
    
    # 统计信息
    unique_orders = len(set(item['order_id'] for item in test_data))
    unique_suppliers = len(set(item['shop_name'] for item in test_data))
    total_amount = sum(item['total_amount'] for item in test_data)
    
    print("📈 统计信息:")
    print(f"  商品SKU总数: {len(test_data)}")
    print(f"  订单数量: {unique_orders}")
    print(f"  供应商数量: {unique_suppliers}")
    print(f"  总金额: {total_amount:.2f} 元")

if __name__ == "__main__":
    asyncio.run(test_sku_extraction())
