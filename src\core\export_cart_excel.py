#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688采购车数据导出工具 - 模拟MCP操作流程
基于MCP执行提示词中的流程与经验编写
"""

import asyncio
import os
import random
import sys
import subprocess
import time
import socket
from datetime import datetime
from pathlib import Path

# 设置控制台编码以支持中文和emoji
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 定义项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright, Page, Browser
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Pat<PERSON><PERSON>ill, Alignment
import requests


class ExportExcelAutomation:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.playwright = None
        self.cart_data = []
        self.current_tab_index = 0
        self.debug_port = 9222

    def download_image(self, url, timeout=10):
        """下载图片"""
        try:
            headers = {
                'User-Agent': ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                               'AppleWebKit/537.36')
            }
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            return response.content
        except Exception as e:
            print(f"下载图片失败 {url}: {e}")
            return None

    def test_chrome_connection(self):
        """测试Chrome调试连接"""
        try:
            # 使用HTTP请求测试连接，绕过代理
            import requests
            url = f"http://localhost:{self.debug_port}/json/version"
            proxies = {'http': None, 'https': None}
            response = requests.get(url, timeout=3, proxies=proxies)
            return response.status_code == 200
        except Exception:
            return False

    async def start_debug_browser(self):
        """启动调试浏览器"""
        try:
            print("正在启动Chrome调试模式...")
            
            # 查找启动脚本
            script_path = PROJECT_ROOT / "start_debug_chrome.bat"
            
            if script_path.exists():
                print(f"找到启动脚本: {script_path}")
                
                # 启动脚本
                subprocess.Popen([str(script_path)], shell=True)
                
                # 等待Chrome启动
                print("等待Chrome启动...")
                for i in range(60):  # 最多等待60秒
                    if self.test_chrome_connection():
                        print(f"Chrome调试模式启动成功 (端口: {self.debug_port})")
                        # 额外等待5秒让Chrome完全初始化
                        await asyncio.sleep(5)
                        return True
                    await asyncio.sleep(1)
                    if i % 10 == 0:
                        print(f"等待中... {i}/60秒")
                
                print("Chrome启动超时")
                return False
            else:
                print(f"未找到启动脚本: {script_path}")
                return False
                
        except Exception as e:
            print(f"启动调试浏览器失败: {e}")
            return False

    async def connect_to_debug_browser(self):
        """连接到调试浏览器"""
        try:
            # 首先检查Chrome是否已经运行
            if not self.test_chrome_connection():
                print("Chrome调试模式未运行，正在启动...")
                if not await self.start_debug_browser():
                    raise Exception("无法启动Chrome调试模式")
            
            # 连接到Chrome调试实例，使用WebSocket URL绕过代理
            import requests
            proxies = {'http': None, 'https': None}
            response = requests.get(f"http://localhost:{self.debug_port}/json/version", proxies=proxies)
            if response.status_code == 200:
                data = response.json()
                websocket_url = data.get('webSocketDebuggerUrl')
                if websocket_url:
                    self.browser = await self.playwright.chromium.connect_over_cdp(websocket_url)
                else:
                    raise Exception("无法获取WebSocket URL")
            else:
                raise Exception("无法连接到Chrome调试端口")
            
            # 获取现有的页面
            contexts = self.browser.contexts
            if contexts:
                context = contexts[0]
                pages = context.pages
                if pages:
                    self.page = pages[0]
                    print("已连接到Chrome调试实例")
                    return True
            
            print("未找到活动的页面，请确保Chrome已启动并导航到1688购物车页面")
            return False
            
        except Exception as e:
            print(f"连接调试浏览器失败: {e}")
            print("请确保已运行 start_debug_chrome.bat 并在Chrome中登录1688")
            return False

    async def wait_for_random_time(self):
        """等待随机时间"""
        wait_time = random.uniform(1.0, 3.0)
        await asyncio.sleep(wait_time)


    async def extract_cart_data(self):
        """提取采购车数据"""
        print("开始提取采购车数据...")

        # 滚动页面以加载所有商品
        await self.page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await asyncio.sleep(2)

        # 使用JavaScript提取数据
        js_code = """
        () => {
            const products = [];
            const shopContainers = document.querySelectorAll('[class*="shop-container--container--"]');
            
            shopContainers.forEach((shopContainer, shopIndex) => {
                try {
                    // 提取生产商名称
                    const manufacturerEl = shopContainer.querySelector('[class*="shop-top--companyName--"]');
                    const manufacturer = manufacturerEl ? manufacturerEl.textContent.trim() : '';
                    
                    // 提取商品主图
                    const mainImageEl = shopContainer.querySelector('[class*="fancy-image"][class*="item-group--image--"] img');
                    const mainImage = mainImageEl ? mainImageEl.src : '';
                    
                    // 提取商品名
                    const titleEl = shopContainer.querySelector('[class*="item-group--title--"]');
                    const baseTitle = titleEl ? titleEl.textContent.trim() : '';
                    
                    // 提取所有规格/款式项
                    const itemContainers = shopContainer.querySelectorAll('[class*="item--"]');
                    
                    itemContainers.forEach((itemContainer, itemIndex) => {
                        try {
                            // 检查是否是规格/款式项(包含图片、标题、数量等)
                            const specImageEl = itemContainer.querySelector('[class*="fancy-image"][class*="item--image--"] img');
                            const specTitleEl = itemContainer.querySelector('[class*="item--titleText--"]');
                            const quantityEl = itemContainer.querySelector('span[class*="next-input"][class*="next-medium"] input');
                            const publishPriceEl = itemContainer.querySelector('[class*="item--publishPrice--"]');
                            const rebatePriceEl = itemContainer.querySelector('[class*="item--rebatePrice--"]');
                            const subtotalEl = itemContainer.querySelector('[class*="item--subtotal--"]');
                            
                            // 只有当有数量时才认为是有效的商品项
                            if (quantityEl && quantityEl.value) {
                                const quantity = parseInt(quantityEl.value) || 0;
                                if (quantity > 0) {
                                    // 选择图片: 优先使用规格图片，没有则使用主图
                                    const image = specImageEl ? specImageEl.src : mainImage;
                                    
                                    // 选择标题: 优先使用规格标题，没有则使用商品标题
                                    const title = specTitleEl ? specTitleEl.textContent.trim() : baseTitle;
                                    
                                    // 提取价格
                                    const publishPrice = publishPriceEl ? parseFloat(publishPriceEl.textContent.replace(/[^\d.]/g, '')) || 0 : 0;
                                    const rebatePrice = rebatePriceEl ? parseFloat(rebatePriceEl.textContent.replace(/[^\d.]/g, '')) || publishPrice;
                                    const subtotal = subtotalEl ? parseFloat(subtotalEl.textContent.replace(/[^\d.]/g, '')) || (rebatePrice * quantity);
                                    
                                    products.push({
                                        index: products.length + 1,
                                        title: title,
                                        manufacturer: manufacturer,
                                        base_title: baseTitle,
                                        spec_title: specTitleEl ? specTitleEl.textContent.trim() : '',
                                        main_image: mainImage,
                                        spec_image: specImageEl ? specImageEl.src : '',
                                        image: image,
                                        quantity: quantity,
                                        publish_price: publishPrice,
                                        rebate_price: rebatePrice,
                                        unit_price: rebatePrice,
                                        subtotal: subtotal,
                                        shop_index: shopIndex + 1,
                                        item_index: itemIndex + 1
                                    });
                                }
                            }
                        } catch (e) { console.log('提取规格项失败:', e); }
                    });
                } catch (e) { console.log('提取店铺容器失败:', e); }
            });
            return products;
        }
        """
        products = await self.page.evaluate(js_code)
        self.cart_data = products

        print(f"成功提取 {len(products)} 个商品项")
        return products

    async def create_excel_report(self, output_path: str):
        """创建Excel报告"""
        if not self.cart_data:
            print("没有数据可导出")
            return

        # 创建DataFrame
        df = pd.DataFrame(self.cart_data)

        # 重命名列
        df.columns = [
            '序号', '商品名称', '生产商', '基础标题', '规格标题', '主图链接', 
            '规格图片链接', '图片链接', '数量', '发布价格', '优惠价格', 
            '单价', '小计', '店铺序号', '商品序号'
        ]

        # 创建Excel工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "采购车数据"

        # 写入表头
        headers = df.columns.tolist()
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(
                start_color="CCCCCC", 
                end_color="CCCCCC", 
                fill_type="solid"
            )
            cell.alignment = Alignment(horizontal="center")

        # 写入数据
        for row_idx, row in enumerate(df.itertuples(index=False), 2):
            for col_idx, value in enumerate(row, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (ValueError, TypeError):
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(output_path)
        print(f"Excel报告已保存到: {output_path}")

    async def run_export(self, output_path: str = None):
        """运行导出流程"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = PROJECT_ROOT / "reports" / f"cart_export_{timestamp}.xlsx"

        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        try:
            # 启动playwright
            self.playwright = await async_playwright().start()
            
            # 连接到调试浏览器
            if not await self.connect_to_debug_browser():
                raise Exception("无法连接到调试浏览器")
            
            # 提示用户登录1688
            print("\n" + "=" * 50)
            print("请在Chrome中完成以下步骤:")
            print("1. 登录1688账号")
            print("2. 导航到采购车页面 (https://cart.1688.com/cart.htm)")
            print("3. 确保页面完全加载")
            print("=" * 50)
            print("准备好后按回车键继续...")
            
            # 等待用户确认
            try:
                await asyncio.get_event_loop().run_in_executor(None, input, "")
            except (KeyboardInterrupt, EOFError):
                print("\n操作已取消")
                return None
            
            # 开始提取数据
            await self.extract_cart_data()
            await self.create_excel_report(str(output_path))

            print(f"\n导出完成: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"导出失败: {e}")
            raise
        finally:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="1688采购车数据导出工具")
    parser.add_argument("--output", "-o", help="输出Excel文件路径")
    args = parser.parse_args()

    automation = ExportExcelAutomation()
    await automation.run_export(args.output)


if __name__ == "__main__":
    asyncio.run(main())