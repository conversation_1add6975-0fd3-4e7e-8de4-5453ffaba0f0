#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688采购车数据处理工具 - 超精简版
极简PyQt6界面，优化打包大小，专注于核心功能
"""

import sys
import os
import time
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QTextEdit, QRadioButton,
    QButtonGroup, QFileDialog, QMessageBox, QGroupBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class SimpleWorker(QThread):
    """简化的工作线程"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    log = pyqtSignal(str, str)
    done = pyqtSignal()
    
    def __init__(self, mode, file_path=None):
        super().__init__()
        self.mode = mode
        self.file_path = file_path
        self.running = True
        
    def stop(self):
        self.running = False
        
    def run(self):
        try:
            if self.mode == 'cart':
                self.process_cart()
            else:
                self.process_order()
        except Exception as e:
            self.log.emit(f"错误: {str(e)}", "error")
        finally:
            self.done.emit()
    
    def process_cart(self):
        """处理采购车数据"""
        self.log.emit("开始采购车数据提取...", "info")
        steps = ["连接浏览器", "提取数据", "处理图片", "生成报告"]
        
        for i, step in enumerate(steps):
            if not self.running: break
            self.log.emit(f"步骤 {i+1}: {step}", "info")
            for j in range(25):
                if not self.running: break
                self.progress.emit(i * 25 + j)
                self.status.emit(f"处理中: {step}")
                self.msleep(40)
        
        if self.running:
            self.log.emit("采购车数据处理完成!", "success")
            self.status.emit("完成")
    
    def process_order(self):
        """处理订单数据"""
        if not self.file_path:
            self.log.emit("请选择Excel文件", "warning")
            return
            
        self.log.emit(f"处理文件: {os.path.basename(self.file_path)}", "info")
        steps = ["读取文件", "分析数据", "匹配商品", "增强数据", "保存结果"]
        
        for i, step in enumerate(steps):
            if not self.running: break
            self.log.emit(f"步骤 {i+1}: {step}", "info")
            for j in range(20):
                if not self.running: break
                self.progress.emit(i * 20 + j)
                self.status.emit(f"处理中: {step}")
                self.msleep(50)
        
        if self.running:
            self.log.emit("订单数据处理完成!", "success")
            self.status.emit("完成")


class MinimalApp(QMainWindow):
    """极简应用界面"""
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("1688数据工具")
        self.setFixedSize(500, 400)
        
        # 主部件
        widget = QWidget()
        self.setCentralWidget(widget)
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("1688采购车数据处理工具")
        title.setFont(QFont("微软雅黑", 12, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 功能选择
        func_group = QGroupBox("选择功能")
        func_layout = QVBoxLayout()
        self.radio_group = QButtonGroup()
        
        cart_radio = QRadioButton("采购车数据提取")
        cart_radio.setChecked(True)
        self.radio_group.addButton(cart_radio)
        cart_radio.mode = 'cart'
        func_layout.addWidget(cart_radio)
        
        order_radio = QRadioButton("订单数据增强")
        self.radio_group.addButton(order_radio)
        order_radio.mode = 'order'
        func_layout.addWidget(order_radio)
        
        func_group.setLayout(func_layout)
        layout.addWidget(func_group)
        
        # 进度
        self.progress = QProgressBar()
        layout.addWidget(self.progress)
        
        self.status = QLabel("准备就绪")
        self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status)
        
        # 日志
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 按钮
        btn_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始")
        self.start_btn.clicked.connect(self.start_work)
        btn_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_work)
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)
        
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self.clear_log)
        btn_layout.addWidget(self.clear_btn)
        
        self.exit_btn = QPushButton("退出")
        self.exit_btn.clicked.connect(self.close)
        btn_layout.addWidget(self.exit_btn)
        
        layout.addLayout(btn_layout)
        
        # 简化样式
        self.setStyleSheet("""
            QPushButton { padding: 5px 10px; }
            QTextEdit { font-family: Consolas; font-size: 9px; }
        """)
    
    def start_work(self):
        """开始工作"""
        checked = self.radio_group.checkedButton()
        mode = checked.mode if checked else 'cart'
        file_path = None
        
        if mode == 'order':
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择Excel文件", "", "Excel (*.xlsx *.xls)"
            )
            if not file_path:
                self.add_log("未选择文件", "warning")
                return
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress.setValue(0)
        
        self.worker = SimpleWorker(mode, file_path)
        self.worker.progress.connect(self.progress.setValue)
        self.worker.status.connect(self.status.setText)
        self.worker.log.connect(self.add_log)
        self.worker.done.connect(self.work_done)
        self.worker.start()
    
    def stop_work(self):
        """停止工作"""
        if self.worker:
            self.worker.stop()
        self.add_log("已停止", "warning")
        self.reset_buttons()
    
    def work_done(self):
        """工作完成"""
        self.reset_buttons()
        if self.worker:
            self.worker.deleteLater()
            self.worker = None
    
    def reset_buttons(self):
        """重置按钮"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空", "info")
    
    def add_log(self, msg, msg_type="info"):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        color = {"success": "#4CAF50", "warning": "#FF9800", "error": "#f44336"}.get(msg_type, "#000")
        self.log_text.append(f'<span style="color: {color}">[{timestamp}] {msg}</span>')
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self, "确认退出", "任务运行中，确定退出？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.worker.stop()
                self.worker.wait()
            else:
                event.ignore()
                return
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("1688数据工具")
    
    window = MinimalApp()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()