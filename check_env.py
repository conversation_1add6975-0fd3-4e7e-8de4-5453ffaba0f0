#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查环境差异
"""

import sys
import os
from pathlib import Path

print("=" * 50)
print("环境检查")
print("=" * 50)

print(f"Python 可执行文件: {sys.executable}")
print(f"Python 版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

print("\n" + "=" * 30)
print("依赖包检查")
print("=" * 30)

try:
    import requests
    print(f"✓ requests: {requests.__version__}")
except ImportError as e:
    print(f"✗ requests: {e}")

try:
    import playwright
    print(f"✓ playwright: 已安装")
    try:
        print(f"  版本: {playwright.__version__}")
    except:
        print(f"  版本: 无法获取")
except ImportError as e:
    print(f"✗ playwright: {e}")

try:
    from PyQt6.QtWidgets import QApplication
    print(f"✓ PyQt6: 已安装")
except ImportError as e:
    print(f"✗ PyQt6: {e}")

print("\n" + "=" * 30)
print("Chrome连接测试")
print("=" * 30)

try:
    import requests
    response = requests.get("http://localhost:9222/json/version", timeout=3, proxies={'http': None, 'https': None})
    if response.status_code == 200:
        print(f"✓ Chrome调试接口可访问")
        data = response.json()
        print(f"  浏览器: {data.get('Browser', 'Unknown')}")
    else:
        print(f"✗ Chrome调试接口异常: {response.status_code}")
except Exception as e:
    print(f"✗ Chrome调试接口连接失败: {e}")

print("\n" + "=" * 50)
