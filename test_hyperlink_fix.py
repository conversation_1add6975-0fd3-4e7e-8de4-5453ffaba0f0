#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超链接修复效果
"""

import sys
import os
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def create_test_file():
    """创建测试文件"""
    reports_dir = PROJECT_ROOT / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    test_file = reports_dir / f"test_hyperlink_{timestamp}.xlsx"
    
    # 创建测试Excel文件
    import pandas as pd
    df = pd.DataFrame({
        '测试列': ['测试数据1', '测试数据2'],
        '时间': [timestamp, timestamp]
    })
    df.to_excel(str(test_file), index=False)
    
    print(f"✅ 创建测试文件: {test_file}")
    return test_file

def test_hyperlink_generation():
    """测试超链接生成"""
    print("🔗 测试超链接生成...")
    
    # 创建测试文件
    test_file = create_test_file()
    
    # 导入GUI模块
    from src.gui.qt_real import RealFunctionApp
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = RealFunctionApp()
    
    # 测试消息
    test_message = f"Excel报告已生成: {test_file}"
    enhanced = window.enhance_message_with_links(test_message)
    
    print(f"\n📝 测试结果:")
    print(f"原始消息: {test_message}")
    print(f"增强后: {enhanced}")
    
    # 检查URL格式
    if 'file:///' in enhanced:
        print("✅ 包含file://协议")
        
        # 检查是否使用正斜杠
        if enhanced.count('/') > enhanced.count('\\'):
            print("✅ 使用正斜杠格式")
        else:
            print("⚠️ 仍使用反斜杠格式")
    else:
        print("❌ 未生成超链接")
    
    # 清理测试文件
    test_file.unlink()
    print(f"🧹 清理测试文件")
    
    return enhanced

def test_url_parsing():
    """测试URL解析"""
    print("\n🔍 测试URL解析...")
    
    from urllib.parse import unquote
    
    # 模拟问题URL
    problem_urls = [
        "file:///D:%5C1688_automation_project%5Creports%5Ccart_report_20250812_170248.xlsx",
        "file:///D:/1688_automation_project/reports/cart_report_20250812_170248.xlsx"
    ]
    
    for url in problem_urls:
        print(f"\n原始URL: {url}")
        
        # 移除file:///前缀
        if url.startswith('file:///'):
            clean_url = url[8:]
        else:
            clean_url = url
        
        # 解码
        decoded = unquote(clean_url)
        print(f"解码后:   {decoded}")
        
        # 检查文件是否存在
        if os.path.exists(decoded):
            print("✅ 文件存在")
        else:
            print("❌ 文件不存在")

def main():
    """主函数"""
    print("超链接修复测试")
    print("=" * 30)
    
    # 1. 测试超链接生成
    enhanced_msg = test_hyperlink_generation()
    
    # 2. 测试URL解析
    test_url_parsing()
    
    print(f"\n🎯 修复要点:")
    print("1. 生成超链接时使用正斜杠 (/) 而不是反斜杠 (\\)")
    print("2. 点击时使用 unquote() 解码URL")
    print("3. 确保文件路径正确转换")
    
    print(f"\n💡 如果仍有问题，请检查:")
    print("1. 文件是否真实存在")
    print("2. 路径中是否有特殊字符")
    print("3. 权限是否足够")

if __name__ == "__main__":
    main()
