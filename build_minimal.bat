@echo off
echo ========================================
echo 1688数据工具 - 优化打包脚本
echo ========================================
echo.

REM 检查PyInstaller是否安装
python -c "import PyInstaller" 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到PyInstaller，正在安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo [错误] PyInstaller安装失败！
        pause
        exit /b 1
    )
    echo [成功] PyInstaller安装完成
    echo.
)

echo [信息] 开始优化打包...
echo.

REM 创建临时目录用于最小化依赖
if not exist "temp_build" mkdir temp_build

REM 复制必要的文件到临时目录
copy /Y "ui_app\qt_ultralight.py" "temp_build\main.py"
copy /Y "enrich_orders_simple.py" "temp_build\"
copy /Y "start_debug_chrome.bat" "temp_build\"

REM 进入临时目录
cd temp_build

REM 使用最小化配置打包
pyinstaller --noconfirm --clean ^
    --name "1688数据工具" ^
    --windowed ^
    --onedir ^
    --exclude-module=tkinter ^
    --exclude-module=matplotlib ^
    --exclude-module=numpy ^
    --exclude-module=scipy ^
    --exclude-module=pandas ^
    --exclude-module=openpyxl ^
    --exclude-module=PIL ^
    --exclude-module=aiohttp ^
    --exclude-module=asyncio ^
    --exclude-module=difflib ^
    --exclude-module=hashlib ^
    --exclude-module=requests ^
    --exclude-module=urllib3 ^
    --exclude-module=certifi ^
    --exclude-module=chardet ^
    --exclude-module=idna ^
    --exclude-module=click ^
    --exclude-module=colorama ^
    --exclude-module=tqdm ^
    --exclude-module=pytest ^
    --exclude-module=unittest ^
    --exclude-module=docutils ^
    --exclude-module=sphinx ^
    --exclude-module=jinja2 ^
    --exclude-module=markupsafe ^
    --exclude-module=flask ^
    --exclude-module=django ^
    --exclude-module=sqlalchemy ^
    --exclude-module=beautifulsoup4 ^
    --exclude-module=lxml ^
    --exclude-module=html5lib ^
    --exclude-module=PyQt6.QtWebEngine ^
    --exclude-module=PyQt6.QtWebEngineWidgets ^
    --exclude-module=PyQt6.QtMultimedia ^
    --exclude-module=PyQt6.QtMultimediaWidgets ^
    --exclude-module=PyQt6.QtBluetooth ^
    --exclude-module=PyQt6.QtPositioning ^
    --exclude-module=PyQt6.QtSensors ^
    --exclude-module=PyQt6.QtSerialPort ^
    --exclude-module=PyQt6.QtTest ^
    --exclude-module=PyQt6.QtDesigner ^
    --exclude-module=PyQt6.QtHelp ^
    --exclude-module=PyQt6.QtSql ^
    --exclude-module=PyQt6.QtXml ^
    --exclude-module=PyQt6.QtNetwork ^
    --exclude-module=PyQt6.QtOpenGL ^
    --exclude-module=PyQt6.QtQuick ^
    --exclude-module=PyQt6.QtQml ^
    --collect-all PyQt6.QtWidgets ^
    --collect-all PyQt6.QtCore ^
    --collect-all PyQt6.QtGui ^
    --icon=NONE ^
    --distpath "../dist_minimal" ^
    --workpath "../build_minimal" ^
    "main.py"

if %errorlevel% neq 0 (
    echo.
    echo [错误] 打包失败！
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo [成功] 优化打包完成！
echo.
echo [信息] 输出位置: dist_minimal\1688数据工具\
echo [信息] 可执行文件: dist_minimal\1688数据工具\1688数据工具.exe
echo.

REM 计算文件大小
for /f %%A in ('dir /s /b "dist_minimal\1688数据工具.exe"') do set size=%%~zA
set /a size_mb=%size%/1024/1024
echo [信息] 打包大小: %size_mb% MB

echo.
echo [提示] 进一步优化建议:
echo   1. 删除不需要的Qt插件
echo   2. 使用UPX压缩可执行文件
echo   3. 考虑使用--onefile模式(会增加内存使用)
echo   4. 手动删除不必要的翻译文件
echo.

REM 清理临时文件
rmdir /s /q temp_build 2>nul

pause