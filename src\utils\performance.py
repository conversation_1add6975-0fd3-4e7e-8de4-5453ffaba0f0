# -*- coding: utf-8 -*-
"""
1688自动化项目性能优化模块
提供性能监控、优化和内存管理功能
"""

import time
import psutil
import gc
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import json

from utils.logger import get_logger
from config import Config

logger = get_logger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    memory_start: Optional[float] = None
    memory_end: Optional[float] = None
    memory_delta: Optional[float] = None
    cpu_percent: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    additional_metrics: Optional[Dict[str, Any]] = None

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, log_interval: int = 50):
        self.logger = logger
        self.log_interval = log_interval
        self.metrics: List[PerformanceMetrics] = []
        self.operation_counts: Dict[str, int] = {}
        self.total_operation_time: Dict[str, float] = {}
        self.lock = threading.Lock()
        
    def start_operation(self, operation_name: str) -> PerformanceMetrics:
        """开始监控操作"""
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            start_time=datetime.now(),
            memory_start=self._get_memory_usage(),
            success=True
        )
        
        with self.lock:
            self.operation_counts[operation_name] = self.operation_counts.get(operation_name, 0) + 1
        
        self.logger.debug(f"开始监控操作: {operation_name}")
        return metrics
    
    def end_operation(self, metrics: PerformanceMetrics, 
                     success: bool = True, 
                     error_message: str = None,
                     **additional_metrics):
        """结束监控操作"""
        metrics.end_time = datetime.now()
        metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
        metrics.memory_end = self._get_memory_usage()
        metrics.memory_delta = metrics.memory_end - metrics.memory_start
        metrics.cpu_percent = psutil.cpu_percent()
        metrics.success = success
        metrics.error_message = error_message
        metrics.additional_metrics = additional_metrics
        
        with self.lock:
            self.metrics.append(metrics)
            operation_name = metrics.operation_name
            self.total_operation_time[operation_name] = self.total_operation_time.get(operation_name, 0) + metrics.duration
            
            # 定期记录性能指标
            if len(self.metrics) % self.log_interval == 0:
                self.log_performance_summary()
        
        # 记录单个操作性能
        if metrics.duration > 10:  # 超过10秒记录警告
            self.logger.warning(f"操作 '{operation_name}' 耗时过长: {metrics.duration:.2f}秒")
        
        if metrics.memory_delta > 100:  # 内存增长超过100MB记录警告
            self.logger.warning(f"操作 '{operation_name}' 内存增长过多: {metrics.memory_delta:.2f}MB")
        
        self.logger.info(f"操作 '{operation_name}' 完成，耗时: {metrics.duration:.2f}秒，内存变化: {metrics.memory_delta:.2f}MB")
        
        return metrics
    
    def log_performance_summary(self):
        """记录性能汇总"""
        with self.lock:
            if not self.metrics:
                return
            
            self.logger.info("性能指标汇总:")
            self.logger.info(f"  总操作数: {len(self.metrics)}")
            
            # 按操作类型统计
            operation_stats = {}
            for metrics in self.metrics:
                op_name = metrics.operation_name
                if op_name not in operation_stats:
                    operation_stats[op_name] = {
                        'count': 0,
                        'total_time': 0,
                        'avg_time': 0,
                        'max_time': 0,
                        'min_time': float('inf'),
                        'success_count': 0,
                        'error_count': 0
                    }
                
                stats = operation_stats[op_name]
                stats['count'] += 1
                stats['total_time'] += metrics.duration
                stats['max_time'] = max(stats['max_time'], metrics.duration)
                stats['min_time'] = min(stats['min_time'], metrics.duration)
                
                if metrics.success:
                    stats['success_count'] += 1
                else:
                    stats['error_count'] += 1
            
            # 计算平均时间
            for op_name, stats in operation_stats.items():
                stats['avg_time'] = stats['total_time'] / stats['count']
                stats['success_rate'] = stats['success_count'] / stats['count'] * 100
                
                self.logger.info(f"  {op_name}:")
                self.logger.info(f"    执行次数: {stats['count']}")
                self.logger.info(f"    平均耗时: {stats['avg_time']:.2f}秒")
                self.logger.info(f"    最长耗时: {stats['max_time']:.2f}秒")
                self.logger.info(f"    最短耗时: {stats['min_time']:.2f}秒")
                self.logger.info(f"    成功率: {stats['success_rate']:.1f}%")
    
    def get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """获取特定操作的统计信息"""
        with self.lock:
            operation_metrics = [m for m in self.metrics if m.operation_name == operation_name]
            
            if not operation_metrics:
                return {}
            
            total_time = sum(m.duration for m in operation_metrics)
            avg_time = total_time / len(operation_metrics)
            max_time = max(m.duration for m in operation_metrics)
            min_time = min(m.duration for m in operation_metrics)
            success_count = sum(1 for m in operation_metrics if m.success)
            success_rate = success_count / len(operation_metrics) * 100
            
            return {
                'operation_name': operation_name,
                'count': len(operation_metrics),
                'total_time': total_time,
                'avg_time': avg_time,
                'max_time': max_time,
                'min_time': min_time,
                'success_rate': success_rate,
                'success_count': success_count,
                'error_count': len(operation_metrics) - success_count
            }
    
    def export_metrics(self, file_path: str):
        """导出性能指标到文件"""
        with self.lock:
            metrics_data = {
                'export_time': datetime.now().isoformat(),
                'total_metrics': len(self.metrics),
                'metrics': [asdict(m) for m in self.metrics],
                'operation_counts': self.operation_counts.copy(),
                'total_operation_time': self.total_operation_time.copy()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"性能指标已导出到: {file_path}")
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def clear_metrics(self):
        """清除性能指标"""
        with self.lock:
            self.metrics.clear()
            self.operation_counts.clear()
            self.total_operation_time.clear()
        
        self.logger.info("性能指标已清除")

class MemoryManager:
    """内存管理器"""
    
    def __init__(self, cleanup_interval: int = 100, 
                 max_memory_usage: float = 1024):  # 1GB
        self.logger = logger
        self.cleanup_interval = cleanup_interval
        self.max_memory_usage = max_memory_usage
        self.operation_count = 0
        
    def check_memory_usage(self) -> float:
        """检查内存使用情况"""
        memory_usage = self._get_memory_usage()
        
        if memory_usage > self.max_memory_usage:
            self.logger.warning(f"内存使用过高: {memory_usage:.2f}MB > {self.max_memory_usage:.2f}MB")
            self.cleanup_memory()
        
        return memory_usage
    
    def cleanup_memory(self):
        """清理内存"""
        self.logger.info("开始清理内存...")
        
        # 强制垃圾回收
        gc.collect()
        
        # 清理缓存目录（可选）
        self._cleanup_cache()
        
        memory_after = self._get_memory_usage()
        self.logger.info(f"内存清理完成，当前内存使用: {memory_after:.2f}MB")
    
    def increment_operation(self):
        """增加操作计数"""
        self.operation_count += 1
        
        # 定期清理内存
        if self.operation_count % self.cleanup_interval == 0:
            self.check_memory_usage()
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def _cleanup_cache(self):
        """清理缓存目录"""
        cache_dir = Path(Config.CACHE_DIR) / "images"
        
        if not cache_dir.exists():
            return
        
        # 删除超过7天的缓存文件
        import time
        current_time = time.time()
        max_age = 7 * 24 * 60 * 60  # 7天
        
        deleted_count = 0
        for cache_file in cache_dir.glob("*"):
            if cache_file.is_file():
                file_age = current_time - cache_file.stat().st_mtime
                if file_age > max_age:
                    try:
                        cache_file.unlink()
                        deleted_count += 1
                    except Exception as e:
                        self.logger.warning(f"删除缓存文件失败 {cache_file}: {e}")
        
        if deleted_count > 0:
            self.logger.info(f"清理了 {deleted_count} 个过期缓存文件")

class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 50, 
                 max_concurrent: int = 5,
                 memory_manager: MemoryManager = None):
        self.logger = logger
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.memory_manager = memory_manager or MemoryManager()
        
    async def process_batch_async(self, items: List[Any], 
                                 process_func: Callable,
                                 **kwargs) -> List[Any]:
        """异步批量处理"""
        import asyncio
        
        results = []
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            self.logger.info(f"处理批次 {i//self.batch_size + 1}/{(len(items)-1)//self.batch_size + 1}，包含 {len(batch)} 项")
            
            # 内存检查
            self.memory_manager.check_memory_usage()
            
            # 并发处理批次
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def process_item(item):
                async with semaphore:
                    try:
                        result = await process_func(item, **kwargs)
                        self.memory_manager.increment_operation()
                        return result
                    except Exception as e:
                        self.logger.error(f"处理项目失败: {e}")
                        return None
            
            batch_results = await asyncio.gather(*[
                process_item(item) for item in batch
            ], return_exceptions=True)
            
            # 过滤掉失败的结果
            batch_results = [r for r in batch_results if r is not None]
            results.extend(batch_results)
            
            self.logger.info(f"批次 {i//self.batch_size + 1} 完成，成功处理 {len(batch_results)} 项")
        
        return results
    
    def process_batch_sync(self, items: List[Any], 
                          process_func: Callable,
                          **kwargs) -> List[Any]:
        """同步批量处理"""
        results = []
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            self.logger.info(f"处理批次 {i//self.batch_size + 1}/{(len(items)-1)//self.batch_size + 1}，包含 {len(batch)} 项")
            
            # 内存检查
            self.memory_manager.check_memory_usage()
            
            batch_results = []
            for item in batch:
                try:
                    result = process_func(item, **kwargs)
                    self.memory_manager.increment_operation()
                    batch_results.append(result)
                except Exception as e:
                    self.logger.error(f"处理项目失败: {e}")
            
            results.extend(batch_results)
            
            self.logger.info(f"批次 {i//self.batch_size + 1} 完成，成功处理 {len(batch_results)} 项")
            
            # 批次间短暂休息
            if i + self.batch_size < len(items):
                time.sleep(0.1)
        
        return results

# 性能监控装饰器
def monitor_performance(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            # 获取性能监控器
            monitor = get_performance_monitor()
            
            # 开始监控
            metrics = monitor.start_operation(op_name)
            
            try:
                result = func(*args, **kwargs)
                monitor.end_operation(metrics, success=True)
                return result
            except Exception as e:
                monitor.end_operation(metrics, success=False, error_message=str(e))
                raise
        
        return wrapper
    return decorator

# 全局性能监控器实例
_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def get_memory_manager() -> MemoryManager:
    """获取全局内存管理器"""
    return MemoryManager()

def get_batch_processor(batch_size: int = 50) -> BatchProcessor:
    """获取批处理器"""
    return BatchProcessor(batch_size=batch_size)

# 使用示例
if __name__ == "__main__":
    # 测试性能监控
    monitor = get_performance_monitor()
    
    @monitor_performance("test_operation")
    def test_function():
        time.sleep(0.1)
        return "test_result"
    
    # 运行测试
    result = test_function()
    print(f"测试结果: {result}")
    
    # 测试内存管理
    memory_manager = get_memory_manager()
    memory_usage = memory_manager.check_memory_usage()
    print(f"当前内存使用: {memory_usage:.2f}MB")
    
    # 测试批处理
    batch_processor = get_batch_processor(batch_size=3)
    
    def process_item(item):
        time.sleep(0.05)
        return f"processed_{item}"
    
    items = list(range(10))
    results = batch_processor.process_batch_sync(items, process_item)
    print(f"批处理结果: {results}")
    
    # 导出性能指标
    monitor.export_metrics("test_output/performance_metrics.json")
    
    print("✅ 性能优化模块测试完成")