"""
文件操作工具模块
"""

import os
from pathlib import Path
from typing import Optional, List, Tuple
import pandas as pd

# 延迟导入tkinter，避免在没有GUI的环境中出错
_tkinter_imported = False
_filedialog = None

def _get_filedialog():
    """延迟导入filedialog"""
    global _tkinter_imported, _filedialog
    if not _tkinter_imported:
        try:
            import tkinter as tk
            from tkinter import filedialog
            _filedialog = filedialog
            _tkinter_imported = True
        except ImportError:
            _filedialog = None
            _tkinter_imported = True
    return _filedialog

class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def select_excel_file(title: str = "选择Excel文件") -> Optional[str]:
        """选择Excel文件"""
        dialog = _get_filedialog()
        if dialog is None:
            print("WARNING: GUI不可用，无法显示文件选择对话框")
            return None
            
        file_path = dialog.askopenfilename(
            title=title,
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("所有文件", "*.*")
            ],
            initialdir=os.path.expanduser("~/Documents")
        )
        
        return file_path if file_path else None
    
    @staticmethod
    def select_save_file(
        title: str = "保存文件",
        default_name: str = "output.xlsx",
        filetypes: List[Tuple[str, str]] = None
    ) -> Optional[str]:
        """选择保存文件路径"""
        if filetypes is None:
            filetypes = [("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        
        dialog = _get_filedialog()
        if dialog is None:
            print("WARNING: GUI不可用，无法显示文件保存对话框")
            return None
            
        file_path = dialog.asksaveasfilename(
            title=title,
            defaultextension=".xlsx",
            initialfile=default_name,
            filetypes=filetypes,
            initialdir=os.path.expanduser("~/Documents")
        )
        
        return file_path if file_path else None
    
    @staticmethod
    def validate_excel_file(file_path: str) -> Tuple[bool, str]:
        """验证Excel文件"""
        try:
            if not os.path.exists(file_path):
                return False, "文件不存在"
            
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                return False, "文件格式不支持，请选择.xlsx或.xls文件"
            
            # 尝试读取文件
            df = pd.read_excel(file_path)
            
            if len(df) == 0:
                return False, "Excel文件为空"
            
            return True, "文件验证成功"
            
        except Exception as e:
            return False, f"文件读取失败：{str(e)}"
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """获取文件信息"""
        try:
            path = Path(file_path)
            stat = path.stat()
            
            return {
                "name": path.name,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "extension": path.suffix.lower(),
                "exists": path.exists()
            }
        except Exception:
            return {
                "name": "未知",
                "size": 0,
                "modified": 0,
                "extension": "",
                "exists": False
            }
    
    @staticmethod
    def generate_output_filename(prefix: str, extension: str = ".xlsx") -> str:
        """生成输出文件名"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{timestamp}{extension}"
    
    @staticmethod
    def ensure_directory_exists(directory: str) -> bool:
        """确保目录存在"""
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """获取目录大小"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        except Exception:
            return 0
    
    @staticmethod
    def clean_old_files(directory: str, days: int = 30) -> int:
        """清理旧文件"""
        import time
        
        try:
            count = 0
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            count += 1
                        except Exception:
                            pass
            
            return count
        except Exception:
            return 0