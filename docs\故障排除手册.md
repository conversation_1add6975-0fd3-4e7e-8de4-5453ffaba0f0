# 1688自动化项目故障排除手册

## 🚨 紧急情况处理

### 立即停止服务
```bash
# 停止所有相关进程
pkill -f "python.*export_cart_excel"
pkill -f "python.*extract_orders"
pkill -f "chrome.*remote-debugging"

# 停止系统服务（如果是服务部署）
sudo systemctl stop 1688-automation
```

### 备份数据
```bash
# 快速备份
./backup.sh

# 或手动备份
cp -r data/ backup/data_$(date +%Y%m%d_%H%M%S)
cp -r reports/ backup/reports_$(date +%Y%m%d_%H%M%S)
```

## 📊 错误分类及解决方案

### 1. 浏览器连接问题

#### 错误现象
- `BrowserConnectionError: 无法连接到调试浏览器`
- `TimeoutError: Chrome连接超时`
- `ConnectionRefusedError: 连接被拒绝`

#### 可能原因
1. Chrome未启动或未在调试模式运行
2. 端口被占用或防火墙阻止
3. Chrome版本不兼容
4. 用户数据目录权限问题

#### 解决方案
```bash
# 1. 检查Chrome进程
ps aux | grep chrome
ps aux | grep "remote-debugging"

# 2. 检查端口占用
netstat -tulpn | grep 9222
lsof -i :9222

# 3. 杀死占用端口的进程
sudo kill -9 $(lsof -t -i:9222)

# 4. 重新启动Chrome调试模式
# Windows:
start_debug_chrome.bat

# Linux/macOS:
google-chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome_debug &

# 5. 检查防火墙
# Ubuntu:
sudo ufw status
sudo ufw allow 9222

# CentOS:
sudo firewall-cmd --list-all
sudo firewall-cmd --permanent --add-port=9222/tcp
sudo firewall-cmd --reload

# 6. 验证连接
curl http://localhost:9222/json
```

#### 预防措施
- 在启动脚本中添加端口检查
- 设置自动重连机制
- 使用固定的Chrome版本

### 2. 数据提取失败

#### 错误现象
- `DataExtractionError: 数据提取失败`
- `AttributeError: 找不到元素`
- `TimeoutError: 页面加载超时`
- 提取的数据不完整或为空

#### 可能原因
1. 页面结构变化
2. 网络连接问题
3. 登录状态失效
4. 页面未完全加载
5. JavaScript错误

#### 解决方案
```bash
# 1. 检查网络连接
ping 1688.com
curl -I https://1688.com

# 2. 检查页面加载
# 在Chrome中手动访问页面，检查是否正常

# 3. 验证登录状态
# 在Chrome中检查是否已登录1688

# 4. 启用详细日志
python export_cart_excel.py --log-level DEBUG --verbose

# 5. 检查页面结构
# 在Chrome开发者工具中检查选择器是否有效

# 6. 更新选择器
# 编辑 config.py 中的 CART_ITEM_SELECTORS

# 7. 增加等待时间
echo '{"request_timeout": 60, "wait_timeout": 15000}' > user_config.json
```

#### 预防措施
- 定期检查和更新选择器
- 实现页面结构检测
- 添加自动重试机制

### 3. 内存不足问题

#### 错误现象
- `MemoryError: 内存不足`
- `PerformanceError: 内存使用过高`
- 系统变慢或无响应
- 进程被系统杀死

#### 可能原因
1. 处理数据量过大
2. 内存泄漏
3. 缓存文件过多
4. 系统资源不足

#### 解决方案
```bash
# 1. 检查内存使用
free -h
ps aux | grep python | sort -rk 4

# 2. 检查具体进程内存
ps -p <PID> -o rss,vsz,pcpu,pmem,cmd

# 3. 清理内存
sync && echo 3 > /proc/sys/vm/drop_caches

# 4. 杀死内存占用高的进程
sudo kill -9 <PID>

# 5. 清理缓存
rm -rf cache/images/*
find cache/images/ -name "*.jpg" -mtime +7 -delete

# 6. 调整批处理大小
echo '{"batch_size": 25, "memory_cleanup_interval": 25}' > user_config.json

# 7. 重启服务
sudo systemctl restart 1688-automation
```

#### 预防措施
- 实现内存监控和自动清理
- 使用批处理机制
- 定期清理缓存文件

### 4. 磁盘空间不足

#### 错误现象
- `FileOperationError: 磁盘空间不足`
- `OSError: No space left on device`
- 文件写入失败
- 系统警告磁盘空间不足

#### 可能原因
1. 日志文件过大
2. 缓存文件过多
3. 数据文件积累
4. 系统磁盘空间不足

#### 解决方案
```bash
# 1. 检查磁盘使用
df -h
du -sh /app/1688_automation_project/*

# 2. 查找大文件
find /app -type f -size +100M -exec ls -lh {} \;

# 3. 清理日志文件
find /var/log -name "*.log" -size +50M -delete
find logs/ -name "*.log" -mtime +7 -delete

# 4. 清理缓存
rm -rf cache/images/*
find cache/ -name "*.jpg" -mtime +3 -delete

# 5. 清理临时文件
find /tmp -name "*1688*" -delete

# 6. 压缩旧数据
tar -czf backup/old_data_$(date +%Y%m%d).tar.gz data/ reports/
rm -rf data/2024* reports/2024*

# 7. 设置日志轮转
# 配置 logrotate 或使用 RotatingFileHandler
```

#### 预防措施
- 实现日志轮转
- 定期清理缓存
- 设置磁盘空间监控

### 5. 网络连接问题

#### 错误现象
- `NetworkError: 网络请求失败`
- `TimeoutError: 请求超时`
- `ConnectionError: 连接被拒绝`
- 图片下载失败

#### 可能原因
1. 网络连接不稳定
2. 防火墙阻止
3. 代理设置问题
4. 1688反爬机制

#### 解决方案
```bash
# 1. 检查网络连接
ping 1688.com
ping *******

# 2. 检查DNS解析
nslookup 1688.com

# 3. 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY

# 4. 测试网络请求
curl -I https://1688.com
curl -I https://img.alicdn.com/test.jpg

# 5. 检查防火墙
sudo ufw status
sudo iptables -L

# 6. 增加重试次数和超时时间
echo '{"max_retries": 5, "request_timeout": 60}' > user_config.json

# 7. 使用代理（如果需要）
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080
```

#### 预防措施
- 实现网络重试机制
- 使用代理池
- 监控网络状态

### 6. Excel生成失败

#### 错误现象
- `ExcelGenerationError: Excel生成失败`
- `PermissionError: 权限被拒绝`
- `ValueError: 数据格式错误`
- 文件损坏或无法打开

#### 可能原因
1. 文件权限问题
2. 数据格式错误
3. Excel格式兼容性问题
4. 磁盘空间不足

#### 解决方案
```bash
# 1. 检查文件权限
ls -la reports/
chmod 755 reports/
chmod 644 reports/*.xlsx

# 2. 检查磁盘空间
df -h .

# 3. 验证数据格式
python validate_project.py

# 4. 手动创建测试文件
python -c "
import pandas as pd
df = pd.DataFrame({'test': [1, 2, 3]})
df.to_excel('test.xlsx')
"

# 5. 检查Excel兼容性
# 尝试用不同的Excel版本打开文件

# 6. 清理临时文件
rm -f *.tmp
rm -f ~/*.xlsx
```

#### 预防措施
- 实现数据验证
- 使用标准的Excel格式
- 添加文件权限检查

## 🔧 诊断工具

### 系统诊断脚本
```bash
#!/bin/bash
# diagnose.sh

echo "=== 系统诊断报告 ==="
echo "时间: $(date)"
echo

# 系统信息
echo "=== 系统信息 ==="
uname -a
python --version
google-chrome --version
echo

# 资源使用
echo "=== 资源使用 ==="
free -h
df -h
echo

# 进程检查
echo "=== 进程检查 ==="
ps aux | grep -E "(python|chrome)" | grep -v grep
echo

# 端口检查
echo "=== 端口检查 ==="
netstat -tulpn | grep -E "(9222|python|chrome)"
echo

# 文件检查
echo "=== 文件检查 ==="
echo "项目目录:"
ls -la
echo "数据目录:"
ls -la data/ 2>/dev/null || echo "数据目录不存在"
echo "报告目录:"
ls -la reports/ 2>/dev/null || echo "报告目录不存在"
echo "日志目录:"
ls -la logs/ 2>/dev/null || echo "日志目录不存在"
echo

# 权限检查
echo "=== 权限检查 ==="
echo "当前用户: $(whoami)"
echo "Python权限:"
python -c "import os; print('可写数据目录:', os.access('data/', os.W_OK))"
echo "Excel权限:"
python -c "import pandas as pd; print('Excel库正常')" 2>/dev/null || echo "Excel库有问题"
echo

# 网络检查
echo "=== 网络检查 ==="
ping -c 3 1688.com
echo "DNS解析:"
nslookup 1688.com
echo "HTTP连接:"
curl -I --connect-timeout 10 https://1688.com
echo

# 服务检查
echo "=== 服务检查 ==="
if command -v systemctl &> /dev/null; then
    systemctl status 1688-automation 2>/dev/null || echo "服务未安装"
fi
echo

echo "=== 诊断完成 ==="
```

### 日志分析脚本
```bash
#!/bin/bash
# analyze_logs.sh

LOG_DIR="logs"
ERROR_LOG="$LOG_DIR/1688_automation.log"

if [ ! -f "$ERROR_LOG" ]; then
    echo "日志文件不存在: $ERROR_LOG"
    exit 1
fi

echo "=== 日志分析报告 ==="
echo "分析文件: $ERROR_LOG"
echo "分析时间: $(date)"
echo

# 错误统计
echo "=== 错误统计 ==="
echo "总错误数: $(grep -c "ERROR" "$ERROR_LOG")"
echo "总警告数: $(grep -c "WARNING" "$ERROR_LOG")"
echo "总信息数: $(grep -c "INFO" "$ERROR_LOG")"
echo

# 错误类型
echo "=== 错误类型 ==="
grep "ERROR" "$ERROR_LOG" | sed 's/.*ERROR.*\[\(.*\)\].*/\1/' | sort | uniq -c | sort -nr
echo

# 最近错误
echo "=== 最近10个错误 ==="
grep "ERROR" "$ERROR_LOG" | tail -10
echo

# 性能问题
echo "=== 性能问题 ==="
grep -i "timeout\|slow\|performance\|memory" "$ERROR_LOG" | tail -5
echo

# 连接问题
echo "=== 连接问题 ==="
grep -i "connection\|network\|chrome\|browser" "$ERROR_LOG" | tail -5
echo

echo "=== 分析完成 ==="
```

### 性能监控脚本
```bash
#!/bin/bash
# monitor_performance.sh

PROJECT_DIR="/app/1688_automation_project"
LOG_FILE="$PROJECT_DIR/logs/performance.log"

# 获取性能指标
get_memory_usage() {
    ps aux | grep "python.*export_cart_excel" | grep -v grep | awk '{print $4/1024 "MB"}'
}

get_cpu_usage() {
    ps aux | grep "python.*export_cart_excel" | grep -v grep | awk '{print $3 "%"}'
}

get_disk_usage() {
    df "$PROJECT_DIR" | tail -1 | awk '{print $5}'
}

# 记录性能指标
echo "$(date), 内存: $(get_memory_usage), CPU: $(get_cpu_usage), 磁盘: $(get_disk_usage)" >> "$LOG_FILE"

# 检查阈值
MEMORY_USAGE=$(get_memory_usage | sed 's/MB//')
CPU_USAGE=$(get_cpu_usage | sed 's/%//')
DISK_USAGE=$(get_disk_usage | sed 's/%//')

# 内存告警
if (( $(echo "$MEMORY_USAGE > 1024" | bc -l) )); then
    echo "警告: 内存使用过高 (${MEMORY_USAGE}MB)" | mail -s "内存告警" <EMAIL>
fi

# CPU告警
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "警告: CPU使用过高 (${CPU_USAGE}%)" | mail -s "CPU告警" <EMAIL>
fi

# 磁盘告警
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "警告: 磁盘使用过高 (${DISK_USAGE}%)" | mail -s "磁盘告警" <EMAIL>
fi
```

## 📞 获取帮助

### 日志文件位置
- **主日志**: `logs/1688_automation.log`
- **错误日志**: `logs/error.log`
- **性能日志**: `logs/performance.log`
- **系统日志**: `/var/log/syslog` (Linux)

### 诊断命令
```bash
# 运行系统诊断
./diagnose.sh > diagnosis_$(date +%Y%m%d_%H%M%S).txt

# 分析日志
./analyze_logs.sh > log_analysis_$(date +%Y%m%d_%H%M%S).txt

# 检查性能
./monitor_performance.sh
```

### 联系技术支持
在联系技术支持前，请准备以下信息：
1. 操作系统版本和Python版本
2. 完整的错误日志
3. 系统诊断报告
4. 问题发生的具体步骤
5. 已尝试的解决方法

### 常见问题快速参考
- **Chrome连接问题**: 检查端口9222是否被占用
- **数据提取失败**: 检查登录状态和网络连接
- **内存不足**: 清理缓存并调整批处理大小
- **磁盘空间不足**: 清理日志和临时文件
- **Excel生成失败**: 检查文件权限和数据格式

---

**故障排除完成！** 🔧

请根据具体错误类型参考相应的解决方案。