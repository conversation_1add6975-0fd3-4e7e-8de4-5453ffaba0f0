# 1688采购车数据处理工具

一个用于从1688网站提取采购车数据并增强Excel订单文件的桌面应用程序。

## 🚀 功能特点

### 🛒 采购车数据提取
- 直接从1688采购车页面提取商品数据
- 自动下载商品图片
- 生成包含图片的Excel报告
- 支持多种数据格式和字段

### 📋 订单数据增强  
- 为已有Excel订单文件添加商品信息
- 自动匹配商品图片和详情链接
- 智能识别商品名称、价格等字段
- 生成增强后的Excel文件

### 🎨 用户友好界面
- 简洁直观的图形界面
- 分步骤操作指引
- 实时进度显示
- 详细的操作日志

## 📦 安装和运行

### 环境要求
- Python 3.7+
- Chrome浏览器
- Windows操作系统

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd ui_app
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行测试**
   ```bash
   python test_app.py
   ```

4. **启动应用**
   ```bash
   python main.py
   ```

### 打包成可执行文件

```bash
python build_exe.py
```

打包完成后，可执行文件位于 `dist/1688采购车数据处理工具.exe`

## 🎯 使用指南

### 采购车数据提取

1. **启动Chrome调试模式**
   - 点击"启动Chrome"按钮
   - 在Chrome中登录1688账号
   - 进入采购车页面

2. **提取采购车数据**
   - 确保采购车页面已完全加载
   - 点击"开始提取"按钮
   - 等待数据提取完成

3. **处理和增强数据**
   - 系统自动处理提取的数据
   - 下载商品图片
   - 标准化数据格式

4. **保存结果**
   - 选择保存位置
   - 生成包含图片的Excel报告

### 订单数据增强

1. **选择Excel文件**
   - 点击"选择文件"按钮
   - 选择要增强的Excel订单文件
   - 系统自动验证文件格式

2. **分析订单数据**
   - 系统分析数据结构
   - 识别商品名称、价格等字段
   - 生成分析报告

3. **增强数据**
   - 自动搜索商品信息
   - 添加商品图片和详情链接
   - 逐条处理订单记录

4. **保存增强结果**
   - 选择保存位置
   - 生成增强后的Excel文件

## 📁 项目结构

```
ui_app/
├── main.py              # 主程序入口
├── config.json          # 配置文件
├── requirements.txt     # 依赖包列表
├── build_exe.py        # 打包脚本
├── test_app.py         # 测试脚本
├── ui/                  # UI模块
│   ├── __init__.py
│   ├── main_window.py   # 主窗口
│   ├── function_selection.py  # 功能选择界面
│   ├── step_manager.py  # 步骤管理器
│   └── components.py    # UI组件
├── core/                # 核心功能模块
│   ├── __init__.py
│   ├── config.py        # 配置管理
│   ├── error_handler.py # 错误处理
│   ├── chrome_manager.py # Chrome管理
│   ├── data_processor.py # 数据处理
│   ├── cart_steps.py   # 采购车步骤
│   └── order_steps.py  # 订单步骤
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── logger.py        # 日志记录
│   ├── file_utils.py    # 文件操作
│   └── helpers.py       # 辅助函数
├── assets/              # 资源文件
└── data/                # 数据文件
```

## 🔧 配置说明

配置文件 `config.json` 包含以下设置：

```json
{
  "app_name": "1688采购车数据处理工具",
  "chrome": {
    "debug_port": 9222,
    "timeout": 30,
    "retry_attempts": 3
  },
  "processing": {
    "max_workers": 5,
    "timeout": 300,
    "chunk_size": 10
  },
  "ui": {
    "window_width": 800,
    "window_height": 600,
    "progress_update_interval": 100
  },
  "paths": {
    "cache_dir": "cache",
    "logs_dir": "logs",
    "output_dir": "output"
  }
}
```

## 🛠️ 开发指南

### 添加新功能

1. **创建新的步骤类**
   ```python
   from core.step_manager import BaseStep
   
   class NewStep(BaseStep):
       def __init__(self):
           super().__init__("新步骤名称", "步骤描述")
       
       def execute(self, progress_callback):
           # 实现步骤逻辑
           pass
   ```

2. **注册新步骤**
   ```python
   step_manager.add_step(NewStep())
   ```

3. **更新UI组件**
   - 在 `components.py` 中添加新组件
   - 在主窗口中集成新组件

### 调试技巧

1. **查看日志**
   - 日志文件位于 `logs/` 目录
   - 界面中也有实时日志显示

2. **测试功能**
   - 运行 `python test_app.py` 进行测试
   - 检查各模块是否正常工作

3. **Chrome调试**
   - 确保Chrome已安装
   - 检查调试端口是否被占用
   - 查看Chrome启动日志

## 🐛 常见问题

### Chrome启动失败
- 确保Chrome浏览器已安装
- 检查Chrome路径是否正确
- 尝试手动启动Chrome

### 文件处理失败
- 确保Excel文件格式正确
- 检查文件权限
- 确认文件未被其他程序占用

### 网络连接问题
- 检查网络连接
- 确保可以访问1688网站
- 尝试使用代理或VPN

### 数据提取失败
- 确保已登录1688账号
- 检查采购车页面是否正确加载
- 尝试刷新页面后重试

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**版本**: 1.0.0  
**最后更新**: 2025-08-10  
**作者**: 开发团队