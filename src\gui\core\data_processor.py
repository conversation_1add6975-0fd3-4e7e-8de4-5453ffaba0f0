"""
数据处理器模块
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable
import threading
import time
import json
import os
from pathlib import Path

class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config=None):
        self.config = config
        self.is_running = False
        self.progress_callback = None
        self.result_callback = None
        self.error_callback = None
    
    def process_orders_async(self, input_file: str, output_file: str = None, 
                           progress_callback: Callable = None,
                           result_callback: Callable = None,
                           error_callback: Callable = None):
        """异步处理订单数据"""
        def worker():
            self.is_running = True
            try:
                result = self.process_orders(input_file, output_file, progress_callback)
                if result_callback:
                    result_callback(result)
            except Exception as e:
                if error_callback:
                    error_callback(str(e))
            finally:
                self.is_running = False
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def process_orders(self, input_file: str, output_file: str = None, 
                      progress_callback: Callable = None) -> Dict[str, Any]:
        """处理订单数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(input_file)
            
            if progress_callback:
                progress_callback(10, "文件读取成功")
            
            # 分析数据结构
            analysis = self.analyze_data_structure(df)
            
            if progress_callback:
                progress_callback(20, "数据分析完成")
            
            # 增强数据
            enhanced_df = self.enhance_order_data(df, analysis, progress_callback)
            
            if progress_callback:
                progress_callback(90, "数据增强完成")
            
            # 保存结果
            if output_file is None:
                output_file = self.generate_output_filename(input_file)
            
            enhanced_df.to_excel(output_file, index=False)
            
            if progress_callback:
                progress_callback(100, "处理完成")
            
            return {
                'success': True,
                'input_file': input_file,
                'output_file': output_file,
                'total_records': len(df),
                'enhanced_records': len(enhanced_df),
                'analysis': analysis
            }
            
        except Exception as e:
            raise Exception(f"数据处理失败：{str(e)}")
    
    def analyze_data_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析数据结构"""
        analysis = {
            'total_records': len(df),
            'total_columns': len(df.columns),
            'columns': list(df.columns),
            'data_types': df.dtypes.to_dict(),
            'null_counts': df.isnull().sum().to_dict(),
            'column_analysis': {}
        }
        
        # 分析每列
        for col in df.columns:
            col_data = df[col]
            analysis['column_analysis'][col] = {
                'dtype': str(col_data.dtype),
                'null_count': col_data.isnull().sum(),
                'unique_count': col_data.nunique(),
                'sample_values': col_data.dropna().head(5).tolist(),
                'is_potential_product_name': self.is_product_name_column(col_data),
                'is_potential_price': self.is_price_column(col_data),
                'is_potential_quantity': self.is_quantity_column(col_data)
            }
        
        return analysis
    
    def is_product_name_column(self, series: pd.Series) -> bool:
        """判断是否为商品名称列"""
        if series.dtype == 'object':
            non_null = series.dropna()
            if len(non_null) > 0:
                avg_len = non_null.astype(str).str.len().mean()
                return 5 <= avg_len <= 100
        return False
    
    def is_price_column(self, series: pd.Series) -> bool:
        """判断是否为价格列"""
        try:
            non_null = series.dropna()
            if len(non_null) > 0:
                numeric_vals = pd.to_numeric(
                    non_null.astype(str).str.replace('[,元,$]', '', regex=True),
                    errors='coerce'
                )
                valid_vals = numeric_vals.dropna()
                return len(valid_vals) > 0 and (valid_vals > 0).all()
        except:
            pass
        return False
    
    def is_quantity_column(self, series: pd.Series) -> bool:
        """判断是否为数量列"""
        try:
            non_null = series.dropna()
            if len(non_null) > 0:
                numeric_vals = pd.to_numeric(non_null, errors='coerce')
                valid_vals = numeric_vals.dropna()
                return len(valid_vals) > 0 and (valid_vals > 0).all()
        except:
            pass
        return False
    
    def process_item(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个商品数据"""
        try:
            processed = item_data.copy()
            
            # 标准化价格
            if 'price' in processed:
                price_str = str(processed['price'])
                # 移除货币符号和空格
                price_clean = ''.join(c for c in price_str if c.isdigit() or c == '.')
                try:
                    processed['price_numeric'] = float(price_clean)
                except ValueError:
                    processed['price_numeric'] = 0.0
            
            # 标准化数量
            if 'quantity' in processed:
                quantity_str = str(processed['quantity'])
                # 提取数字部分
                quantity_clean = ''.join(c for c in quantity_str if c.isdigit())
                try:
                    processed['quantity_numeric'] = int(quantity_clean) if quantity_clean else 1
                except ValueError:
                    processed['quantity_numeric'] = 1
            
            # 标准化商品名称
            if 'name' in processed:
                processed['name_clean'] = str(processed['name']).strip()
            
            # 添加处理时间戳
            import time
            processed['processed_at'] = time.time()
            
            return processed
            
        except Exception as e:
            return {
                'error': str(e),
                'original_data': item_data,
                'processed_at': time.time()
            }
    
    def enhance_order_data(self, df: pd.DataFrame, analysis: Dict[str, Any], 
                           progress_callback: Callable = None) -> pd.DataFrame:
        """增强订单数据"""
        enhanced_df = df.copy()
        
        # 识别关键列
        product_name_col = self.find_column_by_type(analysis, 'is_potential_product_name')
        price_col = self.find_column_by_type(analysis, 'is_potential_price')
        quantity_col = self.find_column_by_type(analysis, 'is_potential_quantity')
        
        # 添加新列
        enhanced_df['product_image_url'] = ''
        enhanced_df['product_detail_url'] = ''
        enhanced_df['enhancement_status'] = 'pending'
        enhanced_df['enhancement_error'] = ''
        
        total_records = len(enhanced_df)
        
        for i, (_, row) in enumerate(enhanced_df.iterrows()):
            try:
                # 获取商品名称
                product_name = str(row.get(product_name_col, '')).strip()
                
                if product_name:
                    # 搜索商品信息
                    product_info = self.search_product_info(product_name)
                    
                    if product_info:
                        enhanced_df.at[i, 'product_image_url'] = product_info.get('image_url', '')
                        enhanced_df.at[i, 'product_detail_url'] = product_info.get('detail_url', '')
                        enhanced_df.at[i, 'enhancement_status'] = 'success'
                    else:
                        enhanced_df.at[i, 'enhancement_status'] = 'failed'
                        enhanced_df.at[i, 'enhancement_error'] = '未找到匹配商品'
                else:
                    enhanced_df.at[i, 'enhancement_status'] = 'failed'
                    enhanced_df.at[i, 'enhancement_error'] = '商品名称为空'
                
                # 更新进度
                if progress_callback:
                    progress = 20 + (i + 1) / total_records * 70
                    progress_callback(progress, f"正在处理第{i+1}/{total_records}条记录")
                
                # 添加延迟
                time.sleep(0.1)
                
            except Exception as e:
                enhanced_df.at[i, 'enhancement_status'] = 'failed'
                enhanced_df.at[i, 'enhancement_error'] = str(e)
        
        return enhanced_df
    
    def find_column_by_type(self, analysis: Dict[str, Any], column_type: str) -> Optional[str]:
        """根据类型查找列"""
        for col, col_info in analysis.get('column_analysis', {}).items():
            if col_info.get(column_type, False):
                return col
        return None
    
    def search_product_info(self, product_name: str) -> Optional[Dict[str, Any]]:
        """搜索商品信息"""
        try:
            # 这里可以集成实际的搜索逻辑
            # 现在返回模拟数据
            return {
                'image_url': f'https://example.com/image/{hash(product_name)}.jpg',
                'detail_url': f'https://detail.1688.com/offer/{hash(product_name)}.html',
                'price': '价格面议',
                'supplier': '示例供应商'
            }
        except Exception as e:
            print(f"搜索商品信息失败：{e}")
            return None
    
    def generate_output_filename(self, input_file: str) -> str:
        """生成输出文件名"""
        from datetime import datetime
        
        input_path = Path(input_file)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        stem = input_path.stem.replace('_enhanced', '').replace('_processed', '')
        
        return str(input_path.parent / f"{stem}_enhanced_{timestamp}.xlsx")
    
    def cancel(self):
        """取消处理"""
        self.is_running = False

class CartDataProcessor:
    """采购车数据处理器"""
    
    def __init__(self, config=None):
        self.config = config
    
    def process_cart_data(self, cart_data: List[Dict[str, Any]], 
                         progress_callback: Callable = None) -> Dict[str, Any]:
        """处理采购车数据"""
        try:
            processed_data = []
            total_items = len(cart_data)
            
            for i, item in enumerate(cart_data):
                processed_item = self.process_cart_item(item)
                processed_data.append(processed_item)
                
                # 更新进度
                if progress_callback:
                    progress = (i + 1) / total_items * 100
                    progress_callback(progress, f"正在处理第{i+1}/{total_items}个商品")
                
                # 下载图片
                if item.get('imageUrl'):
                    self.download_product_image(item['imageUrl'], i)
                
                time.sleep(0.1)
            
            return {
                'success': True,
                'total_items': total_items,
                'processed_items': processed_data
            }
            
        except Exception as e:
            raise Exception(f"采购车数据处理失败：{str(e)}")
    
    def process_cart_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个采购车商品"""
        processed = item.copy()
        
        # 标准化价格
        price = item.get('price', '0')
        if isinstance(price, str):
            import re
            price_match = re.search(r'[\d,]+\.?\d*', price)
            if price_match:
                processed['price'] = price_match.group().replace(',', '')
            else:
                processed['price'] = '0'
        
        # 标准化数量
        quantity = item.get('quantity', '1')
        if isinstance(quantity, str):
            import re
            quantity_match = re.search(r'\d+', quantity)
            if quantity_match:
                processed['quantity'] = quantity_match.group()
            else:
                processed['quantity'] = '1'
        
        # 添加处理时间
        processed['processed_at'] = time.strftime("%Y-%m-%dT%H:%M:%S")
        
        return processed
    
    def download_product_image(self, image_url: str, index: int):
        """下载商品图片"""
        try:
            import requests
            from pathlib import Path
            
            if not image_url:
                return
            
            cache_dir = Path(__file__).parent.parent / "cache" / "images"
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            file_extension = Path(image_url).suffix.lower()
            if not file_extension:
                file_extension = '.jpg'
            
            filename = f"cart_product_{index:04d}{file_extension}"
            file_path = cache_dir / filename
            
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"采购车商品图片下载成功：{filename}")
            
        except Exception as e:
            print(f"下载采购车商品图片失败：{e}")

def hash(text: str) -> str:
    """简单的哈希函数"""
    import hashlib
    return hashlib.md5(text.encode()).hexdigest()[:8]