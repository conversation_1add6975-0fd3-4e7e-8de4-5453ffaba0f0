# -*- coding: utf-8 -*-
"""
1688自动化项目测试套件 - 集成测试
"""

import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

import sys
sys.path.append('..')

from config import Config
from utils.helpers import DataUtils, FileUtils
from utils.logger import get_logger
from utils.exceptions import ValidationError

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = Config()
        self.logger = get_logger('test_integration')
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cart_data_processing_pipeline(self):
        """测试购物车数据处理管道"""
        # 模拟原始购物车数据
        raw_cart_data = [
            {
                "序号": 1,
                "品名": "  测试商品1  ",
                "规格": "  默认规格  ",
                "数量": 5,
                "单价": 29.9,
                "小计": 149.5,
                "生产商名称": "  测试供应商  ",
                "图片URL": "https://example.com/image1.jpg",
                "商品链接": "https://detail.1688.com/offer/123456.html"
            },
            {
                "序号": 2,
                "品名": "测试商品2",
                "规格": "红色;L码",
                "数量": 2,
                "单价": 59.9,
                "小计": 119.8,
                "生产商名称": "测试供应商2",
                "图片URL": "https://example.com/image2.jpg",
                "商品链接": ""
            },
            {
                "序号": 3,
                "品名": "无效商品",
                "数量": 0,
                "单价": 0,
                "小计": 0
            }
        ]
        
        # 数据标准化
        normalized_data = DataUtils.normalize_cart_data(raw_data)
        
        # 验证处理结果
        self.assertEqual(len(normalized_data), 2)  # 无效商品应该被过滤
        
        # 验证第一个商品
        first_item = normalized_data[0]
        self.assertEqual(first_item['品名'], '测试商品1')
        self.assertEqual(first_item['规格'], '默认规格')
        self.assertEqual(first_item['数量'], 5)
        self.assertEqual(first_item['生产商名称'], '测试供应商')
        
        # 验证第二个商品
        second_item = normalized_data[1]
        self.assertEqual(second_item['品名'], '测试商品2')
        self.assertEqual(second_item['规格'], '红色;L码')
        self.assertEqual(second_item['数量'], 2)
        self.assertEqual(second_item['生产商名称'], '测试供应商2')
    
    def test_config_loading_and_merging(self):
        """测试配置加载和合并"""
        # 创建测试用户配置
        user_config = {
            'chrome_debug_port': 9999,
            'request_timeout': 60,
            'custom_setting': 'user_value'
        }
        
        user_config_file = Path(self.temp_dir) / 'user_config.json'
        FileUtils.save_json(user_config, user_config_file)
        
        # 设置测试环境变量
        import os
        os.environ['REQUEST_TIMEOUT'] = '90'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        
        try:
            # 加载配置
            loaded_user_config = self.config.load_user_config(str(user_config_file))
            env_config = self.config.get_env_config()
            
            # 合并配置
            base_config = {
                'chrome_debug_port': 9222,
                'request_timeout': 30,
                'log_level': 'INFO'
            }
            
            merged_config = self.config.merge_config(base_config, loaded_user_config, env_config)
            
            # 验证合并结果
            self.assertEqual(merged_config['chrome_debug_port'], 9999)  # 用户配置
            self.assertEqual(merged_config['request_timeout'], 90)      # 环境变量
            self.assertEqual(merged_config['log_level'], 'DEBUG')        # 环境变量
            self.assertEqual(merged_config['custom_setting'], 'user_value')  # 用户配置
            
        finally:
            # 清理环境变量
            del os.environ['REQUEST_TIMEOUT']
            del os.environ['LOG_LEVEL']
    
    def test_excel_column_validation(self):
        """测试Excel列验证"""
        # 测试数据
        test_data = [
            {
                '序号': 1,
                '商品名称': '测试商品',
                '生产商名称': '测试供应商',
                '数量': 5
            }
        ]
        
        # 必需列
        required_columns = ['序号', '商品名称', '数量']
        
        # 验证数据
        from utils.helpers import ValidationUtils
        is_valid = ValidationUtils.validate_excel_data(test_data, required_columns)
        self.assertTrue(is_valid)
        
        # 测试缺少必需列的情况
        invalid_data = [{'序号': 1, '商品名称': '测试商品'}]  # 缺少数量
        is_valid = ValidationUtils.validate_excel_data(invalid_data, required_columns)
        self.assertFalse(is_valid)
    
    def test_file_operations_integration(self):
        """测试文件操作集成"""
        # 创建测试数据
        test_data = {
            'test': 'integration',
            'number': 42,
            'items': ['item1', 'item2']
        }
        
        # 创建目录和文件
        test_dir = Path(self.temp_dir) / 'test_data'
        test_file = test_dir / 'test_integration.json'
        
        # 确保目录存在
        FileUtils.ensure_directory(test_dir)
        
        # 保存文件
        FileUtils.save_json(test_data, test_file)
        self.assertTrue(test_file.exists())
        
        # 加载文件
        loaded_data = FileUtils.load_json(test_file)
        self.assertEqual(loaded_data, test_data)
        
        # 测试文件名生成
        filename = FileUtils.generate_filename('integration_test', '.json')
        self.assertTrue(filename.startswith('integration_test_'))
        self.assertTrue(filename.endswith('.json'))
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        from utils.exceptions import ValidationError, NetworkError
        from utils.logger import get_error_handler
        
        error_handler = get_error_handler(self.logger)
        
        # 测试验证错误
        try:
            raise ValidationError("测试验证错误")
        except ValidationError as e:
            error_info = error_handler.handle_error(e, "测试验证")
            self.assertEqual(error_info['error_type'], 'ValidationError')
            self.assertEqual(error_info['context'], '测试验证')
        
        # 测试网络错误
        try:
            raise NetworkError("测试网络错误")
        except NetworkError as e:
            error_info = error_handler.handle_error(e, "测试网络")
            self.assertEqual(error_info['error_type'], 'NetworkError')
            self.assertEqual(error_info['context'], '测试网络')
        
        # 检查错误统计
        self.assertEqual(error_handler.error_count, 2)
        self.assertEqual(error_handler.error_types['ValidationError'], 1)
        self.assertEqual(error_handler.error_types['NetworkError'], 1)
    
    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        from utils.logger import get_performance_monitor
        
        monitor = get_performance_monitor(self.logger)
        
        # 模拟操作计时
        monitor.start_timer('test_operation')
        
        # 模拟一些处理时间
        import time
        time.sleep(0.1)
        
        monitor.end_timer('test_operation')
        
        # 验证性能指标
        self.assertIn('test_operation', monitor.metrics)
        self.assertIsNotNone(monitor.metrics['test_operation']['duration'])
        self.assertGreater(monitor.metrics['test_operation']['duration'], 0)
        
        # 记录性能指标
        monitor.log_metrics()
    
    def test_cache_integration(self):
        """测试缓存集成"""
        from utils.helpers import CacheUtils
        
        # 测试缓存键生成
        url = 'https://example.com/image.jpg'
        cache_key = CacheUtils.get_cache_key(url)
        self.assertIsInstance(cache_key, str)
        self.assertEqual(len(cache_key), 32)  # MD5哈希长度
        
        # 测试缓存路径生成
        cache_dir = Path(self.temp_dir) / 'cache'
        cache_path = CacheUtils.get_cache_path(cache_dir, cache_key, '.jpg')
        
        self.assertEqual(cache_path.name, f'{cache_key}.jpg')
        self.assertEqual(cache_path.parent, cache_dir)
    
    def test_data_extraction_simulation(self):
        """模拟数据提取流程"""
        # 模拟从网页提取的原始数据
        raw_extracted_data = {
            "提取时间": "2025-08-09T13:47:11.279950",
            "页面URL": "https://cart.1688.com/cart.htm",
            "商品总数": 2,
            "商品数据": [
                {
                    "序号": 1,
                    "品名": "  模拟商品1  ",
                    "规格": "  默认规格  ",
                    "数量": 3,
                    "单价": 19.9,
                    "小计": 59.7,
                    "生产商名称": "  模拟供应商  ",
                    "图片URL": "https://example.com/img1.jpg",
                    "商品链接": "https://detail.1688.com/offer/111111.html"
                },
                {
                    "序号": 2,
                    "品名": "模拟商品2",
                    "规格": "红色;M码",
                    "数量": 1,
                    "单价": 29.9,
                    "小计": 29.9,
                    "生产商名称": "模拟供应商2",
                    "图片URL": "https://example.com/img2.jpg",
                    "商品链接": ""
                }
            ]
        }
        
        # 保存原始数据
        raw_data_file = Path(self.temp_dir) / 'raw_cart_data.json'
        FileUtils.save_json(raw_extracted_data, raw_data_file)
        
        # 处理数据
        processed_data = DataUtils.normalize_cart_data(raw_extracted_data['商品数据'])
        
        # 验证处理结果
        self.assertEqual(len(processed_data), 2)
        
        # 验证第一个商品
        first_item = processed_data[0]
        self.assertEqual(first_item['品名'], '模拟商品1')
        self.assertEqual(first_item['规格'], '默认规格')
        self.assertEqual(first_item['数量'], 3)
        self.assertEqual(first_item['单价'], 19.9)
        
        # 验证第二个商品
        second_item = processed_data[1]
        self.assertEqual(second_item['品名'], '模拟商品2')
        self.assertEqual(second_item['规格'], '红色;M码')
        self.assertEqual(second_item['数量'], 1)
        self.assertEqual(second_item['单价'], 29.9)
        
        # 保存处理后的数据
        processed_data_file = Path(self.temp_dir) / 'processed_cart_data.json'
        FileUtils.save_json(processed_data, processed_data_file)
        
        # 验证文件保存
        self.assertTrue(raw_data_file.exists())
        self.assertTrue(processed_data_file.exists())
        
        # 验证数据一致性
        loaded_processed_data = FileUtils.load_json(processed_data_file)
        self.assertEqual(loaded_processed_data, processed_data)

if __name__ == '__main__':
    unittest.main()