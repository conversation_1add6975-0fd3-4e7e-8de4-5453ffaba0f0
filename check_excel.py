#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件内容
"""

import pandas as pd
import sys
import os

def check_excel(file_path):
    """检查Excel文件内容"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"📄 文件: {file_path}")
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print("\n" + "="*80)
        print("📝 数据预览:")
        print(df.to_string())
        print("\n" + "="*80)
        
        # 检查关键字段
        key_fields = ['订单号', '商品名称', '单价', '数量', '总金额', '店铺名称']
        print("🔍 关键字段检查:")
        for field in key_fields:
            if field in df.columns:
                values = df[field].dropna().unique()
                print(f"  ✅ {field}: {len(values)} 个唯一值")
                if len(values) <= 5:
                    print(f"     值: {list(values)}")
                else:
                    print(f"     前5个值: {list(values[:5])}")
            else:
                print(f"  ❌ {field}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

if __name__ == "__main__":
    # 查找最新的订单文件
    reports_dir = "reports"
    if os.path.exists(reports_dir):
        files = [f for f in os.listdir(reports_dir) if f.startswith("1688_orders_") and f.endswith(".xlsx")]
        if files:
            # 按时间排序，取最新的
            files.sort(reverse=True)
            latest_file = os.path.join(reports_dir, files[0])
            check_excel(latest_file)
        else:
            print("❌ 未找到订单文件")
    else:
        print("❌ reports目录不存在")
