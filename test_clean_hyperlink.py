#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清理后的超链接功能
验证是否移除了多余的调试信息
"""

import sys
import os
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_path_cleaning():
    """测试路径清理功能"""
    print("🧹 测试路径清理功能...")
    
    test_paths = [
        "cart_report_20250812_171034.xlsx 📁",
        "D:/1688_automation_project/reports/file.xlsx 📁",
        "normal_file.xlsx",
        "file with spaces.xlsx 📁"
    ]
    
    for path in test_paths:
        cleaned = path.replace(' 📁', '').strip()
        print(f"原始: {path}")
        print(f"清理: {cleaned}")
        print()

def test_hyperlink_generation():
    """测试超链接生成（不显示调试信息）"""
    print("🔗 测试超链接生成...")
    
    # 创建测试文件
    reports_dir = PROJECT_ROOT / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    test_file = reports_dir / f"test_clean_{timestamp}.xlsx"
    
    # 创建测试Excel文件
    import pandas as pd
    df = pd.DataFrame({'测试': ['数据']})
    df.to_excel(str(test_file), index=False)
    
    print(f"✅ 创建测试文件: {test_file}")
    
    # 导入GUI模块
    from src.gui.qt_real import RealFunctionApp
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = RealFunctionApp()
    
    # 测试消息
    test_message = f"Excel报告已生成: {test_file}"
    enhanced = window.enhance_message_with_links(test_message)
    
    print(f"\n📝 超链接生成结果:")
    print(f"原始: {test_message}")
    print(f"增强: {enhanced}")
    
    # 检查是否包含📁图标在正确位置
    if '📁</a>' in enhanced:
        print("✅ 📁图标位置正确（在</a>标签内）")
    else:
        print("⚠️ 📁图标位置可能有问题")
    
    # 清理测试文件
    test_file.unlink()
    print(f"🧹 清理测试文件")

def main():
    """主函数"""
    print("清理后的超链接功能测试")
    print("=" * 40)
    
    # 1. 测试路径清理
    test_path_cleaning()
    
    # 2. 测试超链接生成
    test_hyperlink_generation()
    
    print(f"\n🎯 预期效果:")
    print("1. 点击超链接时不再显示调试信息")
    print("2. 文件路径中的📁图标被正确清理")
    print("3. 只在出错时显示错误信息")
    print("4. 用户体验更简洁清爽")

if __name__ == "__main__":
    main()
