# 1688采购车数据提取优化说明

## 优化概述

基于采购车页面实际DOM结构，重新设计了数据提取逻辑，支持多规格/款式商品的精准提取。

## 主要改进

### 1. 精准的DOM结构定位
- **主容器**: `[class*="shop-container--container--"]` - 每个商品店铺容器
- **生产商**: `[class*="shop-top--companyName--"]` - 商品生产商名称
- **主图**: `[class*="fancy-image"][class*="item-group--image--"] img` - 商品主图
- **商品名**: `[class*="item-group--title--"]` - 基础商品标题

### 2. 多规格/款式支持
- **规格图片**: `[class*="fancy-image"][class*="item--image--"] img` - 规格图片
- **规格标题**: `[class*="item--titleText--"]` - 规格名称
- **数量**: `span[class*="next-input"][class*="next-medium"] input` - 商品数量
- **发布价格**: `[class*="item--publishPrice--"]` - 原价
- **优惠价格**: `[class*="item--rebatePrice--"]` - 优惠价
- **小计**: `[class*="item--subtotal--"]` - 小计金额

### 3. 智能数据处理
- **图片选择**: 优先使用规格图片，没有则使用主图
- **标题选择**: 优先使用规格标题，没有则使用基础标题
- **价格计算**: 自动计算小计，支持价格验证
- **数据验证**: 只有数量>0的商品项才会被提取

## 数据结构对比

### 优化前
| 字段 | 说明 |
|------|------|
| 序号 | 商品序号 |
| 商品名称 | 商品标题 |
| 单价 | 商品价格 |
| 数量 | 购买数量 |
| 小计 | 总金额 |
| 图片链接 | 商品图片 |
| 商品链接 | 商品链接 |

### 优化后
| 字段 | 说明 |
|------|------|
| 序号 | 商品项序号 |
| 商品名称 | 显示标题（规格或基础） |
| 生产商 | 商品生产商名称 |
| 基础标题 | 商品基础标题 |
| 规格标题 | 规格具体名称 |
| 主图链接 | 商品主图链接 |
| 规格图片链接 | 规格图片链接 |
| 图片链接 | 最终显示图片链接 |
| 数量 | 购买数量 |
| 发布价格 | 原价 |
| 优惠价格 | 优惠后价格 |
| 单价 | 实际单价 |
| 小计 | 小计金额 |
| 店铺序号 | 店铺容器序号 |
| 商品序号 | 商品项序号 |

## 使用场景示例

### 场景1：单规格商品
- 商品：T恤
- 生产商：ABC服装厂
- 主图：tshirt_main.jpg
- 数量：2件
- 价格：¥29.9

**输出**：
```
序号: 1
商品名称: T恤
生产商: ABC服装厂
基础标题: T恤
规格标题: (空)
主图链接: tshirt_main.jpg
规格图片链接: (空)
图片链接: tshirt_main.jpg
数量: 2
```

### 场景2：多规格商品
- 商品：运动鞋
- 生产商：XYZ体育用品
- 主图：shoes_main.jpg
- 规格1：红色-42码，图片：shoes_red.jpg，数量：1
- 规格2：蓝色-43码，图片：shoes_blue.jpg，数量：2

**输出**：
```
序号: 1
商品名称: 红色-42码
生产商: XYZ体育用品
基础标题: 运动鞋
规格标题: 红色-42码
主图链接: shoes_main.jpg
规格图片链接: shoes_red.jpg
图片链接: shoes_red.jpg
数量: 1

序号: 2
商品名称: 蓝色-43码
生产商: XYZ体育用品
基础标题: 运动鞋
规格标题: 蓝色-43码
主图链接: shoes_main.jpg
规格图片链接: shoes_blue.jpg
图片链接: shoes_blue.jpg
数量: 2
```

## 使用方式

1. **启动调试浏览器**：
   ```bash
   start_debug_chrome.bat
   ```

2. **在Chrome中登录1688并导航到购物车页面**

3. **运行优化后的导出脚本**：
   ```bash
   python export_cart_excel.py --output cart_data.xlsx
   ```

## 技术特点

1. **精确匹配**：使用CSS选择器精确匹配1688页面结构
2. **容错处理**：多层try-catch确保单个商品提取失败不影响整体
3. **数据验证**：只提取有效数据（数量>0）
4. **智能回退**：规格数据缺失时自动使用基础数据
5. **性能优化**：先滚动加载所有商品，再一次性提取

## 注意事项

1. 确保Chrome调试端口9222可用
2. 需要在Chrome中完成1688登录
3. 购物车页面需要完全加载
4. 建议在页面稳定后再执行提取