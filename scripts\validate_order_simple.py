#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单验证订单数据抓取工具的存在性和基本结构
"""

import os
import sys
import json

# 设置控制台编码
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

def validate_order_structure():
    """验证订单数据抓取工具的基本结构"""
    print("🔍 验证订单数据抓取工具...")
    
    # 检查文件是否存在
    if not os.path.exists('extract_orders.py'):
        print("❌ extract_orders.py 文件不存在")
        return False
    
    print("✅ extract_orders.py 文件存在")
    
    # 检查文件内容
    try:
        with open('extract_orders.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和函数
        if 'class OrderDataExtractor' in content:
            print("✅ OrderDataExtractor 类存在")
        else:
            print("❌ OrderDataExtractor 类不存在")
            return False
        
        if 'async def extract_orders_from_api' in content:
            print("✅ API提取方法存在")
        else:
            print("❌ API提取方法不存在")
            return False
        
        if 'async def extract_orders_from_dom' in content:
            print("✅ DOM提取方法存在")
        else:
            print("❌ DOM提取方法不存在")
            return False
        
        # 检查依赖
        required_imports = ['playwright', 'pandas', 'asyncio']
        for imp in required_imports:
            if imp in content:
                print(f"✅ {imp} 依赖存在")
            else:
                print(f"❌ {imp} 依赖不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def validate_test_data():
    """验证测试数据"""
    print("\n🔍 验证测试数据...")
    
    test_file = 'test_output/mock_orders.json'
    if not os.path.exists(test_file):
        print("❌ 测试数据文件不存在")
        return False
    
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 测试数据文件存在，包含 {len(data)} 条记录")
        
        # 检查数据结构
        if isinstance(data, list) and len(data) > 0:
            first_record = data[0]
            required_fields = ['order_id', 'product_name', 'quantity', 'price']
            
            for field in required_fields:
                if field in first_record:
                    print(f"✅ 字段 {field} 存在")
                else:
                    print(f"❌ 字段 {field} 不存在")
                    return False
            
            return True
        else:
            print("❌ 测试数据格式不正确")
            return False
            
    except Exception as e:
        print(f"❌ 读取测试数据失败: {e}")
        return False

def validate_dependencies():
    """验证依赖包"""
    print("\n🔍 验证依赖包...")
    
    required_packages = [
        'playwright',
        'pandas', 
        'openpyxl',
        'PIL',
        'requests'
    ]
    
    all_ok = True
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            all_ok = False
    
    return all_ok

def main():
    """主函数"""
    print("🚀 开始验证订单数据抓取工具...")
    print("=" * 50)
    
    results = []
    
    # 验证文件结构
    results.append(("文件结构", validate_order_structure()))
    
    # 验证测试数据
    results.append(("测试数据", validate_test_data()))
    
    # 验证依赖
    results.append(("依赖包", validate_dependencies()))
    
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体得分: {passed/total*100:.1f}% ({passed}/{total})")
    
    if passed == total:
        print("🎉 所有验证都通过了！订单数据抓取工具已准备就绪！")
    else:
        print("⚠️ 部分验证未通过，请检查相关问题。")

if __name__ == "__main__":
    main()