# -*- coding: utf-8 -*-
"""
1688自动化项目异常定义
统一异常类型，便于错误处理和日志记录
"""

class AutomationException(Exception):
    """基础异常类"""
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class BrowserConnectionError(AutomationException):
    """浏览器连接异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "BROWSER_CONNECTION_ERROR", details)

class DataExtractionError(AutomationException):
    """数据提取异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "DATA_EXTRACTION_ERROR", details)

class FileOperationError(AutomationException):
    """文件操作异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "FILE_OPERATION_ERROR", details)

class NetworkError(AutomationException):
    """网络请求异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "NETWORK_ERROR", details)

class ConfigurationError(AutomationException):
    """配置错误异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "CONFIGURATION_ERROR", details)

class ChromeDebugError(AutomationException):
    """Chrome调试模式异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "CHROME_DEBUG_ERROR", details)

class ImageProcessingError(AutomationException):
    """图片处理异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "IMAGE_PROCESSING_ERROR", details)

class ExcelGenerationError(AutomationException):
    """Excel生成异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "EXCEL_GENERATION_ERROR", details)

class AuthenticationError(AutomationException):
    """认证异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "AUTHENTICATION_ERROR", details)

class TimeoutError(AutomationException):
    """超时异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "TIMEOUT_ERROR", details)

class ValidationError(AutomationException):
    """数据验证异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "VALIDATION_ERROR", details)

class PerformanceError(AutomationException):
    """性能异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "PERFORMANCE_ERROR", details)