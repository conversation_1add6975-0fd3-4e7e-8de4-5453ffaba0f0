"""
步骤管理器
"""

from abc import ABC, abstractmethod
from typing import List, Callable, Optional, Any
import threading
import time

class BaseStep(ABC):
    """步骤基类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.is_completed = False
        self.is_running = False
        self.error_message = None
        self.result = None
        self.progress = 0.0
        self.status_message = ""
    
    @abstractmethod
    def execute(self, progress_callback: Callable[[float, str, Any], None]) -> None:
        """执行步骤"""
        pass
    
    def can_proceed(self) -> bool:
        """检查是否可以进行下一步"""
        return self.is_completed and self.error_message is None
    
    def get_status(self) -> str:
        """获取步骤状态"""
        if self.error_message:
            return "error"
        elif self.is_running:
            return "running"
        elif self.is_completed:
            return "completed"
        else:
            return "pending"
    
    def reset(self):
        """重置步骤状态"""
        self.is_completed = False
        self.is_running = False
        self.error_message = None
        self.result = None
        self.progress = 0.0
        self.status_message = ""

class StepManager:
    """步骤管理器"""
    
    def __init__(self):
        self.steps: List[BaseStep] = []
        self.current_step_index = 0
        self.is_processing = False
        self.progress_callbacks: List[Callable] = []
        self.completion_callbacks: List[Callable] = []
        self.error_callbacks: List[Callable] = []
    
    def add_step(self, step: BaseStep):
        """添加步骤"""
        self.steps.append(step)
    
    def remove_step(self, index: int):
        """移除步骤"""
        if 0 <= index < len(self.steps):
            self.steps.pop(index)
    
    def get_current_step(self) -> Optional[BaseStep]:
        """获取当前步骤"""
        if 0 <= self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None
    
    def get_step_count(self) -> int:
        """获取步骤总数"""
        return len(self.steps)
    
    def get_current_step_index(self) -> int:
        """获取当前步骤索引"""
        return self.current_step_index
    
    def can_proceed_to_next(self) -> bool:
        """检查是否可以进入下一步"""
        current_step = self.get_current_step()
        if current_step:
            return current_step.can_proceed()
        return False
    
    def next_step(self) -> bool:
        """进入下一步"""
        if self.current_step_index < len(self.steps) - 1:
            self.current_step_index += 1
            return True
        return False
    
    def previous_step(self) -> bool:
        """返回上一步"""
        if self.current_step_index > 0:
            self.current_step_index -= 1
            return True
        return False
    
    def jump_to_step(self, index: int) -> bool:
        """跳转到指定步骤"""
        if 0 <= index < len(self.steps):
            self.current_step_index = index
            return True
        return False
    
    def execute_current_step(self):
        """执行当前步骤"""
        if self.is_processing:
            return
        
        current_step = self.get_current_step()
        if not current_step:
            return
        
        self.is_processing = True
        current_step.is_running = True
        current_step.reset()
        
        # 在新线程中执行步骤
        def worker():
            try:
                current_step.execute(self._progress_callback)
                
                if current_step.error_message:
                    self._handle_error(current_step.error_message, current_step)
                else:
                    current_step.is_completed = True
                    self._notify_completion(current_step)
                    
            except Exception as e:
                current_step.error_message = str(e)
                self._handle_error(str(e), current_step)
            finally:
                current_step.is_running = False
                self.is_processing = False
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def _progress_callback(self, progress: float, message: str, result: Any = None):
        """进度回调"""
        current_step = self.get_current_step()
        if current_step:
            current_step.progress = progress
            current_step.status_message = message
            if result is not None:
                current_step.result = result
        
        # 通知所有进度回调
        for callback in self.progress_callbacks:
            try:
                callback(self.current_step_index, progress, message, result)
            except Exception:
                pass
    
    def _notify_completion(self, step: BaseStep):
        """通知步骤完成"""
        for callback in self.completion_callbacks:
            try:
                callback(self.current_step_index, step)
            except Exception:
                pass
    
    def _handle_error(self, error_message: str, step: BaseStep):
        """处理错误"""
        for callback in self.error_callbacks:
            try:
                callback(self.current_step_index, error_message, step)
            except Exception:
                pass
    
    def add_progress_callback(self, callback: Callable):
        """添加进度回调"""
        self.progress_callbacks.append(callback)
    
    def add_completion_callback(self, callback: Callable):
        """添加完成回调"""
        self.completion_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable):
        """添加错误回调"""
        self.error_callbacks.append(callback)
    
    def reset_all_steps(self):
        """重置所有步骤"""
        for step in self.steps:
            step.reset()
        self.current_step_index = 0
        self.is_processing = False
    
    def get_progress_summary(self) -> dict:
        """获取进度摘要"""
        completed_steps = sum(1 for step in self.steps if step.is_completed)
        total_steps = len(self.steps)
        
        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "current_step": self.current_step_index + 1,
            "overall_progress": (completed_steps / total_steps * 100) if total_steps > 0 else 0,
            "is_processing": self.is_processing
        }
    
    def can_finish(self) -> bool:
        """检查是否可以完成"""
        return all(step.is_completed for step in self.steps)
    
    def get_step_by_name(self, name: str) -> Optional[BaseStep]:
        """根据名称获取步骤"""
        for step in self.steps:
            if step.name == name:
                return step
        return None
    
    def get_step_status_list(self) -> List[dict]:
        """获取所有步骤状态列表"""
        status_list = []
        for i, step in enumerate(self.steps):
            status_list.append({
                "index": i,
                "name": step.name,
                "description": step.description,
                "status": step.get_status(),
                "progress": step.progress,
                "message": step.status_message,
                "is_current": i == self.current_step_index
            })
        return status_list