"""
演示程序 - 简化版本用于测试和演示
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import json
import os
from pathlib import Path

class DemoApp:
    """演示应用程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("1688采购车数据处理工具 - 演示版")
        self.root.geometry("800x600")
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_styles(self):
        """设置样式"""
        self.root.configure(bg="#f5f5f5")
        
        # 创建字体
        self.title_font = ("微软雅黑", 16, "bold")
        self.header_font = ("微软雅黑", 12)
        self.normal_font = ("微软雅黑", 10)
        
        # 颜色主题
        self.primary_color = "#2196F3"
        self.success_color = "#4CAF50"
        self.warning_color = "#FF9800"
        self.error_color = "#f44336"
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.root, bg="#f5f5f5", height=80)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        title_frame.pack_propagate(False)
        
        tk.Label(
            title_frame,
            text="1688采购车数据处理工具 - 演示版",
            font=self.title_font,
            bg="#f5f5f5",
            fg="#333"
        ).pack(expand=True)
        
        # 功能选择区域
        self.create_function_selection()
        
        # 进度显示区域
        self.create_progress_area()
        
        # 日志显示区域
        self.create_log_area()
        
        # 按钮区域
        self.create_button_area()
    
    def create_function_selection(self):
        """创建功能选择区域"""
        function_frame = tk.LabelFrame(
            self.root,
            text="选择功能",
            font=self.header_font,
            bg="#f5f5f5",
            padx=20,
            pady=10
        )
        function_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        
        # 功能选择
        self.function_var = tk.StringVar(value="cart")
        
        tk.Radiobutton(
            function_frame,
            text="🛒 采购车数据提取 - 从1688采购车页面提取商品数据",
            variable=self.function_var,
            value="cart",
            font=self.normal_font,
            bg="#f5f5f5",
            command=self.on_function_change
        ).pack(anchor=tk.W, pady=5)
        
        tk.Radiobutton(
            function_frame,
            text="📋 订单数据增强 - 为Excel订单文件添加商品信息",
            variable=self.function_var,
            value="order",
            font=self.normal_font,
            bg="#f5f5f5",
            command=self.on_function_change
        ).pack(anchor=tk.W, pady=5)
    
    def create_progress_area(self):
        """创建进度显示区域"""
        progress_frame = tk.LabelFrame(
            self.root,
            text="处理进度",
            font=self.header_font,
            bg="#f5f5f5",
            padx=20,
            pady=10
        )
        progress_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=400
        )
        self.progress_bar.pack(pady=5)
        
        # 状态标签
        self.status_label = tk.Label(
            progress_frame,
            text="准备就绪",
            font=self.normal_font,
            bg="#f5f5f5"
        )
        self.status_label.pack()
    
    def create_log_area(self):
        """创建日志显示区域"""
        log_frame = tk.LabelFrame(
            self.root,
            text="操作日志",
            font=self.header_font,
            bg="#f5f5f5",
            padx=20,
            pady=10
        )
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))
        
        # 创建文本框和滚动条
        scrollbar = tk.Scrollbar(log_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.log_text = tk.Text(
            log_frame,
            height=10,
            wrap=tk.WORD,
            yscrollcommand=scrollbar.set,
            bg="#f8f8f8",
            font=("Consolas", 9)
        )
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=self.log_text.yview)
        
        # 配置文本标签
        self.log_text.tag_config("info", foreground="#333")
        self.log_text.tag_config("success", foreground="#4CAF50")
        self.log_text.tag_config("warning", foreground="#FF9800")
        self.log_text.tag_config("error", foreground="#f44336")
    
    def create_button_area(self):
        """创建按钮区域"""
        button_frame = tk.Frame(self.root, bg="#f5f5f5")
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # 开始按钮
        self.start_button = tk.Button(
            button_frame,
            text="开始处理",
            font=self.normal_font,
            bg=self.primary_color,
            fg="white",
            padx=20,
            pady=10,
            command=self.start_processing
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = tk.Button(
            button_frame,
            text="停止",
            font=self.normal_font,
            bg=self.error_color,
            fg="white",
            padx=20,
            pady=10,
            command=self.stop_processing,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空日志按钮
        self.clear_button = tk.Button(
            button_frame,
            text="清空日志",
            font=self.normal_font,
            bg="#e0e0e0",
            fg="#333",
            padx=20,
            pady=10,
            command=self.clear_log
        )
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 退出按钮
        self.exit_button = tk.Button(
            button_frame,
            text="退出",
            font=self.normal_font,
            bg="#757575",
            fg="white",
            padx=20,
            pady=10,
            command=self.on_closing
        )
        self.exit_button.pack(side=tk.RIGHT)
    
    def on_function_change(self):
        """功能选择改变事件"""
        function = self.function_var.get()
        self.log_info(f"已选择功能：{'采购车数据提取' if function == 'cart' else '订单数据增强'}")
    
    def start_processing(self):
        """开始处理"""
        function = self.function_var.get()
        
        # 禁用开始按钮
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 清空进度
        self.progress_var.set(0)
        
        # 在新线程中处理
        def worker():
            try:
                if function == "cart":
                    self.simulate_cart_processing()
                else:
                    self.simulate_order_processing()
            except Exception as e:
                self.log_error(f"处理失败：{str(e)}")
            finally:
                # 恢复按钮状态
                self.root.after(0, self.reset_buttons)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def stop_processing(self):
        """停止处理"""
        self.log_warning("处理已停止")
        self.reset_buttons()
    
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def simulate_cart_processing(self):
        """模拟采购车数据处理"""
        self.log_info("开始采购车数据处理演示...")
        
        steps = [
            "启动Chrome调试模式",
            "连接到1688采购车页面",
            "提取商品数据",
            "下载商品图片",
            "处理和标准化数据",
            "生成Excel报告",
            "保存结果文件"
        ]
        
        for i, step in enumerate(steps):
            if hasattr(self, '_stop_processing'):
                break
                
            self.log_info(f"步骤 {i+1}/{len(steps)}：{step}")
            
            # 模拟处理时间
            for j in range(101):
                if hasattr(self, '_stop_processing'):
                    break
                    
                progress = (i * 100 + j) / len(steps)
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda s=step: self.status_label.config(text=f"正在处理：{s}"))
                
                time.sleep(0.02)
        
        self.log_success("采购车数据处理完成！")
        self.root.after(0, lambda: self.status_label.config(text="处理完成"))
    
    def simulate_order_processing(self):
        """模拟订单数据处理"""
        self.log_info("开始订单数据处理演示...")
        
        # 选择文件
        self.root.after(0, lambda: self.status_label.config(text="请选择Excel文件"))
        file_path = filedialog.askopenfilename(
            title="选择Excel订单文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            self.log_warning("未选择文件，处理取消")
            return
        
        self.log_info(f"已选择文件：{os.path.basename(file_path)}")
        
        steps = [
            "读取Excel文件",
            "分析数据结构",
            "识别商品信息字段",
            "搜索商品图片和链接",
            "增强订单数据",
            "生成增强后的Excel文件",
            "保存结果"
        ]
        
        for i, step in enumerate(steps):
            if hasattr(self, '_stop_processing'):
                break
                
            self.log_info(f"步骤 {i+1}/{len(steps)}：{step}")
            
            # 模拟处理时间
            for j in range(101):
                if hasattr(self, '_stop_processing'):
                    break
                    
                progress = (i * 100 + j) / len(steps)
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda s=step: self.status_label.config(text=f"正在处理：{s}"))
                
                time.sleep(0.03)
        
        self.log_success("订单数据处理完成！")
        self.root.after(0, lambda: self.status_label.config(text="处理完成"))
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_info("日志已清空")
    
    def log_info(self, message: str):
        """添加信息日志"""
        self.add_log(message, "info")
    
    def log_success(self, message: str):
        """添加成功日志"""
        self.add_log(message, "success")
    
    def log_warning(self, message: str):
        """添加警告日志"""
        self.add_log(message, "warning")
    
    def log_error(self, message: str):
        """添加错误日志"""
        self.add_log(message, "error")
    
    def add_log(self, message: str, tag: str = "info"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n", tag)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        line_count = int(self.log_text.index('end-1c').split('.')[0])
        if line_count > 1000:
            self.log_text.delete('1.0', '2.0')
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = DemoApp()
    app.run()