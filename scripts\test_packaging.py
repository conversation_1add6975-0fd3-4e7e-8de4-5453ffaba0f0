#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本
"""

import os
import sys
import subprocess
import shutil

def test_pyinstaller_packaging():
    """测试PyInstaller打包"""
    print("开始测试PyInstaller打包...")
    
    # 创建临时目录
    temp_dir = "temp_packaging"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 复制必要的文件
    files_to_copy = [
        "ui_app/qt_ultralight.py",
        "start_debug_chrome.bat",
        "enrich_orders_with_images.py"
    ]
    
    for file_path in files_to_copy:
        if os.path.exists(file_path):
            shutil.copy2(file_path, os.path.join(temp_dir, os.path.basename(file_path)))
            print(f"[OK] 复制文件: {file_path}")
        else:
            print(f"[ERROR] 文件不存在: {file_path}")
    
    # 进入临时目录
    os.chdir(temp_dir)
    
    try:
        # 使用PyInstaller打包
        print("[INFO] 开始打包...")
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--noconfirm", "--clean",
            "--name", "1688数据工具",
            "--windowed",
            "--onedir",
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=scipy",
            "--exclude-module=pandas",
            "--exclude-module=openpyxl",
            "--exclude-module=PIL",
            "--exclude-module=aiohttp",
            "--exclude-module=asyncio",
            "--exclude-module=difflib",
            "--exclude-module=hashlib",
            "--exclude-module=requests",
            "--exclude-module=urllib3",
            "--exclude-module=certifi",
            "--exclude-module=chardet",
            "--exclude-module=idna",
            "--exclude-module=click",
            "--exclude-module=colorama",
            "--exclude-module=tqdm",
            "--exclude-module=pytest",
            "--exclude-module=unittest",
            "--exclude-module=docutils",
            "--exclude-module=sphinx",
            "--exclude-module=jinja2",
            "--exclude-module=markupsafe",
            "--exclude-module=flask",
            "--exclude-module=django",
            "--exclude-module=sqlalchemy",
            "--exclude-module=beautifulsoup4",
            "--exclude-module=lxml",
            "--exclude-module=html5lib",
            "--exclude-module=PyQt6.QtWebEngine",
            "--exclude-module=PyQt6.QtWebEngineWidgets",
            "--exclude-module=PyQt6.QtMultimedia",
            "--exclude-module=PyQt6.QtMultimediaWidgets",
            "--exclude-module=PyQt6.QtBluetooth",
            "--exclude-module=PyQt6.QtPositioning",
            "--exclude-module=PyQt6.QtSensors",
            "--exclude-module=PyQt6.QtSerialPort",
            "--exclude-module=PyQt6.QtTest",
            "--exclude-module=PyQt6.QtDesigner",
            "--exclude-module=PyQt6.QtHelp",
            "--exclude-module=PyQt6.QtSql",
            "--exclude-module=PyQt6.QtXml",
            "--exclude-module=PyQt6.QtNetwork",
            "--exclude-module=PyQt6.QtOpenGL",
            "--exclude-module=PyQt6.QtQuick",
            "--exclude-module=PyQt6.QtQml",
            "--collect-all", "PyQt6.QtWidgets",
            "--collect-all", "PyQt6.QtCore",
            "--collect-all", "PyQt6.QtGui",
            "--icon=NONE",
            "qt_ultralight.py"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("[OK] 打包成功!")
            print("[INFO] 输出目录:", os.path.join("dist", "1688数据工具"))
            
            # 计算文件大小
            dist_path = os.path.join("dist", "1688数据工具")
            if os.path.exists(dist_path):
                total_size = 0
                for root, dirs, files in os.walk(dist_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                
                size_mb = total_size / (1024 * 1024)
                print(f"[INFO] 打包大小: {size_mb:.2f} MB")
                
                # 检查可执行文件
                exe_path = os.path.join(dist_path, "1688数据工具.exe")
                if os.path.exists(exe_path):
                    print(f"[OK] 可执行文件: {exe_path}")
                else:
                    print("[ERROR] 可执行文件未找到")
            else:
                print("[ERROR] 输出目录不存在")
                
            return True
        else:
            print("[ERROR] 打包失败!")
            print("[ERROR] 错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"[ERROR] 打包过程出错: {e}")
        return False
    finally:
        # 返回原目录
        os.chdir("..")
        
        # 清理临时文件
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print("[INFO] 清理临时文件完成")

def main():
    """主函数"""
    print("=" * 60)
    print("1688数据工具 - PyInstaller打包测试")
    print("=" * 60)
    
    # 测试打包
    success = test_pyinstaller_packaging()
    
    print("\n" + "=" * 60)
    if success:
        print("[OK] 打包测试成功")
        print("[OK] 可以使用以下命令进行完整打包:")
        print("  pyinstaller --noconfirm --clean --name \"1688数据工具\" --windowed --onedir ui_app/qt_ultralight.py")
    else:
        print("[ERROR] 打包测试失败")
        print("[ERROR] 请检查PyInstaller安装和依赖")
    print("=" * 60)

if __name__ == "__main__":
    main()