#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome调试连接修复测试脚本
测试修复后的503错误处理逻辑
"""

import asyncio
import sys
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

async def test_chrome_connection():
    """测试Chrome连接修复效果"""
    print("[测试] Chrome调试连接修复测试")
    print("=" * 50)
    
    # 模拟GUI程序的连接逻辑
    from playwright.async_api import async_playwright
    debug_port = 9222
    
    print(f"[信息] 测试连接到Chrome调试端口: {debug_port}")
    
    # 修复后的连接参数
    max_retries = 8
    initial_delay = 3
    max_delay = 15
    
    async with async_playwright() as playwright:
        for attempt in range(max_retries):
            try:
                print(f"\n[重试] 连接尝试 {attempt + 1}/{max_retries}...")
                
                # 根据尝试次数调整超时时间
                timeout = 5000 if attempt < 3 else 8000
                print(f"[超时] 超时设置: {timeout}ms")
                
                start_time = time.time()
                browser = await playwright.chromium.connect_over_cdp(f"http://localhost:{debug_port}", timeout=timeout)
                end_time = time.time()
                
                print(f"[成功] 连接成功! 耗时: {end_time - start_time:.2f}秒")
                
                # 检查页面
                if browser.contexts and browser.contexts[0].pages:
                    pages = browser.contexts[0].pages
                    print(f"[页面] 找到 {len(pages)} 个页面:")
                    for i, page in enumerate(pages):
                        print(f"   {i+1}. {page.url}")
                        
                        if '1688.com' in page.url:
                            print(f"[目标] 找到1688页面: {page.url}")
                
                await browser.close()
                return True
                
            except Exception as e:
                error_msg = str(e).lower()
                elapsed_time = time.time() - start_time if 'start_time' in locals() else 0
                
                print(f"[失败] 连接失败 (耗时: {elapsed_time:.2f}秒)")
                
                # 分析错误类型并提供针对性建议
                if "503" in error_msg:
                    if attempt < 3:
                        print(f"[警告] Chrome服务暂时不可用 (503) - 正在启动中")
                        print(f"[提示] 请等待Chrome完全初始化调试接口...")
                    else:
                        print(f"[警告] Chrome调试接口初始化超时 (503)")
                        print(f"[提示] 可能需要重新启动Chrome调试模式")
                elif "econnrefused" in error_msg or "connection refused" in error_msg:
                    print(f"[错误] 连接被拒绝，Chrome调试服务未启动")
                    if attempt >= 2:
                        print(f"[提示] 建议重新运行 start_debug_chrome.bat")
                elif "timeout" in error_msg:
                    print(f"[警告] 连接超时，Chrome响应过慢")
                else:
                    print(f"[错误] 连接失败: {e}")
                
                if attempt < max_retries - 1:
                    # 改进的退避策略
                    if attempt < 2:
                        delay = initial_delay
                    elif attempt < 5:
                        delay = min(initial_delay * (2 ** (attempt - 1)), max_delay)
                    else:
                        delay = max_delay
                    
                    print(f"[等待] 等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    print(f"\n[失败] 所有连接尝试均失败")
                    print(f"[解决] 故障排除建议:")
                    print(f"   1. 关闭所有Chrome进程")
                    print(f"   2. 重新运行 start_debug_chrome.bat")
                    print(f"   3. 等待30秒让Chrome完全启动")
                    print(f"   4. 在Chrome中登录1688并打开采购车页面")
                    print(f"   5. 确保端口9222未被其他程序占用")
                    return False

async def test_chrome_service_check():
    """测试Chrome服务状态检查"""
    print("\n[检查] Chrome服务状态检查")
    print("=" * 30)
    
    try:
        import requests
        proxies = {'http': None, 'https': None}
        
        # 测试基本的HTTP连接
        url = f"http://localhost:9222/json/version"
        print(f"[信息] 测试URL: {url}")
        
        response = requests.get(url, timeout=3, proxies=proxies)
        
        if response.status_code == 200:
            data = response.json()
            print(f"[成功] Chrome调试服务正常运行")
            print(f"[信息] 版本信息:")
            print(f"   Browser: {data.get('Browser', 'Unknown')}")
            print(f"   Protocol-Version: {data.get('Protocol-Version', 'Unknown')}")
            print(f"   WebSocket URL: {data.get('webSocketDebuggerUrl', 'Not available')}")
            return True
        else:
            print(f"[错误] 服务响应异常: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionRefused:
        print(f"[错误] 连接被拒绝 - Chrome调试服务未启动")
        return False
    except requests.exceptions.Timeout:
        print(f"[错误] 连接超时 - Chrome调试服务响应过慢")
        return False
    except Exception as e:
        print(f"[错误] 检查失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("[开始] 开始Chrome调试连接修复测试")
    print("=" * 60)
    
    # 首先检查服务状态
    service_ok = await test_chrome_service_check()
    
    if service_ok:
        print(f"\n[成功] Chrome调试服务状态正常，开始连接测试...")
        connection_ok = await test_chrome_connection()
        
        if connection_ok:
            print(f"\n[完成] 所有测试通过！Chrome连接修复成功！")
        else:
            print(f"\n[失败] 连接测试失败，需要进一步检查")
    else:
        print(f"\n[警告] Chrome调试服务未运行")
        print(f"请先运行 start_debug_chrome.bat 启动Chrome调试模式")
        
        # 询问是否要等待并重试
        try:
            response = input("\n是否要等待30秒后重试？(y/N): ").strip().lower()
            if response == 'y':
                print("[等待] 等待30秒...")
                await asyncio.sleep(30)
                service_ok = await test_chrome_service_check()
                if service_ok:
                    connection_ok = await test_chrome_connection()
                    if connection_ok:
                        print(f"\n[成功] 重试成功！Chrome连接修复有效！")
                    else:
                        print(f"\n[失败] 重试仍然失败")
                else:
                    print(f"\n[失败] Chrome服务仍未启动")
        except KeyboardInterrupt:
            print(f"\n[中断] 测试被用户中断")

if __name__ == "__main__":
    asyncio.run(main())