"""
打包脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def build_exe():
    """打包成可执行文件"""
    print("开始打包应用程序...")
    
    # 检查当前目录
    current_dir = Path(__file__).parent
    os.chdir(current_dir)
    
    # 安装依赖（跳过tkinter，它是标准库）
    print("正在安装依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
    except subprocess.CalledProcessError:
        print("警告：部分依赖安装失败，继续打包...")
    
    # 打包命令
    cmd = [
        "pyinstaller",
        "--name=1688采购车数据处理工具",
        "--windowed",  # 无控制台窗口
        "--onefile",   # 打包成单个文件
        "--icon=assets/icon.ico" if Path("assets/icon.ico").exists() else "",
        "--add-data=config.json;.",  # 包含配置文件
        "--hidden-import=pandas",
        "--hidden-import=openpyxl", 
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=playwright",
        "--hidden-import=psutil",
        "--collect-all=pandas",
        "--collect-all=openpyxl",
        "--collect-all=requests",
        "--collect-all=PIL",
        "--collect-all=playwright",
        "--collect-all=psutil",
        "main.py"
    ]
    
    # 移除空参数
    cmd = [arg for arg in cmd if arg]
    
    print(f"执行打包命令：{' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
        print("打包完成！")
        print(f"可执行文件位于：{current_dir / 'dist' / '1688采购车数据处理工具.exe'}")
    except subprocess.CalledProcessError as e:
        print(f"打包失败：{e}")
        return False
    
    return True

if __name__ == "__main__":
    build_exe()