#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON文件点击问题
重现JSON内容显示在程序窗口的问题
"""

import sys
import os
import time
import json
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def create_test_json():
    """创建测试JSON文件"""
    print("📄 创建测试JSON文件...")
    
    data_dir = PROJECT_ROOT / "data"
    data_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    test_file = data_dir / f"test_json_click_{timestamp}.json"
    
    # 创建测试数据
    test_data = {
        "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
        "测试目的": "验证JSON文件点击行为",
        "商品数据": [
            {
                "商品名称": "测试商品1",
                "价格": 99.99,
                "数量": 2
            },
            {
                "商品名称": "测试商品2", 
                "价格": 199.99,
                "数量": 1
            }
        ],
        "总计": 299.97
    }
    
    # 保存JSON文件
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试文件: {test_file}")
    return test_file

def test_json_file_association():
    """测试JSON文件关联"""
    print("\n🔍 检查JSON文件关联...")
    
    import subprocess
    
    try:
        # 检查JSON文件的默认关联程序
        result = subprocess.run(
            ['assoc', '.json'],
            capture_output=True, text=True, shell=True
        )
        
        if result.returncode == 0:
            print(f"JSON文件关联: {result.stdout.strip()}")
            
            # 获取关联程序的详细信息
            file_type = result.stdout.strip().split('=')[1]
            result2 = subprocess.run(
                ['ftype', file_type],
                capture_output=True, text=True, shell=True
            )
            
            if result2.returncode == 0:
                print(f"关联程序: {result2.stdout.strip()}")
        else:
            print("未找到JSON文件关联")
            
    except Exception as e:
        print(f"检查文件关联失败: {e}")

def test_gui_with_json():
    """测试GUI中的JSON文件点击"""
    print("\n🖥️ 启动GUI测试JSON文件点击...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 创建测试JSON文件
        test_file = create_test_json()
        
        # 添加包含JSON文件路径的日志消息
        window.add_log(f"测试JSON文件已创建: {test_file}", "success")
        window.add_log("请点击上面的JSON文件链接，观察是否有异常行为", "info")
        window.add_log("正常情况下应该只打开JSON文件，不应该在程序中显示内容", "info")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI已启动")
        print("💡 请在GUI中:")
        print("   1. 点击JSON文件的蓝色链接")
        print("   2. 观察是否有JSON内容显示在程序日志中")
        print("   3. 关闭GUI窗口完成测试")
        
        # 运行GUI事件循环
        app.exec()
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
            print(f"🧹 清理测试文件: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("JSON文件点击问题测试")
    print("=" * 40)
    
    # 1. 检查JSON文件关联
    test_json_file_association()
    
    # 2. 询问是否启动GUI测试
    choice = input("\n是否启动GUI测试? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        test_gui_with_json()
    else:
        print("跳过GUI测试")
    
    print(f"\n🎯 如果发现JSON内容显示在程序中，可能的原因:")
    print("1. JSON文件关联的程序有特殊行为")
    print("2. 某个程序将输出重定向到了当前窗口")
    print("3. 系统剪贴板或其他机制的影响")
    print("4. PyQt6的QTextBrowser有特殊的JSON处理逻辑")

if __name__ == "__main__":
    main()
