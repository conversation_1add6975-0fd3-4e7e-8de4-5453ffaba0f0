#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析1688订单页面结构
详细查看页面中的订单相关元素
"""

import asyncio
import sys
import requests
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

async def analyze_order_page():
    """分析订单页面结构"""
    print("🔍 分析1688订单页面结构...")
    
    try:
        from playwright.async_api import async_playwright
        
        # 获取WebSocket URL
        debug_port = 9222
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例")
            
            # 查找1688页面
            page = None
            if browser.contexts and browser.contexts[0].pages:
                for p in enumerate(browser.contexts[0].pages):
                    if '1688.com' in p[1].url:
                        page = p[1]
                        break
            
            if not page:
                print("❌ 未找到1688页面")
                await browser.close()
                return
            
            # 导航到订单页面
            order_url = "https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html?tradeStatus=waitbuyerreceive&spm=a260k.home2025.topmenu.dmyorder&page=1&pageSize=10"
            print(f"🔄 导航到订单页面...")
            await page.goto(order_url, timeout=30000, wait_until="domcontentloaded")
            await page.wait_for_timeout(5000)
            
            print(f"📄 页面标题: {await page.title()}")
            print(f"📍 当前URL: {page.url}")
            
            # 分析页面结构
            print("\n🔍 分析页面结构...")
            
            # 1. 查找所有包含订单相关class的元素
            print("\n1️⃣ 包含'order'关键词的元素:")
            order_elements = await page.query_selector_all('[class*="order"]')
            print(f"   找到 {len(order_elements)} 个元素")
            for i, elem in enumerate(order_elements[:5]):  # 只显示前5个
                class_name = await elem.get_attribute('class')
                tag_name = await elem.evaluate('el => el.tagName')
                print(f"   {i+1}. <{tag_name.lower()}> class='{class_name}'")
            
            # 2. 查找所有包含贸易相关class的元素
            print("\n2️⃣ 包含'trade'关键词的元素:")
            trade_elements = await page.query_selector_all('[class*="trade"]')
            print(f"   找到 {len(trade_elements)} 个元素")
            for i, elem in enumerate(trade_elements[:5]):  # 只显示前5个
                class_name = await elem.get_attribute('class')
                tag_name = await elem.evaluate('el => el.tagName')
                print(f"   {i+1}. <{tag_name.lower()}> class='{class_name}'")
            
            # 3. 查找可能的订单列表容器
            print("\n3️⃣ 可能的订单列表容器:")
            list_selectors = [
                '[class*="list"]',
                '[class*="item"]',
                '[class*="card"]',
                '[class*="row"]'
            ]
            
            for selector in list_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"   {selector}: {len(elements)} 个元素")
            
            # 4. 查找表格结构
            print("\n4️⃣ 表格结构:")
            tables = await page.query_selector_all('table')
            print(f"   找到 {len(tables)} 个表格")
            
            # 5. 检查是否有订单数据
            print("\n5️⃣ 检查订单数据:")
            
            # 尝试查找常见的订单信息
            order_info_selectors = [
                '[class*="order-id"]',
                '[class*="orderid"]',
                '[class*="order-number"]',
                '[class*="trade-id"]',
                '[class*="tradeid"]',
                'td:contains("订单号")',
                'span:contains("订单号")',
                'div:contains("订单号")'
            ]
            
            found_order_info = False
            for selector in order_info_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"   ✅ 找到订单信息元素: {selector} ({len(elements)} 个)")
                        found_order_info = True
                        break
                except:
                    continue
            
            if not found_order_info:
                print("   ⚠️ 未找到明确的订单信息元素")
            
            # 6. 检查页面是否为空
            print("\n6️⃣ 检查页面内容:")
            
            # 查找"暂无数据"或类似的提示
            empty_indicators = [
                'text="暂无数据"',
                'text="没有订单"',
                'text="暂无订单"',
                '[class*="empty"]',
                '[class*="no-data"]'
            ]
            
            is_empty = False
            for indicator in empty_indicators:
                try:
                    elements = await page.query_selector_all(f'*:has-text("{indicator.replace("text=", "").replace('"', '')}")')
                    if elements:
                        print(f"   ⚠️ 发现空数据提示: {indicator}")
                        is_empty = True
                        break
                except:
                    continue
            
            if not is_empty:
                print("   ✅ 页面包含数据内容")
            
            # 7. 获取页面文本内容样本
            print("\n7️⃣ 页面文本内容样本:")
            try:
                page_text = await page.inner_text('body')
                # 只显示前200个字符
                sample_text = page_text[:200].replace('\n', ' ').strip()
                print(f"   {sample_text}...")
            except:
                print("   无法获取页面文本")
            
            await browser.close()
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("1688订单页面结构分析工具")
    print("=" * 50)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print("❌ Chrome调试接口不可用")
            print("💡 请先运行 start_debug_chrome.bat 启动Chrome调试模式")
            return
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return
    
    await analyze_order_page()

if __name__ == "__main__":
    asyncio.run(main())
