"""
辅助函数模块
"""

import time
import threading
from typing import Callable, Any
from datetime import datetime, timedelta

def format_duration(seconds: int) -> str:
    """格式化持续时间"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds}秒"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours}小时{remaining_minutes}分"

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f}KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f}MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f}GB"

def calculate_progress(current: int, total: int) -> float:
    """计算进度百分比"""
    if total == 0:
        return 0.0
    return (current / total) * 100

def estimate_remaining_time(current: int, total: int, elapsed_time: float) -> str:
    """估算剩余时间"""
    if current == 0:
        return "估算中..."
    
    rate = current / elapsed_time
    remaining_items = total - current
    
    if rate == 0:
        return "无法估算"
    
    remaining_seconds = remaining_items / rate
    return format_duration(int(remaining_seconds))

def debounce(func: Callable, wait_time: float = 0.3):
    """防抖装饰器"""
    last_called = [0]
    
    def debounced(*args, **kwargs):
        def call_function():
            func(*args, **kwargs)
        
        current_time = time.time()
        if current_time - last_called[0] >= wait_time:
            last_called[0] = current_time
            call_function()
    
    return debounced

def throttle(func: Callable, interval: float = 0.1):
    """节流装饰器"""
    last_called = [0]
    
    def throttled(*args, **kwargs):
        current_time = time.time()
        if current_time - last_called[0] >= interval:
            last_called[0] = current_time
            return func(*args, **kwargs)
    
    return throttled

def safe_execute(func: Callable, default_value: Any = None, log_errors: bool = True):
    """安全执行函数"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if log_errors:
                print(f"函数执行失败: {func.__name__} - {e}")
            return default_value
    return wrapper

def retry(func: Callable, max_attempts: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def wrapper(*args, **kwargs):
        last_exception = None
        
        for attempt in range(max_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_attempts - 1:
                    time.sleep(delay)
                    delay *= 2  # 指数退避
        
        raise last_exception
    
    return wrapper

def is_valid_url(url: str) -> bool:
    """检查URL是否有效"""
    import re
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None

def sanitize_filename(filename: str) -> str:
    """清理文件名"""
    import re
    # 移除或替换非法字符
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除控制字符
    sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', sanitized)
    # 移除前后空格和点
    sanitized = sanitized.strip('. ')
    # 限制长度
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    
    return sanitized or "unnamed"

def get_timestamp() -> str:
    """获取时间戳字符串"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳字符串"""
    try:
        return datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
    except ValueError:
        return datetime.now()

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int, description: str = "处理中"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
        self.callbacks = []
    
    def add_callback(self, callback: Callable):
        """添加进度回调函数"""
        self.callbacks.append(callback)
    
    def update(self, current: int, message: str = ""):
        """更新进度"""
        self.current = current
        
        progress = calculate_progress(self.current, self.total)
        elapsed = time.time() - self.start_time
        remaining = estimate_remaining_time(self.current, self.total, elapsed)
        
        for callback in self.callbacks:
            try:
                callback(progress, message, remaining)
            except Exception:
                pass
    
    def increment(self, message: str = ""):
        """增加进度"""
        self.update(self.current + 1, message)
    
    def finish(self):
        """完成进度"""
        self.update(self.total, "完成")

class ThreadSafeCounter:
    """线程安全计数器"""
    
    def __init__(self, initial_value: int = 0):
        self.value = initial_value
        self.lock = threading.Lock()
    
    def increment(self, amount: int = 1) -> int:
        """增加计数"""
        with self.lock:
            self.value += amount
            return self.value
    
    def decrement(self, amount: int = 1) -> int:
        """减少计数"""
        with self.lock:
            self.value -= amount
            return self.value
    
    def get(self) -> int:
        """获取当前值"""
        with self.lock:
            return self.value
    
    def set(self, value: int) -> int:
        """设置值"""
        with self.lock:
            self.value = value
            return self.value