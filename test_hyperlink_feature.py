#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超链接功能
创建测试文件并验证超链接是否正常工作
"""

import sys
import os
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def create_test_files():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    # 创建reports目录
    reports_dir = PROJECT_ROOT / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    # 创建data目录
    data_dir = PROJECT_ROOT / "data"
    data_dir.mkdir(exist_ok=True)
    
    # 创建测试Excel文件
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    test_files = [
        reports_dir / f"test_cart_report_{timestamp}.xlsx",
        reports_dir / f"test_orders_{timestamp}.xlsx",
        data_dir / f"test_cart_data_{timestamp}.json",
    ]
    
    for file_path in test_files:
        try:
            if file_path.suffix == '.xlsx':
                # 创建简单的Excel文件
                import pandas as pd
                df = pd.DataFrame({
                    '测试列1': ['测试数据1', '测试数据2'],
                    '测试列2': ['值1', '值2']
                })
                df.to_excel(str(file_path), index=False)
            else:
                # 创建JSON文件
                import json
                test_data = {
                    "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "测试数据": ["项目1", "项目2"]
                }
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(test_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 创建测试文件: {file_path}")
            
        except Exception as e:
            print(f"❌ 创建文件失败 {file_path}: {e}")
    
    return test_files

def test_hyperlink_enhancement():
    """测试超链接增强功能"""
    print("\n🔗 测试超链接增强功能...")
    
    # 导入GUI模块
    try:
        from src.gui.qt_real import RealFunctionApp
        
        # 创建应用实例（不显示GUI）
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口实例
        window = RealFunctionApp()
        
        # 测试消息增强功能
        test_messages = [
            f"Excel报告已生成: {PROJECT_ROOT}/reports/test_cart_report_20241212_163000.xlsx",
            f"数据已保存到: {PROJECT_ROOT}/data/test_cart_data_20241212_163000.json",
            f"订单数据已保存到: reports/test_orders_20241212_163000.xlsx",
            "这是一个普通消息，没有文件路径",
            f"多个文件: {PROJECT_ROOT}/reports/file1.xlsx 和 {PROJECT_ROOT}/data/file2.json"
        ]
        
        print("📝 测试消息增强:")
        for i, msg in enumerate(test_messages, 1):
            enhanced = window.enhance_message_with_links(msg)
            print(f"\n{i}. 原始消息:")
            print(f"   {msg}")
            print(f"   增强后:")
            print(f"   {enhanced}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_with_hyperlinks():
    """测试GUI中的超链接显示"""
    print("\n🖥️ 启动GUI测试超链接显示...")
    print("💡 请在GUI中查看日志区域的超链接是否可以点击")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 添加测试日志消息
        test_files = create_test_files()
        
        # 模拟各种成功消息
        for file_path in test_files:
            if file_path.suffix == '.xlsx':
                window.add_log(f"Excel报告已生成: {file_path}", "success")
            else:
                window.add_log(f"数据已保存到: {file_path}", "success")
        
        # 添加一些其他类型的消息
        window.add_log("这是一个普通的信息消息", "info")
        window.add_log("这是一个警告消息", "warning")
        window.add_log("处理完成！请检查上述文件链接是否可以点击", "success")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI已启动，请检查:")
        print("   1. 文件路径是否显示为蓝色超链接")
        print("   2. 点击文件名是否能打开文件")
        print("   3. 点击📁图标是否能打开目录")
        print("   4. 鼠标悬停是否显示完整路径")
        
        # 不自动退出，让用户测试
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("超链接功能测试工具")
    print("=" * 50)
    
    # 1. 创建测试文件
    test_files = create_test_files()
    
    # 2. 测试超链接增强功能
    enhancement_ok = test_hyperlink_enhancement()
    
    # 3. 询问是否启动GUI测试
    if enhancement_ok:
        print(f"\n🎯 超链接增强功能测试通过!")
        
        choice = input("\n是否启动GUI测试超链接显示? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            gui_ok = test_gui_with_hyperlinks()
            if gui_ok:
                print("\n💡 GUI测试已启动，请在GUI中验证超链接功能")
                print("   关闭GUI窗口后程序将退出")
                
                # 运行GUI事件循环
                from PyQt6.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    app.exec()
        else:
            print("跳过GUI测试")
    
    # 清理测试文件
    print(f"\n🧹 清理测试文件...")
    for file_path in test_files:
        try:
            if file_path.exists():
                file_path.unlink()
                print(f"✅ 删除: {file_path}")
        except Exception as e:
            print(f"⚠️ 删除失败 {file_path}: {e}")
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    main()
