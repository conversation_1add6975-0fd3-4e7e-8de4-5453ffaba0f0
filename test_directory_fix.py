#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试目录链接修复效果
"""

import sys
import os
import time
import json
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def create_test_files():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    # 创建目录
    reports_dir = PROJECT_ROOT / "reports"
    data_dir = PROJECT_ROOT / "data"
    reports_dir.mkdir(exist_ok=True)
    data_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 创建Excel文件
    import pandas as pd
    excel_file = reports_dir / f"test_dir_fix_{timestamp}.xlsx"
    df = pd.DataFrame({'测试': ['目录链接修复']})
    df.to_excel(str(excel_file), index=False)
    print(f"✅ 创建Excel文件: {excel_file}")
    
    # 创建JSON文件
    json_file = data_dir / f"test_dir_fix_{timestamp}.json"
    json_data = {"测试": "目录链接修复", "时间": timestamp}
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    print(f"✅ 创建JSON文件: {json_file}")
    
    return [excel_file, json_file]

def test_directory_link_generation():
    """测试目录链接生成"""
    print("\n🔗 测试目录链接生成...")
    
    # 创建测试文件
    test_files = create_test_files()
    
    # 导入GUI模块
    from src.gui.qt_real import RealFunctionApp
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = RealFunctionApp()
    
    # 测试不同类型的路径
    test_messages = []
    for file_path in test_files:
        # 绝对路径
        test_messages.append(f"文件已生成: {file_path}")
        # 相对路径
        rel_path = os.path.relpath(str(file_path), PROJECT_ROOT)
        test_messages.append(f"文件已生成: {rel_path}")
    
    for msg in test_messages:
        print(f"\n原始消息: {msg}")
        enhanced = window.enhance_message_with_links(msg)
        print(f"增强后: {enhanced}")
        
        # 提取目录链接
        import re
        dir_links = re.findall(r'<a href="file:///([^"]+)">📁</a>', enhanced)
        for link in dir_links:
            print(f"📁 目录链接: {link}")
            # 检查目录是否存在
            if os.path.exists(link.replace('/', '\\')):
                print(f"   ✅ 目录存在")
            else:
                print(f"   ❌ 目录不存在")
    
    # 清理测试文件
    for file_path in test_files:
        try:
            file_path.unlink()
            print(f"🧹 清理: {file_path}")
        except:
            pass

def test_gui_directory_links():
    """测试GUI中的目录链接"""
    print("\n🖥️ 启动GUI测试目录链接...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 创建测试文件
        test_files = create_test_files()
        
        # 添加包含文件路径的日志消息
        window.add_log("目录链接测试文件:", "info")
        for file_path in test_files:
            if file_path.suffix == '.xlsx':
                window.add_log(f"Excel文件: {file_path}", "success")
            else:
                window.add_log(f"JSON文件: {file_path}", "success")
        
        window.add_log("", "info")
        window.add_log("🎯 测试说明:", "info")
        window.add_log("1. 点击📁图标应该打开文件所在目录", "info")
        window.add_log("2. Excel文件应该打开reports目录", "info")
        window.add_log("3. JSON文件应该打开data目录", "info")
        window.add_log("4. 不应该打开文档目录或其他错误目录", "warning")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI已启动")
        print("💡 请在GUI中:")
        print("   1. 点击📁图标")
        print("   2. 确认打开的是正确的目录")
        print("   3. Excel文件应该打开reports目录")
        print("   4. JSON文件应该打开data目录")
        print("   5. 关闭GUI窗口完成测试")
        
        # 运行GUI事件循环
        app.exec()
        
        # 清理测试文件
        for file_path in test_files:
            try:
                if file_path.exists():
                    file_path.unlink()
                    print(f"🧹 清理: {file_path}")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def main():
    """主函数"""
    print("目录链接修复测试")
    print("=" * 30)
    
    print("🎯 修复说明:")
    print("1. 将相对路径转换为绝对路径")
    print("2. 确保目录链接指向正确的目录")
    print("3. 修复📁图标链接到文档目录的问题")
    
    # 1. 测试目录链接生成
    test_directory_link_generation()
    
    # 2. 询问是否启动GUI测试
    choice = input("\n是否启动GUI测试? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        test_gui_directory_links()
    else:
        print("跳过GUI测试")
    
    print(f"\n🎉 修复效果:")
    print("- 📁图标现在指向正确的目录")
    print("- 相对路径被转换为绝对路径")
    print("- 不再链接到错误的文档目录")

if __name__ == "__main__":
    main()
