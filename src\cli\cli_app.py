#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688采购车数据处理工具 - 命令行版本
适用于没有tkinter的环境
"""

import sys
import os
from pathlib import Path

# 定义项目根目录并添加到Python路径
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, str(PROJECT_ROOT))

def show_banner():
    """显示程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                 1688采购车数据处理工具 - 命令行版本                        ║
║                              (无GUI模式)                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("请选择功能:")
    print("1. 采购车数据提取")
    print("2. 订单数据增强")
    print("3. 订单数据抓取")
    print("4. 数据验证")
    print("5. 查看项目状态")
    print("0. 退出程序")
    print("="*60)

def cart_data_extraction():
    """采购车数据提取功能"""
    print("\n🛒 采购车数据提取模式")
    print("-" * 40)
    
    cart_script = PROJECT_ROOT / "src" / "core" / "export_cart_excel.py"
    if not cart_script.exists():
        print(f"❌ 找不到采购车数据提取脚本: {cart_script}")
        return
    
    print("📋 可用选项:")
    print("1. 基本提取 (自动生成文件名)")
    print("2. 指定输出文件")
    print("3. 使用调试模式")
    print("4. 返回主菜单")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        os.system(f"python \"{cart_script}\"")
    elif choice == "2":
        output_file = input("请输入输出文件路径: ").strip()
        if output_file:
            os.system(f"python \"{cart_script}\" --output \"{output_file}\"")
        else:
            print("❌ 文件路径不能为空")
    elif choice == "3":
        port = input("请输入调试端口 (默认9222): ").strip() or "9222"
        os.system(f"python \"{cart_script}\" --debug-port {port}")
    elif choice == "4":
        return
    else:
        print("❌ 无效选择")

def order_data_enhancement():
    """订单数据增强功能"""
    print("\n📦 订单数据增强模式")
    print("-" * 40)
    
    enhance_script = PROJECT_ROOT / "src" / "core" / "enrich_orders_with_images.py"
    if not enhance_script.exists():
        print(f"❌ 找不到订单数据增强脚本: {enhance_script}")
        return
    
    print("📋 可用选项:")
    print("1. 基本增强 (当前目录)")
    print("2. 指定输入文件")
    print("3. 指定输入输出文件")
    print("4. 返回主菜单")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        os.system(f"python \"{enhance_script}\"")
    elif choice == "2":
        input_file = input("请输入输入文件路径: ").strip()
        if input_file:
            os.system(f"python \"{enhance_script}\" --input \"{input_file}\"")
        else:
            print("❌ 文件路径不能为空")
    elif choice == "3":
        input_file = input("请输入输入文件路径: ").strip()
        output_file = input("请输入输出文件路径: ").strip()
        if input_file and output_file:
            os.system(f"python \"{enhance_script}\" --input \"{input_file}\" --output \"{output_file}\"")
        else:
            print("❌ 文件路径不能为空")
    elif choice == "4.":
        return
    else:
        print("❌ 无效选择")

def order_data_extraction():
    """订单数据抓取功能"""
    print("\n📊 订单数据抓取模式")
    print("-" * 40)
    
    extract_script = PROJECT_ROOT / "src" / "core" / "extract_orders.py"
    if not extract_script.exists():
        print(f"❌ 找不到订单数据抓取脚本: {extract_script}")
        return
    
    print("📋 可用选项:")
    print("1. 基本抓取 (默认URL)")
    print("2. 指定URL")
    print("3. 指定输出文件")
    print("4. 无头模式")
    print("5. 返回主菜单")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == "1":
        os.system(f"python \"{extract_script}\"")
    elif choice == "2":
        url = input("请输入订单页面URL: ").strip()
        if url:
            os.system(f"python \"{extract_script}\" --url \"{url}\"")
        else:
            print("❌ URL不能为空")
    elif choice == "3":
        output_file = input("请输入输出文件路径: ").strip()
        if output_file:
            os.system(f"python \"{extract_script}\" --output \"{output_file}\"")
        else:
            print("❌ 文件路径不能为空")
    elif choice == "4":
        os.system(f"python \"{extract_script}\" --headless")
    elif choice == "5":
        return
    else:
        print("❌ 无效选择")

def data_validation():
    """数据验证功能"""
    print("\n✅ 数据验证模式")
    print("-" * 40)
    
    print("📋 可用验证选项:")
    print("1. 项目整体验证")
    print("2. 采购车功能验证")
    print("3. 订单数据验证")
    print("4. 运行测试套件")
    print("5. 返回主菜单")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    validation_scripts = {
        "1": "scripts/validate_project.py",
        "2": "scripts/validate_optimization.py", 
        "3": "scripts/validate_order_data.py",
        "4": "tests/run_tests.py"
    }
    
    if choice in validation_scripts:
        script_path = PROJECT_ROOT / validation_scripts[choice]
        if script_path.exists():
            os.system(f"python \"{script_path}\"")
        else:
            print(f"❌ 找不到验证脚本: {validation_scripts[choice]}")
    elif choice == "5":
        return
    else:
        print("❌ 无效选择")

def show_project_status():
    """显示项目状态"""
    print("\n📈 项目状态")
    print("-" * 40)
    
    status_file = PROJECT_ROOT / "最终状态报告.md"
    if status_file.exists():
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            for line in lines:
                if "项目状态" in line or "验证结果" in line or "清理状态" in line:
                    print(f"✓ {line}")
                elif line.startswith("## "):
                    print(f"\n📋 {line[3:]}")
                    
        except Exception as e:
            print(f"❌ 读取状态报告失败: {e}")
    else:
        print("❌ 找不到项目状态报告")
    
    print("\n📁 项目文件统计:")
    python_files = list(PROJECT_ROOT.rglob("*.py"))
    print(f"  Python文件: {len(python_files)} 个")
    
    key_files = [
        "src/core/export_cart_excel.py",
        "src/core/extract_orders.py", 
        "src/core/enrich_orders_with_images.py",
        "scripts/validate_project.py"
    ]
    
    print("\n🔍 关键文件状态:")
    for file in key_files:
        file_path = PROJECT_ROOT / file
        status = "✅" if file_path.exists() else "❌"
        print(f"  {status} {file}")

def main():
    """主函数"""
    show_banner()
    
    print("🚀 程序启动成功!")
    print("💡 提示: 这是一个无GUI版本，所有功能都通过命令行操作")
    print("📝 如需帮助，请输入 'help' 或 'h'")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能 (0-5): ").strip().lower()
            
            if choice in ['0', 'exit', 'quit', 'q']:
                print("\n👋 感谢使用1688采购车数据处理工具!")
                print("🎉 程序正常退出")
                break
            elif choice in ['1', 'cart']:
                cart_data_extraction()
            elif choice in ['2', 'enhance']:
                order_data_enhancement()
            elif choice in ['3', 'extract']:
                order_data_extraction()
            elif choice in ['4', 'validate']:
                data_validation()
            elif choice in ['5', 'status']:
                show_project_status()
            elif choice in ['help', 'h']:
                show_help()
            else:
                print("❌ 无效选择，请重新输入")
                
            if choice not in ['0', 'exit', 'quit', 'q', 'help', 'h']:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n🛑 程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            input("按回车键继续...")

def show_help():
    """显示帮助信息"""
    print("\n📖 使用帮助")
    print("-" * 40)
    print("🔹 数字键 (1-5): 选择对应功能")
    print("🔹 字母键: 也可以使用功能名称的首字母")
    print("🔹 0/q/quit: 退出程序")
    print("🔹 h/help: 显示帮助信息")
    print("🔹 Ctrl+C: 强制退出程序")
    print("\n💡 使用提示:")
    print("1. 使用采购车数据提取前，请先运行start_debug_chrome.bat")
    print("2. 订单数据增强需要提供Excel文件作为输入")
    print("3. 所有功能都可以通过命令行参数直接调用")
    print("4. 详细使用说明请参考docs/README.md文件")

if __name__ == "__main__":
    main()
