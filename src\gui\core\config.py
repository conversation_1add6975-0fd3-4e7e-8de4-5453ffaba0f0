"""
配置管理模块
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

class Config:
    """配置管理类"""
    
    def __init__(self, config_file=None):
        if config_file is None:
            config_file = Path(__file__).parent.parent / "config.json"
        
        self.config_file = config_file
        self.config = self.load_config()
        self.ensure_directories()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.get_default_config()
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app_name": "1688采购车数据处理工具",
            "version": "1.0.0",
            "author": "开发团队",
            "chrome": {
                "debug_port": 9222,
                "timeout": 30,
                "retry_attempts": 3
            },
            "processing": {
                "max_workers": 5,
                "timeout": 300,
                "chunk_size": 10
            },
            "ui": {
                "window_width": 800,
                "window_height": 600,
                "progress_update_interval": 100
            },
            "paths": {
                "cache_dir": "cache",
                "logs_dir": "logs",
                "output_dir": "output"
            }
        }
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        base_dir = Path(__file__).parent.parent
        
        for path_key in self.config["paths"]:
            dir_path = base_dir / self.config["paths"][path_key]
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            raise ValueError(f"保存配置文件失败: {e}")
    
    def get_path(self, path_key: str) -> Path:
        """获取路径配置"""
        base_dir = Path(__file__).parent.parent
        return base_dir / self.config["paths"][path_key]
    
    @property
    def chrome_port(self) -> int:
        """获取Chrome调试端口"""
        return self.get("chrome.debug_port", 9222)
    
    @property
    def chrome_timeout(self) -> int:
        """获取Chrome超时时间"""
        return self.get("chrome.timeout", 30)
    
    @property
    def max_workers(self) -> int:
        """获取最大工作线程数"""
        return self.get("processing.max_workers", 5)
    
    @property
    def processing_timeout(self) -> int:
        """获取处理超时时间"""
        return self.get("processing.timeout", 300)
    
    @property
    def window_width(self) -> int:
        """获取窗口宽度"""
        return self.get("ui.window_width", 800)
    
    @property
    def window_height(self) -> int:
        """获取窗口高度"""
        return self.get("ui.window_height", 600)