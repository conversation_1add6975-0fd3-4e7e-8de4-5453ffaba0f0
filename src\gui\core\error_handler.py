"""
错误处理模块
"""

import traceback
from typing import Dict, Any, Optional

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_codes = {
            "CHROME_START_FAILED": {
                "message": "Chrome启动失败",
                "solution": "请检查Chrome是否已安装，或尝试手动启动Chrome浏览器"
            },
            "CHROME_PORT_BUSY": {
                "message": "Chrome调试端口被占用",
                "solution": "请关闭其他Chrome实例，或使用不同的端口"
            },
            "CHROME_TIMEOUT": {
                "message": "Chrome连接超时",
                "solution": "请检查网络连接，或重新启动Chrome浏览器"
            },
            "FILE_NOT_FOUND": {
                "message": "文件不存在",
                "solution": "请检查文件路径是否正确"
            },
            "INVALID_FORMAT": {
                "message": "文件格式不正确",
                "solution": "请选择有效的Excel文件（.xlsx或.xls）"
            },
            "NETWORK_ERROR": {
                "message": "网络连接错误",
                "solution": "请检查网络连接，或稍后重试"
            },
            "PERMISSION_DENIED": {
                "message": "权限不足",
                "solution": "请以管理员身份运行程序"
            },
            "DATA_EXTRACTION_FAILED": {
                "message": "数据提取失败",
                "solution": "请确保Chrome中的1688页面已正确加载，然后重试"
            },
            "IMAGE_DOWNLOAD_FAILED": {
                "message": "图片下载失败",
                "solution": "请检查网络连接，或跳过图片下载"
            },
            "EXCEL_PROCESSING_FAILED": {
                "message": "Excel处理失败",
                "solution": "请检查Excel文件是否损坏，或尝试另存为新文件"
            },
            "UNKNOWN_ERROR": {
                "message": "未知错误",
                "solution": "请联系技术支持"
            }
        }
    
    def handle_error(self, error_code: str, details: str = "") -> str:
        """处理错误并返回用户友好的错误信息"""
        error_info = self.error_codes.get(error_code, self.error_codes["UNKNOWN_ERROR"])
        
        # 构建错误信息
        error_message = f"错误：{error_info['message']}\n\n"
        error_message += f"解决方法：{error_info['solution']}"
        
        if details:
            error_message += f"\n\n详细信息：{details}"
        
        return error_message
    
    def get_error_code(self, exception: Exception) -> str:
        """根据异常类型返回错误代码"""
        error_type = type(exception).__name__
        
        error_mapping = {
            "FileNotFoundError": "FILE_NOT_FOUND",
            "PermissionError": "PERMISSION_DENIED",
            "ConnectionError": "NETWORK_ERROR",
            "TimeoutError": "CHROME_TIMEOUT",
            "ValueError": "INVALID_FORMAT",
            "RuntimeError": "DATA_EXTRACTION_FAILED"
        }
        
        return error_mapping.get(error_type, "UNKNOWN_ERROR")
    
    def log_error(self, exception: Exception, context: str = ""):
        """记录错误日志"""
        error_code = self.get_error_code(exception)
        error_info = self.error_codes.get(error_code, self.error_codes["UNKNOWN_ERROR"])
        
        # 记录详细错误信息
        error_details = {
            "error_code": error_code,
            "error_message": error_info["message"],
            "exception": str(exception),
            "traceback": traceback.format_exc(),
            "context": context
        }
        
        # 这里可以添加日志记录逻辑
        print(f"错误记录：{error_details}")
    
    def show_error_dialog(self, error_code: str, details: str = "", title: str = "错误"):
        """显示错误对话框"""
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            error_message = self.handle_error(error_code, details)
            messagebox.showerror(title, error_message)
        except ImportError:
            print(f"ERROR: {title} - {self.handle_error(error_code, details)}")
    
    def is_retryable(self, error_code: str) -> bool:
        """检查错误是否可重试"""
        retryable_errors = [
            "NETWORK_ERROR",
            "CHROME_TIMEOUT",
            "DATA_EXTRACTION_FAILED",
            "IMAGE_DOWNLOAD_FAILED"
        ]
        
        return error_code in retryable_errors
    
    def get_retry_suggestion(self, error_code: str) -> str:
        """获取重试建议"""
        suggestions = {
            "NETWORK_ERROR": "网络连接不稳定，建议稍后重试",
            "CHROME_TIMEOUT": "Chrome响应超时，建议重新启动Chrome后重试",
            "DATA_EXTRACTION_FAILED": "数据提取失败，建议刷新页面后重试",
            "IMAGE_DOWNLOAD_FAILED": "图片下载失败，建议检查网络后重试"
        }
        
        return suggestions.get(error_code, "建议检查设置后重试")