#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Excel保存功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.core.extract_orders import OrderDataExtractor

async def test_simple_excel():
    """简单测试Excel保存功能"""
    print("🧪 测试Excel保存功能...")
    
    # 创建提取器实例
    extractor = OrderDataExtractor()
    
    # 简单的测试数据（不包含图片URL，避免网络问题）
    test_data = [
        {
            'order_id': '12345678901234567',
            'title': '女装连衣裙',
            'product_specs': '红色, L码',
            'price': 89.90,
            'quantity': 1,
            'total_amount': 89.90,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'image_url': '',  # 空的图片URL
            'detail_url': 'https://detail.1688.com/offer/example1.html'
        },
        {
            'order_id': '12345678901234567',
            'title': '女装连衣裙',
            'product_specs': '蓝色, M码',
            'price': 89.90,
            'quantity': 2,
            'total_amount': 179.80,
            'shop_name': '开封市禹王台区臻颂日用百货店',
            'image_url': '',  # 空的图片URL
            'detail_url': 'https://detail.1688.com/offer/example1.html'
        },
        {
            'order_id': '98765432109876543',
            'title': '男士T恤',
            'product_specs': '白色, XL码',
            'price': 45.00,
            'quantity': 3,
            'total_amount': 135.00,
            'shop_name': '另一家供应商',
            'image_url': '',  # 空的图片URL
            'detail_url': 'https://detail.1688.com/offer/example2.html'
        }
    ]
    
    print(f"📊 测试数据: {len(test_data)} 个商品SKU")
    
    # 测试保存到Excel
    output_path = PROJECT_ROOT / "reports" / "test_simple_excel.xlsx"
    
    try:
        await extractor.save_to_excel(test_data, str(output_path))
        print("✅ Excel保存测试成功!")
        print(f"📄 文件已保存到: {output_path}")
        
        # 检查文件是否存在
        if output_path.exists():
            file_size = output_path.stat().st_size
            print(f"📏 文件大小: {file_size / 1024:.1f} KB")
        
    except Exception as e:
        print(f"❌ Excel保存测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 验证数据结构
    print("\n🔍 数据结构验证:")
    print("=" * 50)
    
    for i, item in enumerate(test_data):
        print(f"商品 {i+1}:")
        print(f"  订单号: {item['order_id']}")
        print(f"  商品名称: {item['title']}")
        print(f"  商品规格: {item['product_specs']}")
        print(f"  单价: ￥{item['price']}")
        print(f"  数量: {item['quantity']}")
        print(f"  小计: ￥{item['total_amount']}")
        print(f"  卖家: {item['shop_name']}")
        print()
    
    # 统计信息
    unique_orders = len(set(item['order_id'] for item in test_data))
    unique_suppliers = len(set(item['shop_name'] for item in test_data))
    total_amount = sum(item['total_amount'] for item in test_data)
    
    print("📈 统计信息:")
    print(f"  商品SKU总数: {len(test_data)}")
    print(f"  订单数量: {unique_orders}")
    print(f"  供应商数量: {unique_suppliers}")
    print(f"  总金额: ￥{total_amount:.2f}")

if __name__ == "__main__":
    asyncio.run(test_simple_excel())
