"""
UTF-8启动器 - 确保中文正确显示
"""

import sys
import os

# 设置UTF-8环境
if sys.platform == 'win32':
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 设置控制台代码页
    try:
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except:
        pass

# 导入并运行主程序
if __name__ == "__main__":
    # 获取要运行的脚本名
    if len(sys.argv) > 1:
        script_name = sys.argv[1]
        # 传递剩余参数
        sys.argv = [script_name] + sys.argv[2:]
        
        # 运行目标脚本
        with open(script_name, 'r', encoding='utf-8') as f:
            exec(compile(f.read(), script_name, 'exec'))
    else:
        print("用法: python utf8_launcher.py <脚本名> [参数...]")
