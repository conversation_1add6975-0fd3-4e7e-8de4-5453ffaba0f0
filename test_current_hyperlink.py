#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前超链接功能状态
"""

import sys
import os
import time
import json
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def create_test_file():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    reports_dir = PROJECT_ROOT / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    test_file = reports_dir / f"test_current_{timestamp}.xlsx"
    
    # 创建测试Excel文件
    import pandas as pd
    df = pd.DataFrame({'测试': ['当前状态检查']})
    df.to_excel(str(test_file), index=False)
    
    print(f"✅ 创建测试文件: {test_file}")
    return test_file

def test_hyperlink_generation():
    """测试超链接生成"""
    print("\n🔗 测试超链接生成...")
    
    # 创建测试文件
    test_file = create_test_file()
    
    # 导入GUI模块
    from src.gui.qt_real import RealFunctionApp
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = RealFunctionApp()
    
    # 测试消息
    test_message = f"Excel报告已生成: {test_file}"
    enhanced = window.enhance_message_with_links(test_message)
    
    print(f"\n📝 测试结果:")
    print(f"原始消息: {test_message}")
    print(f"增强后: {enhanced}")
    
    # 检查超链接
    if '<a href=' in enhanced:
        print("✅ 包含超链接")
        
        # 检查文件链接
        if 'file:///' in enhanced:
            print("✅ 包含file://协议")
        else:
            print("❌ 缺少file://协议")
        
        # 检查📁图标
        if '📁' in enhanced:
            print("✅ 包含📁图标")
        else:
            print("❌ 缺少📁图标")
            
        # 检查目录链接
        import re
        dir_links = re.findall(r'<a href="file:///([^"]+)">📁</a>', enhanced)
        if dir_links:
            print(f"✅ 找到目录链接: {dir_links[0]}")
        else:
            print("❌ 未找到目录链接")
    else:
        print("❌ 未生成超链接")
    
    # 清理测试文件
    test_file.unlink()
    print(f"🧹 清理测试文件")

def test_gui_display():
    """测试GUI显示"""
    print("\n🖥️ 测试GUI显示...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 创建测试文件
        test_file = create_test_file()
        
        # 添加测试消息
        window.add_log("当前超链接功能测试:", "info")
        window.add_log(f"测试文件: {test_file}", "success")
        window.add_log("", "info")
        window.add_log("🎯 检查项目:", "info")
        window.add_log("1. 文件名是否为蓝色超链接", "info")
        window.add_log("2. 是否有绿色📁图标", "info")
        window.add_log("3. 点击文件名是否能打开Excel", "info")
        window.add_log("4. 点击📁图标是否能打开reports目录", "info")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI已启动")
        print("💡 请检查:")
        print("   1. 文件名是否为蓝色超链接")
        print("   2. 是否有绿色📁图标")
        print("   3. 点击功能是否正常")
        print("   4. 关闭GUI窗口完成测试")
        
        # 运行GUI事件循环
        app.exec()
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
            print(f"🧹 清理测试文件: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("当前超链接功能状态检查")
    print("=" * 40)
    
    # 1. 测试超链接生成
    test_hyperlink_generation()
    
    # 2. 询问是否启动GUI测试
    choice = input("\n是否启动GUI测试? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        test_gui_display()
    else:
        print("跳过GUI测试")
    
    print(f"\n🎯 如果发现问题:")
    print("1. 文件链接无法点击 - 检查file://协议")
    print("2. 📁图标消失 - 检查HTML生成逻辑")
    print("3. 目录链接错误 - 检查路径处理")

if __name__ == "__main__":
    main()
