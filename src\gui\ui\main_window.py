"""
主窗口界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
from typing import Callable, Optional, Dict, Any
import threading
import time

from .step_manager import StepManager, BaseStep
from .components import (
    ProgressBar, LogViewer, StepIndicator, 
    StatusPanel, ActionButton, LoadingSpinner
)
from core.config import Config
from core.error_handler import <PERSON>rrorHandler
from utils.logger import setup_logger

class MainWindow:
    """主窗口类"""
    
    def __init__(self, parent, mode: str, config: Config, on_back: Callable):
        self.parent = parent
        self.mode = mode  # 'cart' 或 'order'
        self.config = config
        self.on_back = on_back
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title(self.get_window_title())
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # 居中显示
        self.center_window()
        
        # 初始化组件
        self.logger = setup_logger()
        self.error_handler = ErrorHandler()
        
        # 创建步骤管理器
        self.step_manager = StepManager()
        self.setup_steps()
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定事件
        self.setup_events()
        
        # 绑定窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 初始化界面状态
        self.update_ui_state()
    
    def get_window_title(self) -> str:
        """获取窗口标题"""
        if self.mode == 'cart':
            return "1688采购车数据提取工具"
        else:
            return "1688订单数据增强工具"
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_styles(self):
        """设置样式"""
        # 创建字体
        self.title_font = font.Font(family="微软雅黑", size=14, weight="bold")
        self.header_font = font.Font(family="微软雅黑", size=12)
        self.normal_font = font.Font(family="微软雅黑", size=10)
        
        # 设置颜色主题
        self.bg_color = "#f5f5f5"
        self.primary_color = "#2196F3"
        self.success_color = "#4CAF50"
        self.warning_color = "#FF9800"
        self.error_color = "#f44336"
        self.text_color = "#333333"
        self.border_color = "#dddddd"
        
        self.window.configure(bg=self.bg_color)
    
    def setup_steps(self):
        """设置步骤"""
        if self.mode == 'cart':
            self.setup_cart_steps()
        else:
            self.setup_order_steps()
        
        # 绑定回调
        self.step_manager.add_progress_callback(self.on_step_progress)
        self.step_manager.add_completion_callback(self.on_step_completed)
        self.step_manager.add_error_callback(self.on_step_error)
    
    def setup_cart_steps(self):
        """设置采购车模式步骤"""
        from core.cart_steps import (
            ChromeStep, ExtractCartDataStep, 
            ProcessCartDataStep, SaveCartResultStep
        )
        
        self.step_manager.add_step(ChromeStep(mode='cart'))
        self.step_manager.add_step(ExtractCartDataStep())
        self.step_manager.add_step(ProcessCartDataStep())
        self.step_manager.add_step(SaveCartResultStep())
    
    def setup_order_steps(self):
        """设置订单模式步骤"""
        from core.order_steps import (
            SelectOrderFileStep, AnalyzeOrderDataStep,
            EnhanceOrderDataStep, SaveOrderResultStep
        )
        
        self.step_manager.add_step(SelectOrderFileStep())
        self.step_manager.add_step(AnalyzeOrderDataStep())
        self.step_manager.add_step(EnhanceOrderDataStep())
        self.step_manager.add_step(SaveOrderResultStep())
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.window, bg=self.bg_color)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题栏
        self.create_title_bar(main_container)
        
        # 步骤指示器
        self.create_step_indicator(main_container)
        
        # 主要内容区域
        content_frame = tk.Frame(main_container, bg=self.bg_color)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 左侧：当前步骤区域
        left_frame = tk.Frame(content_frame, bg="white", relief=tk.RAISED, bd=1)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        self.create_step_content(left_frame)
        
        # 右侧：进度信息区域
        right_frame = tk.Frame(content_frame, bg=self.bg_color, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.pack_propagate(False)
        
        self.create_progress_panel(right_frame)
        
        # 底部按钮区域
        self.create_button_panel(main_container)
    
    def create_title_bar(self, parent):
        """创建标题栏"""
        title_frame = tk.Frame(parent, bg=self.bg_color, height=60)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        # 标题
        tk.Label(
            title_frame,
            text=self.get_window_title(),
            font=self.title_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(side=tk.LEFT, padx=20)
        
        # 返回按钮
        back_button = ActionButton(
            title_frame,
            text="← 返回",
            command=self.on_back,
            width=80,
            height=25
        )
        back_button.pack(side=tk.RIGHT, padx=20)
    
    def create_step_indicator(self, parent):
        """创建步骤指示器"""
        steps = [step.name for step in self.step_manager.steps]
        self.step_indicator = StepIndicator(parent, steps)
        self.step_indicator.pack(fill=tk.X, pady=(0, 10))
    
    def create_step_content(self, parent):
        """创建步骤内容区域"""
        # 步骤内容容器
        self.step_content_frame = tk.Frame(parent, bg="white")
        self.step_content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 初始显示欢迎信息
        self.show_welcome_content()
    
    def show_welcome_content(self):
        """显示欢迎内容"""
        # 清空内容区域
        for widget in self.step_content_frame.winfo_children():
            widget.destroy()
        
        # 欢迎信息
        welcome_text = self.get_welcome_text()
        
        tk.Label(
            self.step_content_frame,
            text=welcome_text,
            font=self.normal_font,
            bg="white",
            fg="#666666",
            justify=tk.LEFT,
            wraplength=500
        ).pack(expand=True)
    
    def get_welcome_text(self) -> str:
        """获取欢迎文本"""
        if self.mode == 'cart':
            return """欢迎使用1688采购车数据提取工具！

本工具将帮助您从1688采购车页面提取商品数据，并生成包含图片的Excel报告。

使用步骤：
1. 启动Chrome调试模式
2. 提取采购车数据
3. 处理和增强数据
4. 保存结果

请点击"开始处理"按钮开始使用。"""
        else:
            return """欢迎使用1688订单数据增强工具！

本工具将帮助您为已有的Excel订单文件添加商品图片和详情链接。

使用步骤：
1. 选择Excel订单文件
2. 分析订单数据
3. 增强数据（添加图片和链接）
4. 保存增强结果

请点击"开始处理"按钮开始使用。"""
    
    def create_progress_panel(self, parent):
        """创建进度面板"""
        # 进度信息标题
        tk.Label(
            parent,
            text="进度信息",
            font=self.header_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        # 总体进度
        progress_frame = tk.Frame(parent, bg=self.bg_color)
        progress_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        tk.Label(
            progress_frame,
            text="总体进度：",
            font=self.normal_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(anchor=tk.W)
        
        self.overall_progress = ProgressBar(parent, width=280)
        self.overall_progress.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 当前步骤进度
        self.step_progress = ProgressBar(parent, width=280)
        self.step_progress.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 状态信息
        self.status_panel = StatusPanel(parent)
        self.status_panel.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 操作日志
        tk.Label(
            parent,
            text="操作日志",
            font=self.header_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        self.log_viewer = LogViewer(parent, height=150)
        self.log_viewer.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
    
    def create_button_panel(self, parent):
        """创建按钮面板"""
        button_frame = tk.Frame(parent, bg=self.bg_color, height=60)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        button_frame.pack_propagate(False)
        
        # 左侧按钮
        left_button_frame = tk.Frame(button_frame, bg=self.bg_color)
        left_button_frame.pack(side=tk.LEFT, padx=20)
        
        self.start_button = ActionButton(
            left_button_frame,
            text="开始处理",
            command=self.start_processing,
            primary=True,
            width=100
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.pause_button = ActionButton(
            left_button_frame,
            text="暂停",
            command=self.pause_processing,
            width=80
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_button = ActionButton(
            left_button_frame,
            text="取消",
            command=self.cancel_processing,
            danger=True,
            width=80
        )
        self.cancel_button.pack(side=tk.LEFT)
        
        # 右侧按钮
        right_button_frame = tk.Frame(button_frame, bg=self.bg_color)
        right_button_frame.pack(side=tk.RIGHT, padx=20)
        
        self.prev_button = ActionButton(
            right_button_frame,
            text="上一步",
            command=self.previous_step,
            width=80
        )
        self.prev_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.next_button = ActionButton(
            right_button_frame,
            text="下一步",
            command=self.next_step,
            primary=True,
            width=80
        )
        self.next_button.pack(side=tk.RIGHT)
    
    def setup_events(self):
        """设置事件处理"""
        # 绑定键盘事件
        self.window.bind('<Control-n>', lambda e: self.next_step())
        self.window.bind('<Control-p>', lambda e: self.previous_step())
        self.window.bind('<Control-s>', lambda e: self.start_processing())
        self.window.bind('<Escape>', lambda e: self.cancel_processing())
    
    def update_ui_state(self):
        """更新UI状态"""
        current_step = self.step_manager.get_current_step()
        current_index = self.step_manager.get_current_step_index()
        
        # 更新步骤指示器
        self.step_indicator.set_current_step(current_index)
        
        # 更新按钮状态
        self.update_button_states()
        
        # 更新进度条
        self.update_progress_bars()
        
        # 更新状态面板
        if current_step:
            if current_step.is_running:
                self.status_panel.set_busy(f"正在执行：{current_step.name}")
            elif current_step.error_message:
                self.status_panel.set_error(f"错误：{current_step.error_message}")
            elif current_step.is_completed:
                self.status_panel.set_success(f"完成：{current_step.name}")
            else:
                self.status_panel.set_ready(f"准备：{current_step.name}")
    
    def update_button_states(self):
        """更新按钮状态"""
        current_step = self.step_manager.get_current_step()
        is_processing = self.step_manager.is_processing
        
        # 开始/暂停/取消按钮
        if is_processing:
            self.start_button.disable()
            self.pause_button.enable()
            self.cancel_button.enable()
        else:
            self.start_button.enable()
            self.pause_button.disable()
            self.cancel_button.disable()
        
        # 上一步/下一步按钮
        if is_processing:
            self.prev_button.disable()
            self.next_button.disable()
        else:
            # 上一步按钮
            if self.step_manager.get_current_step_index() > 0:
                self.prev_button.enable()
            else:
                self.prev_button.disable()
            
            # 下一步按钮
            if self.step_manager.can_proceed_to_next():
                self.next_button.enable()
            else:
                self.next_button.disable()
    
    def update_progress_bars(self):
        """更新进度条"""
        # 更新总体进度
        summary = self.step_manager.get_progress_summary()
        self.overall_progress.update_progress(
            summary["overall_progress"],
            f"步骤 {summary['current_step']}/{summary['total_steps']}"
        )
        
        # 更新当前步骤进度
        current_step = self.step_manager.get_current_step()
        if current_step:
            self.step_progress.update_progress(
                current_step.progress,
                current_step.status_message
            )
    
    def start_processing(self):
        """开始处理"""
        if not self.step_manager.is_processing:
            current_step = self.step_manager.get_current_step()
            if current_step and not current_step.is_completed:
                self.log_viewer.log_info(f"开始执行步骤：{current_step.name}")
                self.step_manager.execute_current_step()
            else:
                self.log_viewer.log_warning("当前步骤已完成，请进入下一步")
    
    def pause_processing(self):
        """暂停处理"""
        self.log_viewer.log_warning("暂停功能暂未实现")
    
    def cancel_processing(self):
        """取消处理"""
        if messagebox.askyesno("确认取消", "确定要取消当前处理吗？"):
            self.step_manager.reset_all_steps()
            self.update_ui_state()
            self.log_viewer.log_info("已取消处理")
    
    def next_step(self):
        """下一步"""
        if self.step_manager.can_proceed_to_next():
            self.step_manager.next_step()
            self.update_ui_state()
            self.log_viewer.log_info(f"进入步骤：{self.step_manager.get_current_step().name}")
        else:
            self.log_viewer.log_warning("当前步骤未完成，无法进入下一步")
    
    def previous_step(self):
        """上一步"""
        if self.step_manager.previous_step():
            self.update_ui_state()
            self.log_viewer.log_info(f"返回步骤：{self.step_manager.get_current_step().name}")
        else:
            self.log_viewer.log_warning("已经是第一步，无法返回")
    
    def on_step_progress(self, step_index: int, progress: float, message: str, result: Any):
        """步骤进度回调"""
        self.window.after(0, self.update_ui_state)
        self.log_viewer.log_info(f"步骤{step_index + 1}进度：{progress:.1f}% - {message}")
    
    def on_step_completed(self, step_index: int, step: BaseStep):
        """步骤完成回调"""
        self.window.after(0, self.update_ui_state)
        self.log_viewer.log_success(f"步骤{step_index + 1}完成：{step.name}")
        
        # 自动进入下一步
        if self.step_manager.can_proceed_to_next():
            self.window.after(1000, self.next_step)
    
    def on_step_error(self, step_index: int, error_message: str, step: BaseStep):
        """步骤错误回调"""
        self.window.after(0, self.update_ui_state)
        self.log_viewer.log_error(f"步骤{step_index + 1}错误：{error_message}")
        
        # 显示错误对话框
        self.window.after(0, lambda: messagebox.showerror(
            "处理错误",
            f"步骤执行失败：{error_message}"
        ))
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.step_manager.is_processing:
            if not messagebox.askyesno("确认退出", "处理正在进行中，确定要退出吗？"):
                return
        
        self.on_back()
    
    def destroy(self):
        """销毁窗口"""
        self.window.destroy()