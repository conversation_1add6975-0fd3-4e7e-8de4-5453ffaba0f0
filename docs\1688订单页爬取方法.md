## 1688 订单页爬取方法（接口法 / DOM 法 / 水印处理）

适用页面：`已买到的货品` / `订单列表` 等订单数据页。针对页面出现 `quark-watermark` 等固定定位的水印/遮罩、Shadow DOM 组件、图片懒加载等情况，提供可复用的抓取方案。

### 关键结论
- 出现 `quark-watermark` 背景图并不代表“数据被加密”，通常只是反爬水印/遮罩。订单数据依旧来自 DOM 或 XHR/Fetch 接口。
- 首选“接口 JSON 抓取”，辅以“DOM/Shadow DOM 解析”。图片建议走“元素截图”以规避直链限制。

---

### 方法总览
1) 接口法（推荐）：监听网络请求，直接解析返回的 JSON 订单数据。
2) DOM 法（含 Shadow DOM）：使用 `:light()` 选择器穿透 Web Components 获取可见文本与字段。
3) 水印处理：隐藏或移除水印层，避免视觉干扰（不影响数据抓取逻辑）。
4) 图片获取：优先对 `img` 元素做截图，或在懒加载容器中取真实 `src`/`data-src`。
5) 反爬规避与排查：真实浏览行为、随机延迟、快照核对、滚动触发懒加载。

---

### 一、接口法（抓取订单 JSON）
思路：在 Network 面板找到订单列表的 XHR/Fetch 请求；在自动化中监听响应并解析。

```python
# Playwright（Python）示例：监听订单接口响应
from playwright.sync_api import sync_playwright
import json

def run():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        def handle_response(response):
            url = response.url
            if "/order" in url and response.request.resource_type == "xhr":
                try:
                    data = response.json()
                    # TODO: 根据返回结构提取：订单号、店铺、商品、单价、数量、小计、下单时间等
                    print("订单接口:", url)
                    print(json.dumps(data)[:500])
                except Exception:
                    pass

        page.on("response", handle_response)

        page.goto("https://trade.1688.com/order/buyer_order_list.htm", timeout=60000)
        page.wait_for_load_state("networkidle")

        # 如有分页/筛选，模拟点击并等待；监听将持续工作
        # ...

        # 保持一段时间以捕获响应
        page.wait_for_timeout(5000)
        browser.close()

if __name__ == "__main__":
    run()
```

要点：
- 过滤条件：`"/order" in url` 与 `resource_type == "xhr"`；可按实际接口路径调整。
- 对于 gzip/二进制响应，仍可用 `response.json()` 或 `response.text()` 转换。

---

### 二、DOM / Shadow DOM 法
当接口不易复用时，从页面结构直接抽取。1688 订单页常由 Web Components 渲染，需穿透 Shadow DOM。

```python
# 使用 :light() 选择器穿透 Shadow DOM
rows = page.locator(":light(.order-list .order-item)")
titles = rows.locator(":light(.title)").all_text_contents()
prices = rows.locator(":light(.price)").all_text_contents()
quantities = rows.locator(":light(.quantity)").all_text_contents()

# 如需在浏览器环境做更复杂解析：
order_data = page.evaluate(
    """
    () => {
      const toText = el => (el?.textContent || "").trim();
      const items = Array.from(document.querySelectorAll(".order-list .order-item"));
      return items.map(it => ({
        title: toText(it.querySelector(".title")),
        price: toText(it.querySelector(".price")),
        quantity: toText(it.querySelector(".quantity")),
      }));
    }
    """
)
```

提示：
- Shadow DOM 选择器在 Playwright 中使用 `:light()`；在原生 JS 中需 `element.shadowRoot.querySelector(...)`。
- 懒加载区域需先滚动：`page.evaluate("window.scrollTo(0, document.body.scrollHeight)")` 或 `locator.scroll_into_view_if_needed()`。

---

### 三、水印/遮罩处理
页面可能注入 `div.quark-watermark` 等 fixed 层，影响截屏但不影响抓取。可隐藏：

```python
await page.add_style_tag(content=".quark-watermark{display:none!important}")
# 或移除（浏览器上下文）：
page.evaluate("document.querySelectorAll('.quark-watermark').forEach(el=>el.remove())")
```

---

### 四、图片获取策略
结合本仓库规则（见 `web-scraping`）：优先对图片元素截图，避免直链防盗链问题。

```python
# 元素截图（推荐）
img = page.locator(":light(img)").first
if img.is_visible():
    png_bytes = img.screenshot(type="png")
    # 将 png_bytes 写入文件或嵌入 Excel

# 直链提取（备用）：懒加载场景
# 常见容器：<div class="lazyload-wrapper item-group--lazyLoad--*">
src = page.evaluate(
    """
    () => {
      const img = document.querySelector('img');
      return img?.getAttribute('src') || img?.getAttribute('data-src') || '';
    }
    """
)
```

---

### 五、反爬规避与稳定性
- 真实浏览行为：随机等待、滚动、鼠标移动；避免高频操作。
- 等待策略：`wait_for_load_state("networkidle")` + 针对列表容器的可见性断言。
- 分页/筛选：交互后再次监听接口与解析 DOM。
- 登录：需手动一次登录，复用已登录的用户数据目录（或在 MCP 工具中保留会话）。

---

### 六、排查流程（与 MCP 工具配合）
1. `navigate` 到订单页 → `snapshot` 核对结构是否加载完全。
2. `network` 检查是否存在订单 XHR/Fetch（若有，优先接口法）。
3. 若无接口或跨域受限 → 使用 `:light()` 解析 DOM；必要时滚动触发懒加载。
4. 图片：优先元素截图；若要 URL，检查 `lazyload-wrapper` 与 `data-src`。
5. 水印：注入样式隐藏或移除 `quark-watermark`。

常用提示语见根目录 `MCP执行提示词.txt`（第 9 行提到图片 URL 常见容器）。

---

### 七、与现有脚本的衔接
- 结构化数据拿到后，可调用本项目 `create_cart_excel_report_with_images.py` 生成含图片的 Excel 报告。
- 若仅抓采购车，可参考 `1688_cart_with_images_automation.py` 的等待与图片处理逻辑。

---

### 附：极简工作流建议
1. 打开订单页并完成登录 → 等待 `networkidle`。
2. 监听响应，优先抓接口 JSON；若失败，回落到 DOM/Shadow DOM。
3. 对每个商品做元素截图（或提取 `data-src`）。
4. 清洗字段 → 调用现有 Excel 生成脚本。

---

任务引用：`DOC-02 使用说明编写`（本次新增文档）。


