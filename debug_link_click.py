#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试超链接点击问题
"""

import sys
import os
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_url_processing():
    """测试URL处理"""
    print("🔍 测试URL处理...")
    
    from urllib.parse import unquote
    
    # 模拟实际的URL
    test_urls = [
        "file:///D:/1688_automation_project/reports",
        "file:///D:/1688_automation_project/data",
        "file:///D:/1688_automation_project/reports/test.xlsx",
        "file:///D:/1688_automation_project/data/test.json"
    ]
    
    for url_str in test_urls:
        print(f"\n原始URL: {url_str}")
        
        # 模拟handle_link_clicked的处理逻辑
        if url_str.startswith('file:///'):
            file_path = unquote(url_str[8:])  # 移除file:///并解码
        else:
            file_path = unquote(url_str)  # 解码URL
        
        # 清理路径中的特殊字符（如📁图标）
        file_path = file_path.replace(' 📁', '').strip()
        
        print(f"处理后路径: {file_path}")
        print(f"路径存在: {os.path.exists(file_path)}")
        print(f"是文件: {os.path.isfile(file_path)}")
        print(f"是目录: {os.path.isdir(file_path)}")

def create_debug_gui():
    """创建调试GUI"""
    print("\n🖥️ 创建调试GUI...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QUrl
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 重写handle_link_clicked方法添加调试信息
        original_handle_link_clicked = window.handle_link_clicked
        
        def debug_handle_link_clicked(url):
            print(f"\n🔗 调试信息:")
            print(f"   接收到的URL对象: {url}")
            print(f"   URL字符串: {url.toString()}")
            print(f"   URL scheme: {url.scheme()}")
            print(f"   URL path: {url.path()}")
            
            # 添加日志到GUI
            window.add_log(f"🔗 点击URL: {url.toString()}", "info")
            
            # 调用原始方法
            return original_handle_link_clicked(url)
        
        # 替换方法
        window.handle_link_clicked = debug_handle_link_clicked
        
        # 添加测试消息
        window.add_log("调试模式已启用", "info")
        window.add_log("测试目录链接:", "info")
        window.add_log(f"Reports目录: {PROJECT_ROOT}/reports/test.xlsx", "success")
        window.add_log(f"Data目录: {PROJECT_ROOT}/data/test.json", "success")
        window.add_log("", "info")
        window.add_log("💡 点击📁图标查看调试信息", "warning")
        
        # 显示窗口
        window.show()
        
        print("✅ 调试GUI已启动")
        print("💡 请在GUI中:")
        print("   1. 点击📁图标")
        print("   2. 查看控制台的调试信息")
        print("   3. 查看GUI中的调试日志")
        print("   4. 关闭GUI窗口完成调试")
        
        # 运行GUI事件循环
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ 调试GUI失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("超链接点击调试工具")
    print("=" * 30)
    
    # 1. 测试URL处理
    test_url_processing()
    
    # 2. 询问是否启动调试GUI
    choice = input("\n是否启动调试GUI? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        create_debug_gui()
    else:
        print("跳过调试GUI")
    
    print(f"\n🎯 可能的问题:")
    print("1. URL中包含了不应该有的字符")
    print("2. 路径解析有问题")
    print("3. Windows explorer命令有问题")
    print("4. 系统默认行为被覆盖")

if __name__ == "__main__":
    main()
