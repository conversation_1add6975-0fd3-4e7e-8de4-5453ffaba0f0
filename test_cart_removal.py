#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试采购车功能移除效果
"""

import sys
import os
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_gui_options():
    """测试GUI选项"""
    print("🔍 测试GUI功能选项...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 检查功能选项
        print("📋 可用功能选项:")
        for i, button in enumerate(window.radio_group.buttons()):
            mode = button.property("mode")
            text = button.text()
            print(f"   {i+1}. {text} (mode: {mode})")
        
        # 检查是否还有采购车相关选项
        cart_found = False
        for button in window.radio_group.buttons():
            if "采购车" in button.text() or button.property("mode") == "cart":
                cart_found = True
                print(f"❌ 发现采购车选项: {button.text()}")
        
        if not cart_found:
            print("✅ 采购车选项已成功移除")
        
        # 检查窗口标题
        title = window.windowTitle()
        print(f"🏷️ 窗口标题: {title}")
        if "采购车" not in title:
            print("✅ 窗口标题已更新")
        else:
            print("❌ 窗口标题仍包含'采购车'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_worker_methods():
    """测试Worker类方法"""
    print("\n🔍 测试Worker类方法...")
    
    try:
        from src.gui.qt_real import WorkerThread
        
        # 检查Worker类是否还有采购车相关方法
        worker_methods = [method for method in dir(WorkerThread) if not method.startswith('_')]
        print("📋 Worker类方法:")
        
        cart_methods = []
        for method in worker_methods:
            print(f"   - {method}")
            if "cart" in method.lower():
                cart_methods.append(method)
        
        if cart_methods:
            print(f"❌ 发现采购车相关方法: {cart_methods}")
        else:
            print("✅ 采购车相关方法已移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_display():
    """测试GUI显示"""
    print("\n🖥️ 启动GUI测试...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.qt_real import RealFunctionApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = RealFunctionApp()
        
        # 添加测试日志
        window.add_log("采购车功能移除测试", "info")
        window.add_log("", "info")
        window.add_log("🎯 检查项目:", "info")
        window.add_log("1. 功能选项中不应有'采购车数据提取'", "info")
        window.add_log("2. 窗口标题应为'1688订单数据处理工具'", "info")
        window.add_log("3. 只应有'订单数据抓取'和'订单数据增强'两个选项", "info")
        
        # 显示窗口
        window.show()
        
        print("✅ GUI已启动")
        print("💡 请检查:")
        print("   1. 功能选项是否只有两个")
        print("   2. 窗口标题是否正确")
        print("   3. 没有采购车相关选项")
        print("   4. 关闭GUI窗口完成测试")
        
        # 运行GUI事件循环
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("采购车功能移除测试")
    print("=" * 30)
    
    print("🎯 移除内容:")
    print("1. 'cart'模式选项")
    print("2. process_cart_real方法")
    print("3. extract_real_cart_data方法")
    print("4. extract_cart_data_async方法")
    print("5. extract_cart_data_with_playwright方法")
    print("6. generate_excel_report方法")
    print("7. 采购车相关的用户确认对话框")
    print("8. 窗口标题中的'采购车'字样")
    
    # 1. 测试GUI选项
    options_ok = test_gui_options()
    
    # 2. 测试Worker方法
    methods_ok = test_worker_methods()
    
    # 3. 询问是否启动GUI测试
    if options_ok and methods_ok:
        choice = input("\n是否启动GUI测试? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            test_gui_display()
        else:
            print("跳过GUI测试")
    
    print(f"\n🎉 移除效果:")
    print("- 采购车数据提取功能已完全移除")
    print("- 程序专注于订单数据处理")
    print("- 界面更加简洁明确")
    print("- 为将来可能的功能扩展保留了架构")

if __name__ == "__main__":
    main()
