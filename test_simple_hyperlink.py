#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的超链接功能测试
"""

import sys
import os
import time
from pathlib import Path

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置项目路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_enhance_function():
    """测试超链接增强函数"""
    print("🔗 测试超链接增强函数...")
    
    # 创建一个真实的测试文件
    reports_dir = PROJECT_ROOT / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    test_file = reports_dir / f"test_file_{timestamp}.xlsx"
    
    # 创建测试文件
    import pandas as pd
    df = pd.DataFrame({'测试': ['数据']})
    df.to_excel(str(test_file), index=False)
    
    print(f"✅ 创建测试文件: {test_file}")
    
    # 导入增强函数
    from src.gui.qt_real import RealFunctionApp
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = RealFunctionApp()
    
    # 测试消息
    test_message = f"Excel报告已生成: {test_file}"
    enhanced = window.enhance_message_with_links(test_message)
    
    print(f"\n📝 测试结果:")
    print(f"原始消息: {test_message}")
    print(f"增强后: {enhanced}")
    
    # 检查是否包含超链接
    if '<a href=' in enhanced:
        print("✅ 超链接增强成功!")
    else:
        print("❌ 超链接增强失败")
    
    # 清理测试文件
    test_file.unlink()
    print(f"🧹 清理测试文件: {test_file}")

def main():
    """主函数"""
    print("简单超链接功能测试")
    print("=" * 30)
    
    try:
        test_enhance_function()
        print("\n🎉 测试完成!")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
