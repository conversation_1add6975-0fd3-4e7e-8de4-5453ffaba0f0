#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1688自动化项目环境
"""

import sys
import os

# 设置控制台编码以支持中文
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

def test_imports():
    """测试所有依赖导入"""
    print("正在测试依赖导入...")
    
    try:
        # 测试GUI框架
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6 - 导入成功")
        
        import tkinter
        print("✅ tkinter - 导入成功")
        
        # 测试数据处理
        import pandas as pd
        print("✅ pandas - 导入成功")
        
        import openpyxl
        print("✅ openpyxl - 导入成功")
        
        # 测试网络和图像
        import requests
        print("✅ requests - 导入成功")
        
        from PIL import Image
        print("✅ PIL/Pillow - 导入成功")
        
        # 测试浏览器自动化
        import playwright
        print("✅ playwright - 导入成功")
        
        # 测试标准库
        import asyncio
        print("✅ asyncio - 导入成功")
        
        import difflib
        print("✅ difflib - 导入成功")
        
        print("\n🎉 所有依赖导入成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_gui():
    """测试GUI功能"""
    print("\n正在测试GUI功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        print("✅ PyQt6应用程序创建成功")
        
        # 测试基本功能
        print(f"✅ PyQt6版本: {app.applicationVersion()}")
        
        # 清理
        app.quit()
        print("✅ GUI测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def test_project_files():
    """测试项目文件"""
    print("\n正在检查项目文件...")
    
    required_files = [
        "main_app.py",
        "export_cart_excel.py", 
        "ui_app/qt_real.py",
        "test_chrome_port.py",
        "start_debug_chrome.bat"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - 存在")
        else:
            print(f"❌ {file_path} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺失文件: {missing_files}")
        return False
    else:
        print("\n✅ 所有必要文件都存在")
        return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("1688自动化项目环境测试")
    print("=" * 60)
    
    # 显示环境信息
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print()
    
    # 运行测试
    tests = [
        ("依赖导入", test_imports),
        ("GUI功能", test_gui),
        ("项目文件", test_project_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n【{test_name}测试】")
        result = test_func()
        results.append((test_name, result))
    
    # 显示结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！环境配置正确！")
        print("\n现在可以运行项目了：")
        print("  python main_app.py")
        print("  python export_cart_excel.py")
        print("  python ui_app/qt_real.py")
    else:
        print("❌ 部分测试失败，请检查环境配置")
    print("=" * 60)

if __name__ == "__main__":
    main()