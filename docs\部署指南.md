# 1688自动化项目部署指南

## 🚀 部署前准备

### 系统要求
- **操作系统**: Windows 10/11, Linux (Ubuntu 18.04+), macOS (10.14+)
- **Python版本**: 3.8+
- **内存**: 最少 4GB RAM，推荐 8GB+
- **磁盘空间**: 最少 2GB 可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- **Chrome浏览器**: 版本 90+
- **Python包**: 见 requirements.txt
- **系统工具**: git, curl (可选)

## 📦 部署方式

### 方式一：本地部署（推荐）

#### 1. 克隆项目
```bash
git clone <repository-url>
cd 1688_automation_project
```

#### 2. 环境配置
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

#### 3. 配置验证
```bash
# 运行项目验证
python validate_project.py

# 运行测试套件
python tests/run_tests.py
```

#### 4. 创建配置文件
```bash
# 创建用户配置
cat > user_config.json << EOF
{
  "chrome_debug_port": 9222,
  "request_timeout": 30,
  "max_retries": 3,
  "log_level": "INFO"
}
EOF
```

#### 5. 创建启动脚本
```bash
# Windows启动脚本 (start.bat)
@echo off
echo 启动1688自动化项目...
echo 1. 启动Chrome调试模式
start_debug_chrome.bat
echo 2. 等待5秒...
timeout /t 5 /nobreak
echo 3. 在Chrome中登录1688并导航到采购车页面
echo 4. 按任意键开始数据提取...
pause
python export_cart_excel.py
echo 数据提取完成！
pause
```

### 方式二：Docker部署

#### 1. 创建Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# 安装Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
RUN echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list
RUN apt-get update && apt-get install -y google-chrome-stable

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip install -r requirements.txt

# 安装Playwright
RUN playwright install chromium

# 创建必要目录
RUN mkdir -p cache/images data reports logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV DISPLAY=:99

# 暴露端口（如果需要）
EXPOSE 9222

# 启动命令
CMD ["python", "export_cart_excel.py"]
```

#### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  1688-automation:
    build: .
    container_name: 1688-automation
    volumes:
      - ./data:/app/data
      - ./reports:/app/reports
      - ./cache:/app/cache
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - CHROME_DEBUG_PORT=9222
    network_mode: host
    restart: unless-stopped
```

#### 3. 启动Docker容器
```bash
# 构建镜像
docker-compose build

# 启动容器
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 方式三：服务器部署

#### 1. 服务器要求
- **CPU**: 2核心+
- **内存**: 4GB+
- **磁盘**: 20GB+
- **网络**: 稳定的互联网连接

#### 2. 安装步骤
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和依赖
sudo apt install -y python3 python3-pip python3-venv

# 安装Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install -y google-chrome-stable

# 克隆项目
git clone <repository-url>
cd 1688_automation_project

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
playwright install chromium

# 创建服务用户
sudo useradd -r -s /bin/false 1688-automation
sudo chown -R 1688-automation:1688-automation /path/to/1688_automation_project
```

#### 3. 创建systemd服务
```bash
# 创建服务文件
sudo tee /etc/systemd/system/1688-automation.service > /dev/null <<EOF
[Unit]
Description=1688 Automation Service
After=network.target

[Service]
Type=simple
User=1688-automation
WorkingDirectory=/path/to/1688_automation_project
Environment=PYTHONPATH=/path/to/1688_automation_project
Environment=LOG_LEVEL=INFO
ExecStart=/path/to/1688_automation_project/venv/bin/python export_cart_excel.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable 1688-automation
sudo systemctl start 1688-automation

# 查看服务状态
sudo systemctl status 1688-automation
```

## 🔧 配置管理

### 环境变量配置
```bash
# 创建环境变量文件
cat > .env << EOF
# Chrome配置
CHROME_DEBUG_PORT=9222
CHROME_USER_DATA_DIR=/tmp/chrome_debug

# 网络配置
REQUEST_TIMEOUT=30
MAX_RETRIES=3
CONCURRENT_LIMIT=10

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/1688-automation.log

# 路径配置
DATA_DIR=/app/data
CACHE_DIR=/app/cache
REPORTS_DIR=/app/reports
LOGS_DIR=/app/logs

# 性能配置
BATCH_SIZE=50
MEMORY_CLEANUP_INTERVAL=100
MAX_MEMORY_USAGE=1024
EOF
```

### 生产环境配置
```json
{
  "chrome_debug_port": 9222,
  "request_timeout": 60,
  "max_retries": 5,
  "concurrent_limit": 5,
  "log_level": "INFO",
  "batch_size": 25,
  "memory_cleanup_interval": 50,
  "max_memory_usage": 512,
  "enable_monitoring": true,
  "auto_cleanup": true,
  "backup_enabled": true
}
```

## 📊 监控和日志

### 日志配置
```python
# logging_config.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/1688-automation/automation.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/1688-automation/error.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 10,
            'formatter': 'detailed',
        },
    },
    'loggers': {
        '': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

### 性能监控
```bash
# 创建监控脚本
cat > monitor.sh << 'EOF'
#!/bin/bash

# 监控脚本
LOG_FILE="/var/log/1688-automation/monitor.log"
ALERT_EMAIL="<EMAIL>"

# 检查服务状态
if ! systemctl is-active --quiet 1688-automation; then
    echo "$(date): Service is not running" >> $LOG_FILE
    # 发送告警邮件
    echo "1688 Automation Service is down" | mail -s "Service Alert" $ALERT_EMAIL
fi

# 检查内存使用
MEMORY_USAGE=$(ps -o rss,command -C python | grep 1688-automation | awk '{print $1/1024}')
if [ $(echo "$MEMORY_USAGE > 1024" | bc -l) -eq 1 ]; then
    echo "$(date): High memory usage: ${MEMORY_USAGE}MB" >> $LOG_FILE
fi

# 检查磁盘空间
DISK_USAGE=$(df /app | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): High disk usage: ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查日志文件大小
LOG_SIZE=$(du -m /var/log/1688-automation/automation.log | cut -f1)
if [ $LOG_SIZE -gt 100 ]; then
    echo "$(date): Large log file: ${LOG_SIZE}MB" >> $LOG_FILE
fi
EOF

chmod +x monitor.sh
```

### 定时任务配置
```bash
# 添加到crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /path/to/monitor.sh") | crontab -

# 每日凌晨清理缓存
0 0 * * * /usr/bin/find /app/cache/images -type f -mtime +7 -delete

# 每周生成报告
0 0 * * 0 /app/venv/bin/python /app/backup_and_report.py
```

## 🔄 备份和恢复

### 备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/1688-automation"
DATE=$(date +%Y%m%d_%H%M%S)
PROJECT_DIR="/app/1688-automation_project"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份项目文件
tar -czf $BACKUP_DIR/project_$DATE.tar.gz -C /app 1688_automation_project

# 备份数据
cp -r $PROJECT_DIR/data $BACKUP_DIR/data_$DATE
cp -r $PROJECT_DIR/reports $BACKUP_DIR/reports_$DATE

# 备份配置文件
cp $PROJECT_DIR/user_config.json $BACKUP_DIR/config_$DATE.json

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "data_*" -mtime +30 -exec rm -rf {} \;
find $BACKUP_DIR -name "reports_*" -mtime +30 -exec rm -rf {} \;

echo "备份完成: $BACKUP_DIR"
```

### 恢复脚本
```bash
#!/bin/bash
# restore.sh

BACKUP_FILE=$1
PROJECT_DIR="/app/1688-automation_project"

if [ -z "$BACKUP_FILE" ]; then
    echo "请指定备份文件路径"
    exit 1
fi

# 停止服务
systemctl stop 1688-automation

# 恢复项目文件
tar -xzf $BACKUP_FILE -C /app

# 启动服务
systemctl start 1688-automation

echo "恢复完成"
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. Chrome连接问题
```bash
# 检查Chrome进程
ps aux | grep chrome

# 检查端口占用
netstat -tulpn | grep 9222

# 重新启动Chrome调试模式
pkill -f "chrome.*remote-debugging"
google-chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome_debug &
```

#### 2. 权限问题
```bash
# 检查文件权限
ls -la /app/1688-automation_project

# 修复权限
chown -R 1688-automation:1688-automation /app/1688-automation_project
chmod +x /app/1688-automation_project/*.py
```

#### 3. 内存问题
```bash
# 检查内存使用
free -h
ps aux | grep python | sort -rk 4

# 重启服务
systemctl restart 1688-automation

# 清理内存
sync && echo 3 > /proc/sys/vm/drop_caches
```

#### 4. 磁盘空间问题
```bash
# 检查磁盘使用
df -h
du -sh /app/1688-automation_project/*

# 清理日志
find /var/log -name "*.log" -size +100M -delete

# 清理缓存
rm -rf /app/cache/images/*
```

### 性能优化

#### 1. 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

#### 2. 应用优化
```json
{
  "batch_size": 25,
  "concurrent_limit": 3,
  "memory_cleanup_interval": 25,
  "max_memory_usage": 512,
  "enable_compression": true,
  "cache_ttl": 3600
}
```

## 📞 技术支持

### 日志文件位置
- **主日志**: `/var/log/1688-automation/automation.log`
- **错误日志**: `/var/log/1688-automation/error.log`
- **性能日志**: `/var/log/1688-automation/performance.log`

### 联系信息
- **技术支持**: <EMAIL>
- **问题报告**: <EMAIL>
- **文档**: https://docs.example.com

### 紧急情况处理
1. 立即停止服务：`systemctl stop 1688-automation`
2. 备份数据：`./backup.sh`
3. 检查日志：`tail -f /var/log/1688-automation/error.log`
4. 联系技术支持

---

**部署完成！** 🎉

请按照上述步骤完成部署，如有问题请参考故障排除部分。