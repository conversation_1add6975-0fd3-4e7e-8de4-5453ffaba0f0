#!/usr/bin/env python3
"""
调试订单项的具体结构
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
PROJECT_ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_ROOT))

from playwright.async_api import async_playwright
import requests

async def debug_order_structure():
    """调试订单项的具体结构"""
    
    print("🔍 调试订单项的具体结构")
    print("=" * 50)
    
    # 检查Chrome调试接口
    debug_port = 9222
    try:
        version_url = f"http://localhost:{debug_port}/json/version"
        proxies = {'http': None, 'https': None}
        response = requests.get(version_url, timeout=5, proxies=proxies)
        if response.status_code != 200:
            print(f"❌ Chrome调试接口不可用")
            return
        
        version_info = response.json()
        ws_url = version_info.get('webSocketDebuggerUrl')
        print(f"✅ Chrome调试接口正常")
        
    except Exception as e:
        print(f"❌ Chrome调试接口连接失败: {e}")
        return
    
    # 连接到Chrome
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")
            
            # 获取所有标签页并查找订单页面
            page = None
            all_pages = []
            for context in browser.contexts:
                for p_page in context.pages:
                    all_pages.append(p_page)
            
            # 查找订单页面
            order_page_patterns = ['trade-order-list', 'buyer-order-list', 'order', 'trade']
            
            for p_page in all_pages:
                url = p_page.url
                if '1688.com' in url and any(pattern in url for pattern in order_page_patterns):
                    page = p_page
                    print(f"✅ 找到订单页面: {url}")
                    break
            
            if not page:
                print("❌ 未找到订单页面")
                return
            
            # 分析订单项结构
            await analyze_order_items(page)
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")

async def analyze_order_items(page):
    """分析订单项的详细结构"""
    try:
        print("\n🔍 分析订单项结构...")
        
        result = await page.evaluate("""
            () => {
                // Shadow DOM 穿透函数
                function querySelectorDeep(selector, root = document) {
                    const elements = [];
                    
                    // 在当前层级查找
                    const found = root.querySelectorAll(selector);
                    elements.push(...found);
                    
                    // 递归查找所有 Shadow DOM
                    const allElements = root.querySelectorAll('*');
                    for (const el of allElements) {
                        if (el.shadowRoot) {
                            const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                            elements.push(...shadowElements);
                        }
                    }
                    
                    return elements;
                }
                
                // 查找订单项
                const orderItems = querySelectorDeep('order-item');
                console.log(`找到 ${orderItems.length} 个订单项`);
                
                if (orderItems.length === 0) {
                    return { error: "未找到订单项" };
                }
                
                // 分析前3个订单项的结构
                const analysis = [];
                
                for (let i = 0; i < Math.min(3, orderItems.length); i++) {
                    const item = orderItems[i];
                    console.log(`分析第 ${i + 1} 个订单项...`);
                    
                    const itemAnalysis = {
                        index: i + 1,
                        tagName: item.tagName,
                        dataTracker: item.getAttribute('data-tracker'),
                        innerHTML: item.innerHTML.substring(0, 500) + '...',
                        textContent: item.textContent.trim().substring(0, 200) + '...',
                        hasShadowRoot: !!item.shadowRoot,
                        shadowRootMode: item.shadowRoot ? item.shadowRoot.mode : null,
                        childElements: [],
                        shadowElements: [],
                        possibleTitles: [],
                        possiblePrices: [],
                        possibleQuantities: []
                    };
                    
                    // 分析子元素
                    const children = item.querySelectorAll('*');
                    for (let j = 0; j < Math.min(10, children.length); j++) {
                        const child = children[j];
                        const text = child.textContent.trim();
                        if (text && text.length > 0) {
                            itemAnalysis.childElements.push({
                                tagName: child.tagName,
                                className: child.className,
                                textContent: text.substring(0, 50) + (text.length > 50 ? '...' : ''),
                                attributes: Array.from(child.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
                            });
                        }
                    }

                    // 如果有Shadow DOM，分析Shadow DOM内容
                    if (item.shadowRoot) {
                        console.log(`订单项 ${i + 1} 有Shadow DOM，分析内容...`);
                        const shadowChildren = item.shadowRoot.querySelectorAll('*');
                        for (let j = 0; j < Math.min(20, shadowChildren.length); j++) {
                            const child = shadowChildren[j];
                            const text = child.textContent.trim();
                            if (text && text.length > 0) {
                                itemAnalysis.shadowElements.push({
                                    tagName: child.tagName,
                                    className: child.className,
                                    textContent: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                                    attributes: Array.from(child.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
                                });
                            }
                        }

                        // 在Shadow DOM中查找标题、价格、数量
                        const shadowRoot = item.shadowRoot;

                        // 查找标题
                        const titleSelectors = [
                            'h1, h2, h3, h4, h5, h6',
                            '.title, .product-title, .goods-title, .product-name, .goods-name',
                            '[class*="title"], [class*="name"], [class*="product"]',
                            'a[href*="offer"], a[href*="detail"]'
                        ];

                        for (const selector of titleSelectors) {
                            const elements = shadowRoot.querySelectorAll(selector);
                            for (const el of elements) {
                                const text = el.textContent.trim();
                                if (text && text.length > 5) {
                                    itemAnalysis.possibleTitles.push({
                                        selector: selector,
                                        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                                        tagName: el.tagName,
                                        className: el.className,
                                        source: 'shadowDOM'
                                    });
                                }
                            }
                        }

                        // 查找价格
                        const priceSelectors = [
                            '.price, .amount, .money, .unit-price',
                            '[class*="price"], [class*="amount"], [class*="money"]',
                            '[class*="cost"], [class*="fee"]'
                        ];

                        for (const selector of priceSelectors) {
                            const elements = shadowRoot.querySelectorAll(selector);
                            for (const el of elements) {
                                const text = el.textContent.trim();
                                if (text && (text.includes('￥') || text.includes('¥') || text.includes('元') || /\\d+\\.?\\d*/.test(text))) {
                                    itemAnalysis.possiblePrices.push({
                                        selector: selector,
                                        text: text,
                                        tagName: el.tagName,
                                        className: el.className,
                                        source: 'shadowDOM'
                                    });
                                }
                            }
                        }

                        // 查找数量
                        const quantitySelectors = [
                            '.quantity, .num, .count, .number',
                            '[class*="quantity"], [class*="num"], [class*="count"]',
                            '[class*="qty"]'
                        ];

                        for (const selector of quantitySelectors) {
                            const elements = shadowRoot.querySelectorAll(selector);
                            for (const el of elements) {
                                const text = el.textContent.trim();
                                if (text && /\\d+/.test(text)) {
                                    itemAnalysis.possibleQuantities.push({
                                        selector: selector,
                                        text: text,
                                        tagName: el.tagName,
                                        className: el.className,
                                        source: 'shadowDOM'
                                    });
                                }
                            }
                        }
                    }

                    
                    analysis.push(itemAnalysis);
                }
                
                return {
                    success: true,
                    totalItems: orderItems.length,
                    analysis: analysis
                };
            }
        """)
        
        if result.get('error'):
            print(f"❌ {result['error']}")
            return
        
        print(f"📊 分析结果:")
        print(f"   总订单项: {result['totalItems']} 个")
        
        for item in result['analysis']:
            print(f"\n   📦 订单项 {item['index']}:")
            print(f"      标签: {item['tagName']}")
            print(f"      追踪ID: {item['dataTracker'][:50] if item['dataTracker'] else 'None'}...")
            print(f"      文本内容: {item['textContent']}")
            print(f"      🌟 Shadow DOM: {'是' if item['hasShadowRoot'] else '否'} ({item['shadowRootMode'] if item['shadowRootMode'] else 'N/A'})")

            if item['hasShadowRoot']:
                print(f"      🌟 Shadow DOM元素 ({len(item['shadowElements'])} 个，显示前10个):")
                for shadow in item['shadowElements'][:10]:
                    print(f"         - {shadow['tagName']}.{shadow['className']}: {shadow['textContent']}")

            print(f"      🏷️ 可能的标题 ({len(item['possibleTitles'])} 个):")
            for title in item['possibleTitles'][:3]:
                source = f" [{title.get('source', 'normal')}]"
                print(f"         - {title['selector']}{source}: {title['text']}")

            print(f"      💰 可能的价格 ({len(item['possiblePrices'])} 个):")
            for price in item['possiblePrices'][:3]:
                source = f" [{price.get('source', 'normal')}]"
                print(f"         - {price['selector']}{source}: {price['text']}")

            print(f"      🔢 可能的数量 ({len(item['possibleQuantities'])} 个):")
            for qty in item['possibleQuantities'][:3]:
                source = f" [{qty.get('source', 'normal')}]"
                print(f"         - {qty['selector']}{source}: {qty['text']}")

            print(f"      🧩 普通子元素 ({len(item['childElements'])} 个，显示前5个):")
            for child in item['childElements'][:5]:
                print(f"         - {child['tagName']}.{child['className']}: {child['textContent']}")
        
    except Exception as e:
        print(f"❌ 分析订单项结构失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_order_structure())
