# 1688采购车数据导出与增强工具

## 项目要求
- **语言**: 所有会话都用中文交谈
- **测试原则**: 不要过度测试和验证。所有的测试都尽量使用现有资源，或略加改进，仅当没有现有资源可用时才全新创建
- **虚拟环境**: 必须使用miniconda的`1688_automation`虚拟环境。在所有开发工作中，始终先激活该环境：

```bash
conda activate 1688_automation
```

## 项目概述

一个基于Playwright的1688采购车数据自动化抓取和增强工具，支持多种连接方式，可提取商品信息、供应商数据，并生成带图片的Excel报告。

### 项目状态
- ✅ **开发完成**: 所有核心功能已实现并通过验证
- ✅ **双重界面**: 支持GUI和CLI两种操作模式
- ✅ **测试覆盖**: 完整的单元测试和集成测试
- ✅ **生产就绪**: 可直接投入使用

## 核心功能

### 1. 数据采集功能
- **采购车数据提取**: `src/core/export_cart_excel.py`
- **订单数据抓取**: `src/core/extract_orders.py`
- **订单数据增强**: `src/core/enrich_orders_with_images.py`
- **数据增强工具**: `src/core/enhance_cart_data.py`

### 2. 用户界面
- **GUI模式**: PyQt6图形界面，支持功能选择和进度显示
- **CLI模式**: 命令行界面，支持批量处理和自动化
- **主程序**: `main_app.py` - 自动检测环境并选择合适的界面模式

### 3. 浏览器自动化
- **Chrome调试模式**: `start_debug_chrome.bat`
- **远程调试协议**: 端口9222，支持会话保持
- **反检测措施**: 真实浏览行为模拟
- **Shadow DOM处理**: 现代网页组件支持

### 4. 数据处理
- **三层匹配策略**: offerId → 详情链接 → 模糊标题匹配
- **图片处理**: 下载、缓存、Excel嵌入
- **数据标准化**: 多种数据格式支持
- **错误处理**: 优雅降级和重试机制

## 项目架构

### 目录结构
```
1688_automation_project/
├── main_app.py                    # 主程序入口（GUI/CLI自动检测）
├── config.py                      # 统一配置管理
├── requirements.txt               # Python依赖文件
├── start_debug_chrome.bat         # Chrome调试模式启动脚本
├── start_env.bat/sh              # 环境启动脚本
├── app_launcher.py               # 应用启动器
├── 📁 src/                       # 源代码目录
│   ├── cli/                      # 命令行界面模块
│   │   └── cli_app.py           # CLI应用程序
│   ├── core/                     # 核心功能模块
│   │   ├── export_cart_excel.py  # 采购车数据导出
│   │   ├── extract_orders.py     # 订单数据抓取
│   │   ├── enrich_orders_with_images.py  # 订单数据增强
│   │   └── enhance_cart_data.py   # 数据增强工具
│   ├── gui/                      # 图形用户界面模块
│   │   ├── main.py              # GUI主程序
│   │   ├── qt_*.py              # 多种PyQt6实现版本
│   │   ├── core/                # GUI核心组件
│   │   │   ├── chrome_manager.py    # Chrome管理器
│   │   │   ├── data_processor.py    # 数据处理器
│   │   │   ├── error_handler.py     # 错误处理器
│   │   │   ├── cart_steps.py        # 采购车处理步骤
│   │   │   └── order_steps.py       # 订单处理步骤
│   │   ├── ui/                  # UI组件
│   │   │   ├── main_window.py      # 主窗口
│   │   │   ├── function_selection.py  # 功能选择窗口
│   │   │   └── step_manager.py      # 步骤管理器
│   │   └── utils/               # GUI工具模块
│   │       ├── file_utils.py      # 文件操作工具
│   │       ├── helpers.py         # 通用助手函数
│   │       └── logger.py          # 日志系统
│   └── utils/                    # 工具模块
│       ├── exceptions.py          # 异常定义
│       ├── helpers.py             # 通用工具函数
│       ├── logger.py              # 日志系统
│       └── performance.py         # 性能监控
├── 📁 tests/                      # 测试套件
│   ├── core/                      # 核心功能测试
│   ├── gui/                       # GUI测试
│   └── run_tests.py               # 测试运行器
├── 📁 data/                       # 数据目录
│   ├── cart_data_*.json           # 采购车数据样本
│   └── enhanced_*.json            # 增强数据样本
├── 📁 cache/                      # 缓存目录
│   └── images/                    # 图片缓存
├── 📁 reports/                    # Excel报告输出目录
├── 📁 logs/                       # 日志文件目录
├── 📁 docs/                       # 技术文档
│   ├── 使用指南.md                # 详细使用指南
│   ├── 1688订单页爬取方法.md       # 抓取技术文档
│   └── 采购车数据提取优化说明.md   # 优化说明
├── 📁 scripts/                    # 脚本工具
│   ├── fix_encoding.py           # 编码修复工具
│   ├── test_environment.py       # 环境测试
│   └── validate_*.py             # 各种验证工具
└── CLAUDE.md                     # Claude相关文档（本文件）
```

## 开发和使用指南

### 1. 环境设置
```bash
# 激活虚拟环境
conda activate 1688_automation

# 安装依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

### 2. 运行方式

#### GUI模式（推荐）
```bash
# 启动主程序（自动检测GUI支持）
python main_app.py

# 或直接启动GUI
python src/gui/main.py
```

#### CLI模式
```bash
# 启动Chrome调试模式
start_debug_chrome.bat

# 采购车数据提取
python src/core/export_cart_excel.py

# 订单数据增强
python src/core/enrich_orders_with_images.py --input path/to/orders.xlsx
```

### 3. 关键技术特性

#### 三层匹配策略
1. **主要匹配**: 通过offerId提取URL匹配
2. **次要匹配**: 直接URL比较
3. **回退匹配**: 90%相似度的模糊标题匹配

#### Chrome调试集成
- 使用远程调试协议（端口9222）
- 保持用户会话状态
- 自动重连和回退策略
- 支持多种连接方式

#### 图片处理管道
- 下载和缓存避免重复请求
- 图片转换和调整大小以适应Excel
- 处理多种图片格式和错误场景

#### 反检测措施
- 使用`:light()`选择器处理Shadow DOM
- 水印和覆盖层处理
- 真实浏览行为模拟
- 网络响应监控作为DOM解析的备用方案

### 4. 数据结构

#### 采购车数据格式
```json
{
  "提取时间": "2025-08-09T13:47:11.279950",
  "页面URL": "https://cart.1688.com/cart.htm",
  "商品总数": 67,
  "商品数据": [
    {
      "序号": 1,
      "品名": "商品名称",
      "规格": "规格信息",
      "数量": 1,
      "单价": 0,
      "小计": 0,
      "生产商名称": "供应商名称",
      "图片URL": "https://...",
      "商品链接": "https://detail.1688.com/offer/..."
    }
  ]
}
```

#### 增强的Excel列
- 原始Excel列
- `图片原图` - 高清图片URL
- `详情链接` - 商品详情页URL
- `商品图片` - 嵌入的缩略图（第一列）

### 5. 开发模式

#### 添加新数据源
1. 扩展`ORDERS_JS`中的JavaScript提取函数
2. 更新`enrich_orders_df()`中的列映射
3. 如需要，添加新的匹配策略

#### 修改图片处理
1. 更新`download_image()`以支持新图片格式
2. 修改`embed_images_to_excel()`中的缩略图生成
3. 调整`cache_dir`管理中的缓存策略

#### 增强反检测
1. 添加新选择器以处理页面结构变化
2. 更新动态内容的等待策略
3. 为新的边缘情况增强错误处理

## 重要提示

- 在执行自动化脚本之前，务必运行`start_debug_chrome.bat`
- 确保在调试Chrome会话中完成1688登录
- 项目维护广泛缓存以避免重复请求
- 图片处理包含对下载失败的全面错误处理
- 所有脚本都支持交互式和自动化工作流

## 依赖项

- **playwright** - 浏览器自动化
- **pandas** - Excel数据处理
- **openpyxl** - Excel文件操作和图片嵌入
- **requests** - HTTP请求
- **PIL/Pillow** - 图片处理和调整大小
- **asyncio** - 异步编程支持
- **difflib** - 产品标题的模糊字符串匹配
- **PyQt6** - 图形用户界面（可选）

## 测试和调试

- 使用简化版本进行调试
- 检查下载图片的`cache/images/`目录
- 监控Chrome DevTools的网络活动和页面结构
- 查看控制台输出以获取详细进度信息