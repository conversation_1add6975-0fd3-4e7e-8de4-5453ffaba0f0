# Tkinter 问题解决方案

## 问题分析
您的嵌入式Python环境中缺少tkinter模块，这会导致项目中的GUI应用无法运行。

## 解决方案

### 方案1：使用命令行版本 (推荐)
我已经为您创建了 `cli_app.py`，这是一个完全无GUI的命令行版本，提供所有相同的功能：

```bash
# 运行命令行版本
python cli_app.py
```

### 方案2：安装tkinter

#### Windows系统:
```bash
# 如果您有管理员权限，可以尝试安装tkinter
pip install tk
```

或者：
```bash
# 使用Python的包管理器
python -m pip install tk
```

#### Linux系统:
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL/Fedora
sudo yum install tkinter
# 或
sudo dnf install python3-tkinter
```

#### macOS:
```bash
# 如果使用Homebrew
brew install python-tk

# 或使用macPorts
sudo port install py39-tkinter
```

### 方案3：修改现有代码支持无GUI模式
Write
<arg_key>content</arg_key>
<arg_value>#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI应用启动器 - 自动检测tkinter可用性
"""

import sys
import os

def check_tkinter_availability():
    """检查tkinter是否可用"""
    try:
        import tkinter
        # 尝试创建一个简单的窗口来验证
        root = tkinter.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        return True
    except ImportError:
        return False
    except Exception as e:
        print(f"⚠️ tkinter导入失败: {e}")
        return False

def launch_ui_app():
    """启动UI应用"""
    try:
        # 导入UI应用
        from ui_app.main import main as ui_main
        ui_main()
    except Exception as e:
        print(f"❌ UI应用启动失败: {e}")
        return False

def launch_cli_app():
    """启动命令行应用"""
    try:
        # 导入命令行应用
        from cli_app import main as cli_main
        cli_main()
    except Exception as e:
        print(f"❌ 命令行应用启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 1688采购车数据处理工具")
    print("=" * 50)
    
    # 检查tkinter可用性
    if check_tkinter_availability():
        print("✅ 检测到tkinter，启动GUI版本...")
        if not launch_ui_app():
            print("❌ GUI版本启动失败，回退到命令行版本...")
            launch_cli_app()
    else:
        print("❌ 未检测到tkinter，启动命令行版本...")
        print("💡 如果您希望使用GUI版本，请安装tkinter")
        print("   Windows: pip install tk")
        print("   Linux: sudo apt-get install python3-tk")
        print()
        launch_cli_app()

if __name__ == "__main__":
    main()