"""
订单模式步骤实现
"""

# 延迟导入tkinter，避免在没有GUI的环境中出错
def _get_tkinter():
    try:
        import tkinter as tk
        from tkinter import filedialog, messagebox
        return tk, filedialog, messagebox
    except ImportError:
        return None, None, None
from typing import Callable, Optional, Any
import threading
import time
import json
import os
import pandas as pd
from pathlib import Path

from .chrome_manager import ChromeManager
from ..ui.step_manager import BaseStep
from ..utils.file_utils import FileUtils
from ..utils.helpers import format_duration, format_file_size
from ..core.config import Config

class SelectOrderFileStep(BaseStep):
    """选择订单文件步骤"""
    
    def __init__(self):
        super().__init__("选择Excel订单文件", "选择要增强的订单文件")
        self.selected_file = None
        self.order_data = None
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """选择文件"""
        def worker():
            try:
                progress_callback(0, "等待选择文件...")
                
                # 选择文件
                file_path = FileUtils.select_excel_file("选择Excel订单文件")
                
                if file_path:
                    progress_callback(50, f"已选择文件：{os.path.basename(file_path)}")
                    
                    # 验证文件
                    is_valid, message = FileUtils.validate_excel_file(file_path)
                    
                    if is_valid:
                        # 读取文件
                        progress_callback(80, "正在读取文件内容...")
                        df = pd.read_excel(file_path)
                        
                        self.selected_file = file_path
                        self.order_data = df
                        
                        progress_callback(100, f"文件读取成功，共{len(df)}条记录", {
                            'file_path': file_path,
                            'record_count': len(df),
                            'columns': list(df.columns)
                        })
                        self.is_completed = True
                    else:
                        self.error_message = message
                        progress_callback(-1, self.error_message, None)
                else:
                    self.error_message = "未选择文件"
                    progress_callback(-1, self.error_message, None)
                
            except Exception as e:
                self.error_message = f"选择文件失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

class AnalyzeOrderDataStep(BaseStep):
    """分析订单数据步骤"""
    
    def __init__(self):
        super().__init__("分析订单数据", "分析订单数据结构")
        self.analysis_result = None
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """分析数据"""
        def worker():
            try:
                progress_callback(0, "正在分析订单数据...")
                
                # 获取上一步的数据
                order_data = self.get_order_data()
                
                if order_data is None:
                    self.error_message = "未找到订单数据，请先选择文件"
                    progress_callback(-1, self.error_message, None)
                    return
                
                total_records = len(order_data)
                progress_callback(20, f"开始分析{total_records}条订单记录")
                
                # 分析数据结构
                analysis = self.analyze_data_structure(order_data)
                
                progress_callback(60, "正在识别商品信息字段...")
                
                # 识别商品名称字段
                product_name_field = self.identify_product_name_field(order_data)
                analysis['product_name_field'] = product_name_field
                
                # 识别价格字段
                price_field = self.identify_price_field(order_data)
                analysis['price_field'] = price_field
                
                # 识别数量字段
                quantity_field = self.identify_quantity_field(order_data)
                analysis['quantity_field'] = quantity_field
                
                progress_callback(80, "正在生成分析报告...")
                
                # 生成分析报告
                report = self.generate_analysis_report(analysis)
                
                self.analysis_result = analysis
                
                progress_callback(100, f"数据分析完成，识别出{len(analysis['potential_products'])}个商品", analysis)
                self.is_completed = True
                
            except Exception as e:
                self.error_message = f"数据分析失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def get_order_data(self) -> Optional[pd.DataFrame]:
        """获取订单数据"""
        try:
            # 从上一步获取数据
            # 这里应该从步骤管理器获取，现在模拟获取
            return None
            
        except Exception as e:
            print(f"获取订单数据失败：{e}")
            return None
    
    def analyze_data_structure(self, df: pd.DataFrame) -> dict:
        """分析数据结构"""
        analysis = {
            'total_records': len(df),
            'total_columns': len(df.columns),
            'columns': list(df.columns),
            'data_types': df.dtypes.to_dict(),
            'null_counts': df.isnull().sum().to_dict(),
            'potential_products': [],
            'sample_data': df.head(3).to_dict('records')
        }
        
        # 分析每列数据
        for col in df.columns:
            col_data = df[col]
            sample_values = col_data.dropna().head(5).tolist()
            
            analysis['columns_info'] = analysis.get('columns_info', {})
            analysis['columns_info'][col] = {
                'dtype': str(col_data.dtype),
                'null_count': col_data.isnull().sum(),
                'unique_count': col_data.nunique(),
                'sample_values': sample_values,
                'is_potential_product_name': self.is_potential_product_name_col(col_data),
                'is_potential_price': self.is_potential_price_col(col_data),
                'is_potential_quantity': self.is_potential_quantity_col(col_data)
            }
        
        return analysis
    
    def is_potential_product_name_col(self, series: pd.Series) -> bool:
        """判断是否为商品名称列"""
        if series.dtype == 'object':
            non_null_values = series.dropna()
            if len(non_null_values) > 0:
                avg_length = non_null_values.astype(str).str.len().mean()
                # 商品名称通常在5-100个字符之间
                return 5 <= avg_length <= 100
        return False
    
    def is_potential_price_col(self, series: pd.Series) -> bool:
        """判断是否为价格列"""
        try:
            non_null_values = series.dropna()
            if len(non_null_values) > 0:
                # 尝试转换为数值
                numeric_values = pd.to_numeric(non_null_values.astype(str).str.replace('[¥,元,$]', '', regex=True), errors='coerce')
                valid_prices = numeric_values.dropna()
                
                # 价格通常是正数，且范围合理
                if len(valid_prices) > 0:
                    return (valid_prices > 0).all() and (valid_prices < 100000).all()
        except:
            pass
        return False
    
    def is_potential_quantity_col(self, series: pd.Series) -> bool:
        """判断是否为数量列"""
        try:
            non_null_values = series.dropna()
            if len(non_null_values) > 0:
                # 尝试转换为整数
                numeric_values = pd.to_numeric(non_null_values, errors='coerce')
                valid_quantities = numeric_values.dropna()
                
                # 数量通常是正整数
                if len(valid_quantities) > 0:
                    return (valid_quantities > 0).all() and (valid_quantities < 10000).all()
        except:
            pass
        return False
    
    def identify_product_name_field(self, df: pd.DataFrame) -> Optional[str]:
        """识别商品名称字段"""
        # 常见的商品名称字段名
        name_keywords = ['商品', '产品', '名称', '品名', '标题', 'title', 'name', 'product']
        
        for col in df.columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in name_keywords):
                return col
        
        # 如果没有找到关键字段，尝试根据内容判断
        for col in df.columns:
            if self.is_potential_product_name_col(df[col]):
                return col
        
        return None
    
    def identify_price_field(self, df: pd.DataFrame) -> Optional[str]:
        """识别价格字段"""
        # 常见的价格字段名
        price_keywords = ['价格', '金额', '单价', '费用', 'price', 'amount', 'cost']
        
        for col in df.columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in price_keywords):
                return col
        
        # 如果没有找到关键字段，尝试根据内容判断
        for col in df.columns:
            if self.is_potential_price_col(df[col]):
                return col
        
        return None
    
    def identify_quantity_field(self, df: pd.DataFrame) -> Optional[str]:
        """识别数量字段"""
        # 常见的数量字段名
        quantity_keywords = ['数量', '个数', '件数', 'quantity', 'qty', 'count', 'num']
        
        for col in df.columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in quantity_keywords):
                return col
        
        # 如果没有找到关键字段，尝试根据内容判断
        for col in df.columns:
            if self.is_potential_quantity_col(df[col]):
                return col
        
        return None
    
    def generate_analysis_report(self, analysis: dict) -> str:
        """生成分析报告"""
        report = f"""
数据分析报告
=====================================

基本信息：
- 总记录数：{analysis['total_records']}
- 总字段数：{analysis['total_columns']}
- 字段列表：{', '.join(analysis['columns'])}

识别的字段：
- 商品名称字段：{analysis.get('product_name_field', '未识别')}
- 价格字段：{analysis.get('price_field', '未识别')}
- 数量字段：{analysis.get('quantity_field', '未识别')}

数据质量：
- 完整记录数：{analysis['total_records'] - sum(analysis['null_counts'].values())}
- 缺失数据：{sum(analysis['null_counts'].values())} 个

建议：
"""
        
        if not analysis.get('product_name_field'):
            report += "- 请确保Excel文件包含商品名称信息\n"
        
        if not analysis.get('price_field'):
            report += "- 请确保Excel文件包含价格信息\n"
        
        if not analysis.get('quantity_field'):
            report += "- 请确保Excel文件包含数量信息\n"
        
        return report

class EnhanceOrderDataStep(BaseStep):
    """增强订单数据步骤"""
    
    def __init__(self):
        super().__init__("增强数据", "添加商品图片和详情链接")
        self.enhanced_data = None
        self.chrome_manager = ChromeManager()
        self.config = Config()
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """增强数据"""
        def worker():
            try:
                progress_callback(0, "正在准备增强数据...")
                
                # 获取订单数据和分析结果
                order_data = self.get_order_data()
                analysis_result = self.get_analysis_result()
                
                if order_data is None or analysis_result is None:
                    self.error_message = "未找到订单数据或分析结果，请先完成前面的步骤"
                    progress_callback(-1, self.error_message, None)
                    return
                
                total_records = len(order_data)
                enhanced_records = []
                
                progress_callback(10, f"开始增强{total_records}条订单记录")
                
                # 获取商品名称字段
                product_name_field = analysis_result.get('product_name_field')
                if not product_name_field:
                    self.error_message = "未识别到商品名称字段，无法进行数据增强"
                    progress_callback(-1, self.error_message, None)
                    return
                
                # 启动Chrome（如果需要）
                if self.config.get('chrome.use_for_enhancement', True):
                    progress_callback(20, "正在启动Chrome浏览器...")
                    if not self.chrome_manager.test_connection():
                        # 尝试启动Chrome
                        self.chrome_manager.start_chrome()
                        time.sleep(3)
                
                # 逐条增强记录
                for i, (_, record) in enumerate(order_data.iterrows()):
                    try:
                        enhanced_record = self.enhance_single_record(record, product_name_field)
                        enhanced_records.append(enhanced_record)
                        
                        # 更新进度
                        progress = 20 + (i + 1) / total_records * 70
                        progress_callback(progress, f"正在增强第{i+1}/{total_records}条记录...")
                        
                        # 添加延迟避免过快请求
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"增强记录{i+1}失败：{e}")
                        # 添加失败的记录
                        enhanced_record = record.copy()
                        enhanced_record['enhancement_error'] = str(e)
                        enhanced_record['enhancement_status'] = 'failed'
                        enhanced_records.append(enhanced_record)
                
                progress_callback(90, "正在整理增强结果...")
                
                # 保存增强结果
                self.enhanced_data = enhanced_records
                self.save_enhanced_data(enhanced_records)
                
                success_count = len([r for r in enhanced_records if r.get('enhancement_status') == 'success'])
                progress_callback(100, f"数据增强完成，成功增强{success_count}/{total_records}条记录", enhanced_records)
                self.is_completed = True
                
            except Exception as e:
                self.error_message = f"数据增强失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def get_order_data(self) -> Optional[pd.DataFrame]:
        """获取订单数据"""
        try:
            # 从上一步获取数据
            return None
            
        except Exception as e:
            print(f"获取订单数据失败：{e}")
            return None
    
    def get_analysis_result(self) -> Optional[dict]:
        """获取分析结果"""
        try:
            # 从上一步获取分析结果
            return None
            
        except Exception as e:
            print(f"获取分析结果失败：{e}")
            return None
    
    def enhance_single_record(self, record: pd.Series, product_name_field: str) -> dict:
        """增强单条记录"""
        enhanced = record.to_dict()
        
        # 获取商品名称
        product_name = str(record.get(product_name_field, '')).strip()
        
        if not product_name:
            enhanced['enhancement_status'] = 'failed'
            enhanced['enhancement_error'] = '商品名称为空'
            return enhanced
        
        # 搜索商品信息
        product_info = self.search_product_info(product_name)
        
        if product_info:
            enhanced.update({
                'product_image_url': product_info.get('image_url', ''),
                'product_detail_url': product_info.get('detail_url', ''),
                'product_price': product_info.get('price', ''),
                'product_supplier': product_info.get('supplier', ''),
                'enhancement_status': 'success',
                'enhanced_at': time.strftime("%Y-%m-%dT%H:%M:%S")
            })
            
            # 下载商品图片
            if product_info.get('image_url'):
                image_path = self.download_product_image(
                    product_info['image_url'], 
                    len(enhanced_records) if 'enhanced_records' in locals() else 0
                )
                enhanced['product_image_path'] = image_path or ''
        else:
            enhanced['enhancement_status'] = 'failed'
            enhanced['enhancement_error'] = '未找到匹配的商品信息'
        
        return enhanced
    
    def search_product_info(self, product_name: str) -> Optional[dict]:
        """搜索商品信息"""
        try:
            # 构建搜索URL
            search_url = f"https://s.1688.com/selloffer/offer_search.htm?keywords={product_name}&beginPage=1"
            
            # 使用Chrome获取搜索结果
            if self.chrome_manager.test_connection():
                return self.search_with_chrome(product_name, search_url)
            else:
                return self.search_without_chrome(product_name)
                
        except Exception as e:
            print(f"搜索商品信息失败：{e}")
            return None
    
    def search_with_chrome(self, product_name: str, search_url: str) -> Optional[dict]:
        """使用Chrome搜索商品"""
        try:
            # 创建新标签页
            tab_id = self.chrome_manager.create_new_tab(search_url)
            time.sleep(3)
            
            # 提取搜索结果
            js_code = '''
            (function() {
                try {
                    var products = [];
                    var productElements = document.querySelectorAll('.sm-offer-item, .offer-item, [class*="offer"]');
                    
                    productElements.forEach(function(item, index) {
                        if (index >= 3) return; // 只取前3个结果
                        
                        var nameElement = item.querySelector('.sm-offer-title, .title, .offer-title') || item.querySelector('a');
                        var name = nameElement ? nameElement.textContent.trim() : '';
                        
                        var imgElement = item.querySelector('img');
                        var imageUrl = imgElement ? imgElement.src : '';
                        
                        var linkElement = item.querySelector('a');
                        var detailUrl = linkElement ? linkElement.href : '';
                        
                        var priceElement = item.querySelector('.price, .value, .money');
                        var price = priceElement ? priceElement.textContent.trim() : '';
                        
                        if (name && detailUrl) {
                            products.push({
                                name: name,
                                image_url: imageUrl,
                                detail_url: detailUrl,
                                price: price
                            });
                        }
                    });
                    
                    return products;
                } catch (e) {
                    return { error: e.toString() };
                }
            })();
            '''
            
            result = self.chrome_manager.execute_javascript(tab_id, js_code)
            
            if result and isinstance(result, list) and len(result) > 0:
                # 返回第一个匹配度最高的结果
                return result[0]
            
            return None
            
        except Exception as e:
            print(f"Chrome搜索失败：{e}")
            return None
    
    def search_without_chrome(self, product_name: str) -> Optional[dict]:
        """不使用Chrome搜索商品"""
        try:
            import requests
            from urllib.parse import quote
            
            # 构建搜索URL
            search_url = f"https://s.1688.com/selloffer/offer_search.htm?keywords={quote(product_name)}"
            
            # 发送请求
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(search_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            # 简单解析搜索结果（这里需要根据实际HTML结构调整）
            # 返回模拟数据
            return {
                'name': product_name,
                'image_url': '',
                'detail_url': search_url,
                'price': '价格面议'
            }
            
        except Exception as e:
            print(f"无Chrome搜索失败：{e}")
            return None
    
    def download_product_image(self, image_url: str, index: int) -> Optional[str]:
        """下载商品图片"""
        try:
            import requests
            from pathlib import Path
            
            if not image_url:
                return None
            
            # 创建缓存目录
            cache_dir = Path(__file__).parent.parent / "cache" / "images"
            cache_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            file_extension = Path(image_url).suffix.lower()
            if not file_extension:
                file_extension = '.jpg'
            
            filename = f"order_product_{index:04d}{file_extension}"
            file_path = cache_dir / filename
            
            # 下载图片
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"商品图片下载成功：{filename}")
            return str(file_path)
            
        except Exception as e:
            print(f"下载商品图片失败：{e}")
            return None
    
    def save_enhanced_data(self, enhanced_data: list):
        """保存增强后的数据"""
        try:
            # 创建数据目录
            data_dir = Path(__file__).parent.parent / "data"
            data_dir.mkdir(exist_ok=True)
            
            # 保存JSON文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            json_file = data_dir / f"enhanced_order_data_{timestamp}.json"
            
            save_data = {
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
                "source": "1688_order_enhanced",
                "total_records": len(enhanced_data),
                "records": enhanced_data
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            print(f"增强数据已保存到：{json_file}")
            
        except Exception as e:
            print(f"保存增强数据失败：{e}")

class SaveOrderResultStep(BaseStep):
    """保存订单结果步骤"""
    
    def __init__(self):
        super().__init__("保存增强结果", "保存增强后的Excel文件")
        self.saved_file_path = None
        self.config = Config()
    
    def execute(self, progress_callback: Callable[[float, str, Any], None]):
        """保存结果"""
        def worker():
            try:
                progress_callback(0, "正在准备保存文件...")
                
                # 获取增强后的数据
                enhanced_data = self.get_enhanced_data()
                
                if not enhanced_data:
                    self.error_message = "未找到增强后的数据，请先增强数据"
                    progress_callback(-1, self.error_message, None)
                    return
                
                progress_callback(20, "正在生成Excel文件...")
                
                # 生成Excel文件
                excel_file = self.generate_excel_file(enhanced_data)
                
                if excel_file:
                    progress_callback(80, "正在保存文件...")
                    
                    # 保存文件
                    self.saved_file_path = excel_file
                    
                    progress_callback(100, f"文件已保存：{excel_file}", excel_file)
                    self.is_completed = True
                else:
                    self.error_message = "生成Excel文件失败"
                    progress_callback(-1, self.error_message, None)
                
            except Exception as e:
                self.error_message = f"保存文件失败：{str(e)}"
                progress_callback(-1, self.error_message, None)
        
        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()
    
    def get_enhanced_data(self) -> Optional[list]:
        """获取增强后的数据"""
        try:
            # 从数据文件中读取
            data_dir = Path(__file__).parent.parent / "data"
            json_files = list(data_dir.glob("enhanced_order_data_*.json"))
            
            if json_files:
                # 读取最新的文件
                latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('records', [])
            
            return None
            
        except Exception as e:
            print(f"获取增强数据失败：{e}")
            return None
    
    def generate_excel_file(self, enhanced_data: list) -> Optional[str]:
        """生成Excel文件"""
        try:
            import pandas as pd
            from datetime import datetime
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"增强订单数据_{timestamp}.xlsx"
            
            # 选择保存路径
            file_path = FileUtils.select_save_file(
                title="保存增强订单数据",
                default_name=default_filename
            )
            
            if not file_path:
                return None
            
            # 准备数据
            df_data = []
            for item in enhanced_data:
                df_item = {
                    "序号": item.get('index', ''),
                    "商品名称": item.get('product_name_field', '') or item.get('商品名称', ''),
                    "原价格": item.get('price', '') or item.get('原价', ''),
                    "数量": item.get('quantity', '') or item.get('数量', ''),
                    "商品图片URL": item.get('product_image_url', ''),
                    "商品详情链接": item.get('product_detail_url', ''),
                    "增强后价格": item.get('product_price', ''),
                    "供应商": item.get('product_supplier', ''),
                    "增强状态": item.get('enhancement_status', ''),
                    "增强时间": item.get('enhanced_at', '')
                }
                
                if item.get('enhancement_error'):
                    df_item["错误信息"] = item['enhancement_error']
                
                df_data.append(df_item)
            
            # 创建DataFrame
            df = pd.DataFrame(df_data)
            
            # 保存Excel文件
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            print(f"Excel文件已保存：{file_path}")
            return file_path
            
        except Exception as e:
            print(f"生成Excel文件失败：{e}")
            return None