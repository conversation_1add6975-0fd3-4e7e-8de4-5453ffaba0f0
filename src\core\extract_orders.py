#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688订单数据抓取工具
支持从1688订单页面抓取订单数据，包括接口法和DOM法
"""

import asyncio
import time
import pandas as pd
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright
import re
import sys
import requests
from pathlib import Path
import os
import io
from PIL import Image as PILImage

# Windows下设置UTF-8输出
if sys.platform.startswith('win'):
    import codecs
    try:
        if hasattr(sys.stdout, 'detach'):
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        else:
            # 如果detach方法不可用，使用更安全的方法
            import os
            os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        # 如果所有方法都失败，忽略错误
        pass

# 定义项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class OrderDataExtractor:
    """订单数据提取器"""

    def __init__(self):
        self.order_data = []
        self.stats = {
            'total_orders': 0,
            'api_success': 0,
            'dom_success': 0,
            'failed': 0
        }

    async def extract_orders_from_api(self, page) -> List[Dict[str, Any]]:
        """通过API接口抓取订单数据"""
        orders_data = []

        def handle_response(response):
            """处理网络响应"""
            url = response.url
            # 检查是否是订单相关的API请求
            if (any(keyword in url for keyword in ['/order', '/trade', '/buyer', '/list']) and
                    response.request.resource_type == "xhr"):
                try:
                    data = response.json()
                    print(f"📡 捕获到订单API: {url}")

                    # 解析不同结构的订单数据
                    orders = self._parse_api_response(data)
                    if orders:
                        orders_data.extend(orders)
                        self.stats['api_success'] += len(orders)

                except (ValueError, TypeError, KeyError) as e:
                    print(f"⚠️ 解析API响应失败: {e}")

        # 监听响应
        page.on("response", handle_response)

        # 触发页面操作以产生API请求
        await self.trigger_api_requests(page)

        # 等待一段时间让API请求完成
        await page.wait_for_timeout(3000)

        return orders_data

    async def trigger_api_requests(self, page):
        """触发页面操作以产生API请求 - 简化版，避免误点"""
        try:
            print("🔄 等待页面数据加载...")

            # 只进行安全的操作：滚动页面触发懒加载
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(1000)
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(1000)

            print("✅ 页面数据加载完成")

        except Exception as e:
            print(f"⚠️ 页面操作时出错: {e}")

    async def debug_page_state(self, page):
        """调试页面状态"""
        try:
            # 检查页面基本信息
            title = await page.title()
            print(f"📍 当前页面: {title}")

            # 检查页面内容
            all_text = await page.evaluate("document.body.textContent")
            if any(keyword in all_text for keyword in ["暂无", "没有", "空", "无数据"]):
                print("⚠️ 页面可能显示无数据状态")

            # 检查订单容器
            try:
                container = await page.query_selector('.order-list-container')
                if container:
                    print("✅ 找到订单容器")
                else:
                    print("⚠️ 未找到订单容器")
            except Exception:
                pass

            # 简单检查订单元素数量
            try:
                order_items = await page.query_selector_all('order-item')
                if order_items:
                    print(f"📋 发现 {len(order_items)} 个订单项")
            except Exception:
                pass

        except Exception as e:
            print(f"⚠️ 调试页面状态失败: {e}")

    async def debug_pagination_container(self, page):
        """调试翻页容器 - 基于实际HTML结构"""
        try:
            print("🔍 调试翻页容器...")

            # 检查翻页容器（基于截图中的实际结构）
            page_container = await page.query_selector('div.page#page')
            if page_container:
                print("✅ 找到翻页容器: div.page#page")

                # 检查分页信息
                pagination_info = await page.evaluate("""
                    () => {
                        const container = document.querySelector('div.page#page');
                        if (!container) return null;

                        // 查找lu-pagination元素获取分页信息
                        const luPagination = document.querySelector('lu-pagination');
                        let paginationData = {};
                        if (luPagination) {
                            paginationData = {
                                total: luPagination.getAttribute('total') || '',
                                per: luPagination.getAttribute('per') || '',
                                current: luPagination.getAttribute('current') || ''
                            };
                        }

                        // 查找所有页码按钮
                        const pageButtons = container.querySelectorAll('button.ui-page');
                        const buttons = Array.from(pageButtons).map(btn => ({
                            className: btn.className,
                            dataCurrent: btn.getAttribute('data-current') || '',
                            ariaLabel: btn.getAttribute('aria-label') || '',
                            text: btn.textContent.trim(),
                            disabled: btn.disabled,
                            id: btn.id || ''
                        }));

                        return {
                            paginationData,
                            buttons,
                            totalButtons: buttons.length
                        };
                    }
                """)

                if pagination_info:
                    print(f"📊 分页信息:")
                    if pagination_info['paginationData']:
                        pd = pagination_info['paginationData']
                        print(f"   总数: {pd['total']}, 每页: {pd['per']}, 当前页: {pd['current']}")

                    print(f"   页码按钮数量: {pagination_info['totalButtons']}")

                    # 查找下一页按钮
                    next_buttons = [btn for btn in pagination_info['buttons']
                                  if 'next' in btn['className'] or '下一页' in btn['ariaLabel']]

                    if next_buttons:
                        print("🎯 找到下一页按钮:")
                        for btn in next_buttons:
                            status = "禁用" if btn['disabled'] else "可用"
                            print(f"   ID: {btn['id']}, 类名: {btn['className']}, 状态: {status}")
                    else:
                        print("⚠️ 未找到下一页按钮")

            else:
                print("❌ 未找到翻页容器 div.page#page")

        except Exception as e:
            print(f"⚠️ 调试翻页容器失败: {e}")

    async def expand_all_order_items(self, page):
        """展开所有订单的"更多商品"按钮"""
        try:
            print("🔄 展开隐藏商品...")

            # 使用JavaScript查找并点击所有"展开更多"按钮
            expand_result = await page.evaluate("""
                () => {
                    let expandedCount = 0;

                    // Shadow DOM 穿透函数
                    function querySelectorDeep(selector, root = document) {
                        const elements = [];

                        // 在当前层级查找
                        const found = root.querySelectorAll(selector);
                        elements.push(...found);

                        // 递归查找所有 Shadow DOM
                        const allElements = root.querySelectorAll('*');
                        for (const el of allElements) {
                            if (el.shadowRoot) {
                                const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                                elements.push(...shadowElements);
                            }
                        }

                        return elements;
                    }

                    // 查找所有展开按钮
                    const expandButtons = querySelectorDeep('.expend-btn, .expand-btn, [class*="expend"], [class*="expand"]');
                    console.log(`找到 ${expandButtons.length} 个展开按钮`);

                    expandButtons.forEach((btn, index) => {
                        try {
                            const btnText = btn.textContent || '';
                            if (btnText.includes('展开更多') || btnText.includes('展开') || btnText.includes('更多')) {
                                console.log(`点击展开按钮 ${index + 1}: ${btnText.trim()}`);
                                btn.click();
                                expandedCount++;

                                // 等待一小段时间让内容加载
                                setTimeout(() => {}, 100);
                            }
                        } catch (e) {
                            console.log(`点击展开按钮 ${index + 1} 失败:`, e);
                        }
                    });

                    return {
                        totalButtons: expandButtons.length,
                        expandedCount: expandedCount
                    };
                }
            """)

            if expand_result['expandedCount'] > 0:
                print(f"✅ 展开了 {expand_result['expandedCount']} 个隐藏商品区域")

                # 等待内容加载
                await page.wait_for_timeout(2000)

                # 滚动页面确保所有内容都加载
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(500)
                await page.evaluate("window.scrollTo(0, 0)")
                await page.wait_for_timeout(500)
            else:
                print("ℹ️ 未发现需要展开的隐藏商品")

        except Exception as e:
            print(f"⚠️ 展开订单商品失败: {e}")

    async def download_image(self, image_url: str, save_dir: str, filename: str) -> Optional[str]:
        """下载图片并返回本地路径"""
        if not image_url or not image_url.startswith('http'):
            return None

        try:
            # 创建保存目录
            os.makedirs(save_dir, exist_ok=True)

            # 下载图片
            response = requests.get(image_url, timeout=10, stream=True)
            if response.status_code == 200:
                # 获取文件扩展名
                content_type = response.headers.get('content-type', '')
                if 'jpeg' in content_type or 'jpg' in content_type:
                    ext = '.jpg'
                elif 'png' in content_type:
                    ext = '.png'
                elif 'webp' in content_type:
                    ext = '.webp'
                else:
                    ext = '.jpg'  # 默认

                # 保存文件
                file_path = os.path.join(save_dir, f"{filename}{ext}")
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                # 转换为适合Excel的格式（如果需要）
                try:
                    with PILImage.open(file_path) as img:
                        # 调整图片大小以适合Excel单元格
                        img.thumbnail((150, 150), PILImage.Resampling.LANCZOS)
                        # 保存为JPEG格式（Excel兼容性更好）
                        final_path = os.path.join(save_dir, f"{filename}.jpg")
                        if img.mode in ('RGBA', 'LA', 'P'):
                            # 转换为RGB模式
                            rgb_img = PILImage.new('RGB', img.size, (255, 255, 255))
                            rgb_img.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                            rgb_img.save(final_path, 'JPEG', quality=85)
                        else:
                            img.save(final_path, 'JPEG', quality=85)

                        # 删除原始文件（如果不同）
                        if file_path != final_path and os.path.exists(file_path):
                            os.remove(file_path)

                        return final_path
                except Exception:
                    # 如果图片处理失败，返回原始文件
                    return file_path

        except Exception as e:
            print(f"⚠️ 下载图片失败 {image_url}: {e}")
            return None

    def _parse_api_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析API响应数据"""
        if not data or not isinstance(data, dict):
            print(f"⚠️ 无效的API响应数据: "
                  f"{type(data)}")
            return []

        orders = []

        # 常见的订单数据结构路径
        possible_paths = [
            ['data', 'result', 'list'],
            ['data', 'list'],
            ['result', 'data'],
            ['data', 'orderList'],
            ['orderList'],
            ['list']
        ]

        def find_nested_value(obj, path):
            """查找嵌套值"""
            try:
                current = obj
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        return None
                return current
            except ValueError as e:
                print(f"⚠️ 查找嵌套值失败: {e}")
                return None

        for path in possible_paths:
            try:
                order_list = find_nested_value(data, path)
                if order_list and isinstance(order_list, list):
                    print(f"✅ 找到订单列表，路径: {' -> '.join(path)}, "
                          f"数量: {len(order_list)}")

                    for i, item in enumerate(order_list):
                        if not isinstance(item, dict):
                            print(f"⚠️ 订单项 {i} 不是字典类型: "
                                  f"{type(item)}")
                            continue

                        order = self._extract_order_fields(item)
                        if order:
                            orders.append(order)
                        else:
                            print(f"⚠️ 订单项 {i} 提取失败")

                    break
                elif order_list is not None:
                    print(f"⚠️ 路径 {' -> '.join(path)} 存在但不是列表: "
                          f"{type(order_list)}")
            except (ValueError, TypeError) as e:
                print(f"⚠️ 处理路径 {' -> '.join(path)} 失败: "
                      f"{e}")
                continue

        if not orders:
            print("⚠️ 未找到有效的订单数据")
            # 输出数据结构以便调试
            data_keys = list(data.keys()) if isinstance(data, dict) else type(data)
            print(f"🔍 数据结构: {data_keys}")

        return orders

    def _extract_order_fields(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从API数据中提取订单字段"""
        try:
            # 输入验证
            if not item or not isinstance(item, dict):
                print(f"⚠️ 无效的输入数据类型: {type(item)}")
                return None

            if not item:
                print("⚠️ 空的输入数据")
                return None

            # 常见的字段映射
            field_mapping = {
                'order_id': ['orderId', 'id', 'orderNo', 'orderCode'],
                'title': ['title', 'subject', 'productName', 'name', 'goodsName'],
                'price': ['price', 'amount', 'unitPrice', 'money'],
                'quantity': ['quantity', 'num', 'count', 'number'],
                'total_amount': ['totalAmount', 'totalMoney', 'sumMoney'],
                'shop_name': ['shopName', 'sellerName', 'companyName', 'storeName'],
                'order_time': ['createTime', 'orderTime', 'gmtCreate', 'time'],
                'status': ['status', 'orderStatus', 'state', 'tradeStatus'],
                'image_url': ['picUrl', 'imageUrl', 'imgUrl', 'imagePath'],
                'detail_url': ['detailUrl', 'url', 'link', 'href']
            }

            order = {}
            missing_fields = []

            for target_field, source_fields in field_mapping.items():
                found = False
                for source_field in source_fields:
                    if source_field in item and item[source_field]:
                        order[target_field] = item[source_field]
                        found = True
                        break

                if not found:
                    order[target_field] = ""
                    if target_field in ['title', 'order_id']:
                        missing_fields.append(target_field)

            # 标准化价格字段
            for price_field in ['price', 'total_amount']:
                if price_field in order:
                    original_value = order[price_field]
                    if isinstance(original_value, (int, float)):
                        order[price_field] = float(original_value)
                    elif isinstance(original_value, str):
                        # 移除货币符号和逗号
                        price_str = re.sub(r'[^\d.]', '', str(original_value))
                        order[price_field] = float(price_str) if price_str else 0.0
                    else:
                        order[price_field] = 0.0
                        print(f"⚠️ 价格字段类型异常: {price_field} = "
                              f"{original_value} ({type(original_value)})")

            # 标准化数量字段
            if 'quantity' in order:
                quantity_value = order['quantity']
                if isinstance(quantity_value, (int, float)):
                    order['quantity'] = int(quantity_value)
                elif isinstance(quantity_value, str):
                    try:
                        order['quantity'] = int(float(quantity_value))
                    except (ValueError, TypeError):
                        order['quantity'] = 0
                        print(f"⚠️ 数量字段转换失败: {quantity_value}")
                else:
                    order['quantity'] = 0

            # 验证必需字段
            if not order.get('title'):
                print("⚠️ 缺少必需字段: title")
                return None

            # 验证数据合理性
            if order.get('price', 0) < 0:
                print(f"⚠️ 价格不能为负数: {order.get('price')}")
                order['price'] = 0

            if order.get('quantity', 0) < 0:
                print(f"⚠️ 数量不能为负数: {order.get('quantity')}")
                order['quantity'] = 0

            if order.get('total_amount', 0) < 0:
                print(f"⚠️ 总金额不能为负数: {order.get('total_amount')}")
                order['total_amount'] = 0

            # 计算总金额验证
            if order.get('price') and order.get('quantity'):
                calculated_total = order['price'] * order['quantity']
                if abs(calculated_total - order.get('total_amount', 0)) > 0.01:
                    print(f"⚠️ 总金额计算不一致: 计算值={calculated_total}, "
                          f"实际值={order.get('total_amount')}")
                    # 以计算值为准
                    order['total_amount'] = calculated_total

            return order

        except (ValueError, TypeError, KeyError) as e:
            print(f"⚠️ 提取订单字段失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    async def extract_orders_from_dom(self, page) -> List[Dict[str, Any]]:
        """通过DOM解析抓取订单数据 - 以商品SKU为单位"""
        try:
            # 先检查页面状态
            await self.debug_page_state(page)

            # 先展开所有订单的"更多商品"
            await self.expand_all_order_items(page)

            # 使用JavaScript提取订单数据 - 修改为以商品SKU为单位
            js_code = """
            () => {
                const toText = el => (el?.textContent || "").trim();
                const toNumber = str => (
                    parseFloat(String(str).replace(/[^\\d.]/g, "")) || 0
                );

                // 开始DOM解析

                // Shadow DOM 穿透函数
                function querySelectorDeep(selector, root = document) {
                    const elements = [];

                    // 在当前层级查找
                    const found = root.querySelectorAll(selector);
                    elements.push(...found);

                    // 递归查找所有 Shadow DOM
                    const allElements = root.querySelectorAll('*');
                    for (const el of allElements) {
                        if (el.shadowRoot) {
                            console.log("发现Shadow DOM:", el.tagName);
                            const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                            elements.push(...shadowElements);
                        }
                    }

                    return elements;
                }

                // 首先查找订单列表容器（可能在Shadow DOM中）
                let orderContainer = document.querySelector('.order-list-container');
                console.log("普通查找订单容器:", orderContainer ? "✅ 找到" : "❌ 未找到");

                if (!orderContainer) {
                    console.log("🔍 在Shadow DOM中查找订单容器...");
                    const shadowContainers = querySelectorDeep('.order-list-container');
                    if (shadowContainers.length > 0) {
                        orderContainer = shadowContainers[0];
                        console.log("✅ 在Shadow DOM中找到订单容器");
                    }
                }

                if (!orderContainer) {
                    console.log("⚠️ 未找到 .order-list-container，尝试其他容器...");
                    // 备用容器选择器
                    const containerSelectors = [
                        '.order-list',
                        '[class*="order-list"]',
                        '[class*="order"][class*="container"]',
                        '[class*="trade-list"]',
                        '.list-container'
                    ];

                    for (const selector of containerSelectors) {
                        let container = document.querySelector(selector);
                        if (!container) {
                            const shadowContainers = querySelectorDeep(selector);
                            if (shadowContainers.length > 0) {
                                container = shadowContainers[0];
                                console.log(`✅ 在Shadow DOM中找到备用容器: ${selector}`);
                                break;
                            }
                        } else {
                            console.log(`✅ 找到备用容器: ${selector}`);
                            break;
                        }
                    }
                }

                // 在容器内查找订单项（支持Shadow DOM）
                console.log("🔍 查找订单项（支持Shadow DOM）...");

                // 针对订单容器内的选择器 - 基于实际HTML结构
                const selectors = [
                    'order-item',                                    // 直接的order-item标签
                    'order-item[data-tracker]',                      // 带data-tracker的order-item
                    '[data-tracker*="order"]',                       // 任何包含order的data-tracker
                    '.order-item',                                   // 传统的CSS类选择器（备用）
                    '[class*="order-item"]',
                    '[class*="order"][class*="item"]'
                ];

                let items = [];
                let usedSelector = '';

                // 调试每个选择器（包括Shadow DOM）
                for (const selector of selectors) {
                    // 先尝试普通查找
                    let found = Array.from(document.querySelectorAll(selector));
                    console.log(`普通查找 ${selector}: 找到 ${found.length} 个元素`);

                    // 如果普通查找没找到，尝试Shadow DOM穿透
                    if (found.length === 0) {
                        found = querySelectorDeep(selector);
                        console.log(`Shadow DOM查找 ${selector}: 找到 ${found.length} 个元素`);
                    }

                    if (found.length > 0) {
                        items = found;
                        usedSelector = selector;
                        console.log(`✅ 使用选择器: ${selector} (找到 ${found.length} 个元素)`);
                        break;
                    }
                }

                if (items.length === 0 && orderContainer) {
                    console.log("⚠️ 在订单容器内未找到标准订单元素，尝试分析容器结构...");

                    // 分析容器内的直接子元素
                    const directChildren = Array.from(orderContainer.children);
                    console.log(`订单容器直接子元素数量: ${directChildren.length}`);

                    if (directChildren.length > 0) {
                        // 尝试找到包含订单信息的子元素
                        const possibleItems = directChildren.filter(el => {
                            const text = toText(el);
                            const hasPrice = text.includes('￥') || text.includes('¥') || text.includes('元');
                            const hasNumbers = /\\d+/.test(text);
                            const hasReasonableLength = text.length > 20 && text.length < 2000;
                            return hasPrice && hasNumbers && hasReasonableLength;
                        });

                        console.log(`找到 ${possibleItems.length} 个可能的订单元素`);
                        items = possibleItems;
                    }
                }

                // 修改为以商品SKU为单位提取数据
                const allProducts = [];

                items.forEach((item, orderIndex) => {
                    console.log(`处理第 ${orderIndex + 1} 个订单项...`);

                    // 检查是否有Shadow DOM
                    const shadowRoot = item.shadowRoot;
                    if (!shadowRoot) {
                        console.log(`订单 ${orderIndex + 1} 没有Shadow DOM，跳过`);
                        return;
                    }

                    console.log(`订单 ${orderIndex + 1} 有Shadow DOM，提取订单和商品信息...`);

                    // 调试：输出Shadow DOM的结构
                    console.log(`  Shadow DOM内容预览: ${shadowRoot.innerHTML.substring(0, 200)}...`);

                    // 查找所有可能的类名
                    const allElements = shadowRoot.querySelectorAll('*');
                    const classNames = new Set();
                    allElements.forEach(el => {
                        if (el.className) {
                            el.className.split(' ').forEach(cls => {
                                if (cls.trim()) classNames.add(cls.trim());
                            });
                        }
                    });
                    console.log(`  发现的类名: ${Array.from(classNames).slice(0, 10).join(', ')}...`);

                    // 提取订单级别的信息（这些信息将被复制到每个商品记录中）
                    const orderInfo = {};

                    // 从Shadow DOM中提取订单号
                    let orderId = "";
                    // 方法1: 从data-tracker属性中提取
                    const orderItemEl = shadowRoot.querySelector('order-item[data-tracker]');
                    if (orderItemEl) {
                        const tracker = orderItemEl.getAttribute('data-tracker');
                        if (tracker) {
                            orderId = tracker;
                            console.log(`  订单号(tracker): ${orderId}`);
                        }
                    }
                    // 方法2: 从文本中提取纯数字订单号
                    if (!orderId) {
                        const orderIdEl = shadowRoot.querySelector('.order-id, [class*="order-id"], [class*="orderId"]');
                        if (orderIdEl) {
                            const rawText = toText(orderIdEl);
                            const orderMatch = rawText.match(/\\d{15,}/);
                            if (orderMatch) {
                                orderId = orderMatch[0];
                                console.log(`  订单号(文本提取): ${orderId}`);
                            } else {
                                orderId = rawText.trim();
                                console.log(`  订单号(原始): ${orderId}`);
                            }
                        }
                    }
                    orderInfo.orderId = orderId;

                    // 从Shadow DOM中提取订单时间
                    let orderTime = "";
                    const orderTimeEl = shadowRoot.querySelector('.order-time');
                    if (orderTimeEl) {
                        orderTime = toText(orderTimeEl);
                        console.log(`  订单时间: ${orderTime}`);
                    }
                    orderInfo.orderTime = orderTime;

                    // 从Shadow DOM中提取下单账号
                    let buyerAccount = "";
                    const buyerAccountEl = shadowRoot.querySelector('.buyer-account');
                    if (buyerAccountEl) {
                        buyerAccount = toText(buyerAccountEl);
                        console.log(`  下单账号: ${buyerAccount}`);
                    }
                    orderInfo.buyerAccount = buyerAccount;

                    // 提取卖家名称（生产商）- 订单级别信息
                    let shopName = "";
                    const shopSelectors = [
                        '.company-name',                                    // 主要选择器
                        '.shop-name, .seller-name, .store-name',           // 备用选择器
                        '[class*="company"], [class*="shop"], [class*="seller"], [class*="store"]'
                    ];
                    for (const selector of shopSelectors) {
                        const el = shadowRoot.querySelector(selector);
                        if (el && toText(el)) {
                            shopName = toText(el);
                            console.log(`  卖家名称: ${shopName}`);
                            break;
                        }
                    }
                    orderInfo.shopName = shopName;

                    // 查找订单中的所有商品条目（以商品SKU为单位）
                    console.log(`  查找订单中的商品条目...`);

                    // 查找商品条目容器 - 包括展开后的商品
                    let productEntries = Array.from(shadowRoot.querySelectorAll('order-item-entry-product'));
                    console.log(`  找到 ${productEntries.length} 个 order-item-entry-product 条目`);

                    if (productEntries.length === 0) {
                        console.log(`  未找到 order-item-entry-product，尝试其他选择器...`);
                        // 备用选择器 - 按优先级排序
                        const backupSelectors = [
                            'order-item-entry',                             // 订单条目
                            '.product-entry, .product-item, .goods-item',   // 商品条目
                            '[class*="entry-product"]',                     // 包含entry-product的类
                            '[class*="product-entry"]',                     // 包含product-entry的类
                            '[class*="product"][class*="item"]',            // 同时包含product和item的类
                            '.item-container, .product-container'           // 容器类
                        ];

                        for (const selector of backupSelectors) {
                            const found = Array.from(shadowRoot.querySelectorAll(selector));
                            if (found.length > 0) {
                                console.log(`  使用备用选择器 ${selector} 找到 ${found.length} 个商品`);
                                productEntries = found;
                                break;
                            }
                        }
                    }

                    // 如果仍然没有找到商品条目，创建一个虚拟的商品条目（兼容旧逻辑）
                    if (productEntries.length === 0) {
                        console.log(`  未找到具体商品条目，将整个订单作为一个商品处理`);
                        productEntries.push(shadowRoot); // 使用整个shadowRoot作为商品条目
                    }

                    // 遍历每个商品条目，为每个SKU创建独立记录
                    productEntries.forEach((productEntry, productIndex) => {
                        console.log(`    处理商品 ${productIndex + 1}/${productEntries.length}...`);

                        const product = extractProductInfo(productEntry, orderInfo, orderIndex, productIndex);
                        if (product) {
                            allProducts.push(product);
                        }
                    });
                });

                // 提取单个商品信息的函数
                function extractProductInfo(productEntry, orderInfo, orderIndex, productIndex) {
                    const isFullOrder = productEntry === productEntry.ownerDocument?.querySelector('*')?.shadowRoot;
                    console.log(`      提取商品信息 (${isFullOrder ? '整订单模式' : '单商品模式'})...`);

                    let title = "";
                    let productSpecs = "";
                    let imageUrl = "";
                    let price = 0;
                    let quantity = 1;

                    // 提取商品名称
                    if (productEntry.shadowRoot) {
                        // 如果商品条目有自己的Shadow DOM
                        const productNameEl = productEntry.shadowRoot.querySelector('.product-name, .title, .name, .goods-name');
                        if (productNameEl) {
                            title = toText(productNameEl);
                            console.log(`      商品名称: ${title}`);
                        }
                    } else {
                        // 在当前容器中查找商品名称
                        const productNameEl = productEntry.querySelector('.product-name, .title, .name, .goods-name');
                        if (productNameEl) {
                            title = toText(productNameEl);
                            console.log(`      商品名称: ${title}`);
                        }
                    }

                    // 如果没找到商品名称，尝试从父级查找
                    if (!title && !isFullOrder) {
                        const parentNameEl = productEntry.closest('[class*="product"]')?.querySelector('.product-name, .title, .name');
                        if (parentNameEl) {
                            title = toText(parentNameEl);
                            console.log(`      商品名称(父级): ${title}`);
                        }
                    }

                    // 提取商品规格信息（颜色、尺寸等）
                    const specSelectors = [
                        '.sku-info-item, .spec-item, .attr-item',
                        '[class*="sku"], [class*="spec"], [class*="attr"]',
                        '.color, .size, .style'
                    ];

                    const specs = [];
                    for (const selector of specSelectors) {
                        const specElements = (productEntry.shadowRoot || productEntry).querySelectorAll(selector);
                        specElements.forEach(item => {
                            const specText = toText(item);
                            if (specText && specText.length > 0 && !specs.includes(specText)) {
                                specs.push(specText);
                            }
                        });
                        if (specs.length > 0) break; // 找到规格就停止
                    }
                    productSpecs = specs.join(', ');
                    if (productSpecs) {
                        console.log(`      商品规格: ${productSpecs}`);
                    }

                    // 提取商品图片
                    const imgSelectors = [
                        'img[src*="img"], img[src*="photo"], img[src*="pic"]',
                        '.product-img img, .goods-img img, .item-img img',
                        'img'
                    ];
                    for (const selector of imgSelectors) {
                        const imgEl = (productEntry.shadowRoot || productEntry).querySelector(selector);
                        if (imgEl && imgEl.src && imgEl.src.startsWith('http')) {
                            imageUrl = imgEl.src;
                            console.log(`      商品图片: ${imageUrl}`);
                            break;
                        }
                    }

                    // 提取商品价格 - 基于实际HTML结构
                    const priceSelectors = [
                        '.actual-unit-price',                                   // 主要选择器（从截图中确认）
                        '.unit-price',                                          // 备用选择器
                        '.price',                                               // 通用价格选择器
                        '[class*="actual-unit-price"]',                         // 包含actual-unit-price的类名
                        '[class*="unit-price"]',                                // 包含unit-price的类名
                        '[class*="price"]',                                     // 包含price的类名
                        'div:contains("¥")',                                    // 包含¥符号的div
                        'span:contains("¥")'                                    // 包含¥符号的span
                    ];

                    for (const selector of priceSelectors) {
                        try {
                            const priceEl = (productEntry.shadowRoot || productEntry).querySelector(selector);
                            if (priceEl) {
                                const priceText = toText(priceEl);
                                console.log(`      尝试价格选择器 ${selector}: "${priceText}"`);

                                if (priceText && (priceText.includes('￥') || priceText.includes('¥') || priceText.includes('元'))) {
                                    const extractedPrice = toNumber(priceText);
                                    if (extractedPrice > 0) {
                                        price = extractedPrice;
                                        console.log(`      ✅ 商品单价提取成功: ${priceText} -> ${price} (选择器: ${selector})`);
                                        break;
                                    }
                                }
                            }
                        } catch (e) {
                            console.log(`      价格选择器 ${selector} 出错: ${e.message}`);
                        }
                    }

                    // 如果还是没找到价格，尝试更广泛的搜索
                    if (price === 0) {
                        console.log(`      价格未找到，尝试广泛搜索...`);
                        const allElements = (productEntry.shadowRoot || productEntry).querySelectorAll('*');
                        for (const el of allElements) {
                            const text = toText(el);
                            if (text && (text.includes('￥') || text.includes('¥')) && text.length < 20) {
                                const extractedPrice = toNumber(text);
                                if (extractedPrice > 0) {
                                    price = extractedPrice;
                                    console.log(`      ✅ 广泛搜索找到价格: ${text} -> ${price}`);
                                    break;
                                }
                            }
                        }
                    }

                    // 提取商品数量
                    const quantitySelectors = [
                        '.quantity-amount, .quantity, .num, .count',
                        '[class*="quantity"], [class*="num"], [class*="count"]'
                    ];

                    for (const selector of quantitySelectors) {
                        const quantityEl = (productEntry.shadowRoot || productEntry).querySelector(selector);
                        if (quantityEl) {
                            const quantityText = toText(quantityEl);
                            if (quantityText) {
                                const qty = parseInt(quantityText);
                                if (qty > 0) {
                                    quantity = qty;
                                    console.log(`      商品数量: ${quantityText} -> ${quantity}`);
                                    break;
                                }
                            }
                        }
                    }

                    // 如果没有找到商品名称，使用订单号和商品索引作为标题
                    if (!title) {
                        if (orderInfo.orderId) {
                            title = `订单 ${orderInfo.orderId} - 商品 ${productIndex + 1}`;
                        } else {
                            title = `订单 ${orderIndex + 1} - 商品 ${productIndex + 1}`;
                        }
                    }

                    // 如果有规格信息，添加到标题中
                    if (productSpecs) {
                        title = `${title} (${productSpecs})`;
                    }

                    // 计算总金额
                    const calculatedTotal = price * quantity;

                    // 创建商品记录
                    const productRecord = {
                        // 商品信息
                        title: title,
                        product_specs: productSpecs,
                        image_url: imageUrl,
                        price: price,
                        quantity: quantity,
                        total_amount: calculatedTotal,

                        // 订单信息（复制到每个商品记录中）
                        order_id: orderInfo.orderId,
                        order_time: orderInfo.orderTime,
                        buyer_account: orderInfo.buyerAccount,
                        shop_name: orderInfo.shopName, // 卖家名称（生产商）

                        // 其他字段
                        detail_url: "",
                        status: ""
                    };

                    console.log(`      商品记录创建完成:`, {
                        title: productRecord.title,
                        specs: productRecord.product_specs,
                        price: productRecord.price,
                        quantity: productRecord.quantity,
                        shopName: productRecord.shop_name
                    });

                    return productRecord;
                }

                console.log(`🎉 DOM解析完成，找到 ${allProducts.length} 个商品SKU`);
                return allProducts;
            }
            """

            # 执行JavaScript提取数据
            orders_data = await page.evaluate(js_code)
            self.stats['dom_success'] = len(orders_data)

            print(f"🔍 DOM解析成功，提取到 {len(orders_data)} 个订单")
            return orders_data

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ DOM解析失败: {e}")
            return []

    async def extract_orders_from_page(
            self,
            page,
            url: str
    ) -> List[Dict[str, Any]]:
        """从订单页面提取订单数据"""
        print(f"🌐 目标订单页面: {url}")

        all_orders = []

        try:
            # 隐藏水印
            await page.add_style_tag(
                content=".quark-watermark{display:none!important}"
            )

            # 检查当前页面是否已经是订单页面
            current_url = page.url
            print(f"📍 当前页面: {current_url}")

            # 如果不是订单页面，则导航到订单页面
            if 'trade-order-list' not in current_url and 'buyer_order_list' not in current_url:
                print("🔄 导航到订单页面...")
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        print(f"🔄 尝试导航 (第 {attempt + 1}/{max_retries} 次)...")
                        await page.goto(
                            url, timeout=30000, wait_until="domcontentloaded"
                        )

                        # 等待页面加载
                        await page.wait_for_load_state(
                            "networkidle", timeout=30000
                        )
                        await page.wait_for_timeout(2000)

                        # 检查页面是否加载成功
                        page_title = await page.title()
                        if page_title:
                            print(f"📄 页面标题: {page_title}")
                            break
                        else:
                            print("⚠️ 页面标题为空，重试...")
                            if attempt < max_retries - 1:
                                await page.wait_for_timeout(3000)
                                continue
                    except Exception as e:
                        print(f"⚠️ 导航失败 (第 {attempt + 1} 次): {e}")
                        if attempt < max_retries - 1:
                            await page.wait_for_timeout(3000)
                            continue
                        else:
                            raise
            else:
                print("✅ 已在订单页面，无需导航")

            # 根据参数决定是否进行多页抓取
            if hasattr(self, 'enable_all_pages') and self.enable_all_pages:
                all_orders = await self.extract_all_pages_orders(page)
            else:
                # 单页抓取（原有逻辑）
                all_orders = await self.extract_single_page_orders(page)

            return all_orders

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ 提取订单数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def extract_single_page_orders(self, page) -> List[Dict[str, Any]]:
        """提取单页订单数据（原有逻辑）"""
        # 监听API响应
        api_orders = await self.extract_orders_from_api(page)

        # 滚动触发懒加载
        print("📜 滚动页面触发懒加载...")
        await page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await page.wait_for_timeout(1000)

        # 再次向上滚动，确保所有数据加载
        await page.evaluate("window.scrollTo(0, 0)")
        await page.wait_for_timeout(500)
        await page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await page.wait_for_timeout(1000)

        # 如果API没有获取到数据，使用DOM解析
        if not api_orders:
            print("🔍 API未获取到数据，使用DOM解析...")
            dom_orders = await self.extract_orders_from_dom(page)

            if dom_orders:
                print(f"✅ DOM解析成功，提取到 {len(dom_orders)} 个订单")
                self.stats['dom_success'] = len(dom_orders)
                return dom_orders
            else:
                print("⚠️ DOM解析也失败")
                return []
        else:
            print(f"✅ API解析成功，提取到 {len(api_orders)} 个订单")
            self.stats['api_success'] = len(api_orders)
            return api_orders

    async def extract_all_pages_orders(self, page) -> List[Dict[str, Any]]:
        """提取所有页面的订单数据"""
        all_orders = []
        current_page = 1
        max_pages = getattr(self, 'max_pages', 10)  # 使用参数设置的最大页数

        print(f"🔄 开始多页数据抓取...")

        while current_page <= max_pages:
            print(f"\n📄 正在处理第 {current_page} 页...")

            # 监听API响应
            api_orders = await self.extract_orders_from_api(page)

            # 滚动触发懒加载
            print("📜 滚动页面触发懒加载...")
            await page.evaluate(
                "window.scrollTo(0, document.body.scrollHeight)"
            )
            await page.wait_for_timeout(1000)

            # 再次向上滚动，确保所有数据加载
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(500)
            await page.evaluate(
                "window.scrollTo(0, document.body.scrollHeight)"
            )
            await page.wait_for_timeout(1000)

            # 提取当前页数据
            page_orders = []
            if api_orders:
                page_orders = api_orders
                print(f"✅ API解析成功，提取到 {len(page_orders)} 个订单")
                self.stats['api_success'] += len(page_orders)
            else:
                print("🔍 API未获取到数据，使用DOM解析...")
                page_orders = await self.extract_orders_from_dom(page)
                if page_orders:
                    print(f"✅ DOM解析成功，提取到 {len(page_orders)} 个订单")
                    self.stats['dom_success'] += len(page_orders)
                else:
                    print("⚠️ 当前页面未提取到数据")

            # 如果当前页没有数据，可能已到最后一页
            if not page_orders:
                print(f"📄 第 {current_page} 页无数据，可能已到最后一页")
                break

            all_orders.extend(page_orders)
            print(f"📊 累计提取 {len(all_orders)} 个订单")

            # 调试翻页容器（仅在第一页时调试）
            if current_page == 1:
                await self.debug_pagination_container(page)

            # 尝试翻到下一页
            has_next_page = await self.go_to_next_page(page)
            if not has_next_page:
                print("📄 已到最后一页，抓取完成")
                break

            current_page += 1

            # 等待页面加载
            await page.wait_for_timeout(2000)

        print(f"🎉 多页抓取完成，总共提取 {len(all_orders)} 个订单")
        return all_orders

    async def go_to_next_page(self, page) -> bool:
        """尝试翻到下一页 - 基于实际HTML结构"""
        try:
            print("🔄 尝试翻到下一页...")

            # 方法1: 点击下一页按钮（基于截图中的实际结构）
            next_button_selectors = [
                'button.ui-page.ui-page-next#right',  # 精确匹配下一页按钮
                'button.ui-page-next',                # 备用选择器
                '#right[aria-label*="下一页"]',       # 通过ID和aria-label
                'button[aria-label*="下一页"]'        # 通用下一页按钮
            ]

            for selector in next_button_selectors:
                try:
                    next_button = page.locator(selector).first
                    if await next_button.is_visible():
                        # 检查按钮是否被禁用
                        is_disabled = await next_button.get_attribute('disabled')
                        if not is_disabled:
                            print(f"✅ 找到下一页按钮: {selector}")
                            await next_button.click()
                            await page.wait_for_load_state("networkidle", timeout=15000)
                            await page.wait_for_timeout(2000)  # 等待页面稳定
                            print("✅ 翻页成功")
                            return True
                        else:
                            print("⚠️ 下一页按钮已禁用，可能已到最后一页")
                            return False
                except Exception as e:
                    print(f"⚠️ 按钮 {selector} 点击失败: {e}")
                    continue

            # 方法2: 通过URL参数翻页（备用方案）
            current_url = page.url
            if 'page=' in current_url:
                import re
                # 提取当前页码
                page_match = re.search(r'page=(\d+)', current_url)
                if page_match:
                    current_page_num = int(page_match.group(1))
                    next_page_num = current_page_num + 1
                    next_url = re.sub(r'page=\d+', f'page={next_page_num}', current_url)
                    print(f"✅ 通过URL翻页: page={next_page_num}")
                    try:
                        await page.goto(next_url, wait_until="networkidle", timeout=15000)
                        await page.wait_for_timeout(2000)
                        return True
                    except Exception as e:
                        print(f"⚠️ URL翻页失败: {e}")
                        return False

            print("⚠️ 未找到有效的翻页方式，可能已到最后一页")
            return False

        except Exception as e:
            print(f"⚠️ 翻页失败: {e}")
            return False

    async def save_to_excel(
            self,
            orders: List[Dict[str, Any]],
            output_path: str
    ):
        """保存订单数据到Excel，包含图片和超链接"""
        try:
            if not orders:
                print("⚠️ 没有商品数据可保存")
                return

            # 数据验证
            valid_orders = []
            for i, order in enumerate(orders):
                if not isinstance(order, dict):
                    print(f"⚠️ 商品 {i} 不是字典类型，跳过")
                    continue

                if not order.get('title'):
                    print(f"⚠️ 商品 {i} 缺少标题，跳过")
                    continue

                valid_orders.append(order)

            if not valid_orders:
                print("⚠️ 没有有效的商品数据可保存")
                return

            print(f"📊 处理 {len(valid_orders)} 个商品SKU...")

            # 下载图片
            images_dir = os.path.join(os.path.dirname(output_path), "images")
            print("📥 下载商品图片...")

            for i, order in enumerate(valid_orders):
                image_url = order.get('image_url', '')
                if image_url:
                    filename = f"product_{i+1}"
                    local_image_path = await self.download_image(image_url, images_dir, filename)
                    order['local_image_path'] = local_image_path
                else:
                    order['local_image_path'] = None

            df = pd.DataFrame(valid_orders)

            # 重命名列为中文 - 适应商品SKU数据结构
            column_mapping = {
                'order_id': '订单号',
                'title': '商品名称',
                'product_specs': '商品规格',
                'price': '商品单价',
                'quantity': '商品数量',
                'total_amount': '商品小计',
                'shop_name': '卖家名称（生产商）',
                'image_url': '商品图片',
                'detail_url': '详情页'
            }

            # 确保所有列都存在
            for target_col in column_mapping.values():
                if target_col not in df.columns:
                    df[target_col] = ""

            df = df.rename(columns=column_mapping)

            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 使用openpyxl创建Excel文件以支持图片和超链接
            await self.save_excel_with_images(df, output_path, valid_orders)

            print(f"✅ 商品SKU数据已保存到: {output_path}")
            print(f"📊 共 {len(valid_orders)} 个商品SKU")

            # 显示数据统计
            print("\n📋 数据统计:")
            print(f"  商品SKU数量: {len(df)} 个")

            # 统计订单数量
            try:
                if '订单号' in df.columns:
                    unique_orders = len(df['订单号'].unique())
                    print(f"  订单数量: {unique_orders} 个")
            except Exception:
                pass

            # 统计卖家数量
            try:
                if '卖家名称（生产商）' in df.columns:
                    unique_sellers = len(df['卖家名称（生产商）'].unique())
                    print(f"  卖家数量: {unique_sellers} 个")
            except Exception:
                pass

            # 统计总金额
            try:
                if '商品小计' in df.columns:
                    df_copy = df.copy()
                    df_copy['商品小计'] = pd.to_numeric(df_copy['商品小计'], errors='coerce').fillna(0)
                    total_sum = float(df_copy['商品小计'].sum())
                    print(f"  商品总金额: {total_sum:.2f} 元")
            except Exception:
                pass

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ 保存Excel失败: {e}")
            import traceback
            traceback.print_exc()

    async def save_excel_with_images(self, df, output_path: str, original_data: List[Dict]):
        """使用openpyxl保存Excel文件，支持图片和超链接"""
        try:
            from openpyxl import Workbook
            from openpyxl.drawing.image import Image as OpenpyxlImage
            from openpyxl.utils.dataframe import dataframe_to_rows
            from openpyxl.styles import Font

            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "商品SKU数据"

            # 写入数据（不包含图片列）
            df_for_excel = df.drop(columns=['local_image_path'], errors='ignore')

            # 写入表头和数据
            for r in dataframe_to_rows(df_for_excel, index=False, header=True):
                ws.append(r)

            # 设置列宽
            column_widths = {
                'A': 20,  # 订单号
                'B': 30,  # 商品名称
                'C': 20,  # 商品规格
                'D': 12,  # 商品单价
                'E': 10,  # 商品数量
                'F': 12,  # 商品小计
                'G': 25,  # 卖家名称
                'H': 20,  # 商品图片
                'I': 15,  # 详情页
            }

            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width

            # 设置行高（为图片预留空间）
            for row in range(2, len(df) + 2):  # 从第2行开始（跳过表头）
                ws.row_dimensions[row].height = 100

            # 添加图片和超链接
            for idx, _ in enumerate(df.iterrows()):
                row_num = idx + 2  # Excel行号（从2开始，因为1是表头）
                original_item = original_data[idx]

                # 添加图片
                local_image_path = original_item.get('local_image_path')
                if local_image_path and os.path.exists(local_image_path):
                    try:
                        img = OpenpyxlImage(local_image_path)
                        # 调整图片大小
                        img.width = 80
                        img.height = 80
                        # 插入图片到H列
                        ws.add_image(img, f'H{row_num}')
                    except Exception as e:
                        print(f"⚠️ 插入图片失败 {local_image_path}: {e}")

                # 添加原图超链接
                image_url = original_item.get('image_url', '')
                if image_url:
                    cell = ws[f'H{row_num}']
                    cell.hyperlink = image_url
                    cell.value = "原图"
                    # 设置超链接样式
                    cell.font = Font(color="0000FF", underline="single")

                # 添加详情页超链接
                detail_url = original_item.get('detail_url', '')
                if detail_url:
                    cell = ws[f'I{row_num}']
                    cell.hyperlink = detail_url
                    cell.value = "详情页"
                    # 设置超链接样式
                    cell.font = Font(color="0000FF", underline="single")

            # 保存文件
            wb.save(output_path)
            print(f"📸 已插入 {sum(1 for item in original_data if item.get('local_image_path'))} 张商品图片")

        except Exception as e:
            print(f"⚠️ 使用openpyxl保存失败，回退到pandas: {e}")
            # 回退到普通的pandas保存
            df.to_excel(output_path, index=False)

    def print_stats(self):
        """打印统计信息"""
        print("\n📊 抓取统计:")
        print(f"  总商品SKU数: {self.stats['total_orders']}")
        print(f"  API成功: {self.stats['api_success']}")
        print(f"  DOM成功: {self.stats['dom_success']}")
        print(f"  失败: {self.stats['failed']}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='1688订单数据抓取工具')
    parser.add_argument(
        '--url', '-u',
        default='https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html?tradeStatus=waitbuyerreceive&spm=a260k.home2025.topmenu.dmyorder&page=1&pageSize=10',
        help='订单页面URL'
    )
    parser.add_argument('--output', '-o', help='输出Excel文件路径（默认：自动生成）')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--all-pages', action='store_true', help='抓取所有页面的订单数据')
    parser.add_argument('--max-pages', type=int, default=10, help='最大抓取页数（默认：10页）')

    args = parser.parse_args()

    # 设置输出路径
    if not args.output:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        args.output = PROJECT_ROOT / "reports" / f"1688_orders_{timestamp}.xlsx"

    print("🚀 开始1688订单数据抓取（以商品SKU为单位）...")
    print(f"输出文件: {args.output}")
    print("💡 请确保Chrome中已打开1688订单页面:")
    print("   https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html")
    print("   并已筛选好需要的订单状态")

    # 确保输出路径是字符串
    output_path_str = str(args.output)

    # 创建输出目录
    import os
    os.makedirs(os.path.dirname(output_path_str), exist_ok=True)

    extractor = OrderDataExtractor()

    # 设置多页抓取参数 - 默认启用多页抓取
    extractor.enable_all_pages = True  # 默认启用多页抓取
    extractor.max_pages = args.max_pages

    try:
        # 连接到Chrome调试实例
        debug_port = 9222
        print("🔗 正在连接到Chrome调试实例...")

        # 检查Chrome调试接口
        try:
            version_url = f"http://localhost:{debug_port}/json/version"
            proxies = {'http': None, 'https': None}
            response = requests.get(version_url, timeout=5, proxies=proxies)
            if response.status_code != 200:
                print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
                print("💡 请先运行 start_debug_chrome.bat 启动Chrome调试模式")
                return

            version_info = response.json()
            ws_url = version_info.get('webSocketDebuggerUrl')
            if not ws_url:
                print("❌ 无法获取WebSocket URL")
                return

            print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")

        except Exception as e:
            print(f"❌ Chrome调试接口连接失败: {e}")
            print("💡 请确保Chrome调试模式正在运行")
            return

        # 使用Playwright连接到Chrome
        async with async_playwright() as p:
            print(f"🔌 连接到WebSocket: {ws_url}")
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")

            # 获取所有标签页并查找1688页面
            page = None
            all_pages = []
            for context in browser.contexts:
                for p_page in context.pages:
                    all_pages.append(p_page)

            print(f"📄 找到 {len(all_pages)} 个标签页")

            # 优先查找订单页面
            order_page_patterns = [
                'trade-order-list',
                'buyer-order-list',
                'order',
                'trade'
            ]

            for p_page in all_pages:
                url = p_page.url
                title = await p_page.title()

                # 优先选择订单相关页面
                if '1688.com' in url and any(pattern in url for pattern in order_page_patterns):
                    page = p_page
                    print(f"✅ 选择订单页面: {title}")
                    break

            # 如果没找到订单页面，选择任意1688页面
            if not page:
                for p_page in all_pages:
                    if '1688.com' in p_page.url:
                        page = p_page
                        print(f"✅ 选择1688页面")
                        break

            if not page:
                print("❌ 未找到1688页面")
                print("💡 请在Chrome中打开1688网站并登录")
                await browser.close()
                return

            # 检查当前页面是否为订单页面
            current_url = page.url
            if not any(pattern in current_url for pattern in order_page_patterns):
                print(f"⚠️ 当前页面不是订单页面")
                print(f"💡 请手动在Chrome中导航到订单页面:")
                print(f"   https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html")
                print(f"   然后重新运行脚本")
                await browser.close()
                return

            # 提取订单数据（以商品SKU为单位）
            print(f"🚀 开始提取商品SKU数据...")
            products = await extractor.extract_orders_from_page(page, args.url)
            extractor.stats['total_orders'] = len(products)

            # 保存数据
            if products:
                await extractor.save_to_excel(products, output_path_str)
                print(f"💾 商品SKU数据已保存到: {output_path_str}")
            else:
                print("⚠️ 未提取到商品SKU数据")

            # 打印统计
            extractor.print_stats()

            # 关闭连接
            await browser.close()

            print("\n🎉 商品SKU数据抓取完成!")
            print(f"📄 输出文件: {output_path_str}")
            print("💡 数据已按商品SKU为单位提取，每个商品都有独立的记录")

    except (ValueError, TypeError, KeyError) as e:
        print(f"❌ 抓取失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())