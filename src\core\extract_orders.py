#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688订单数据抓取工具
支持从1688订单页面抓取订单数据，包括接口法和DOM法
"""

import asyncio
import time
import pandas as pd
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright
import re
import sys
import requests
from pathlib import Path

# Windows下设置UTF-8输出
if sys.platform.startswith('win'):
    import codecs
    try:
        if hasattr(sys.stdout, 'detach'):
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        else:
            # 如果detach方法不可用，使用更安全的方法
            import os
            os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        # 如果所有方法都失败，忽略错误
        pass

# 定义项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class OrderDataExtractor:
    """订单数据提取器"""

    def __init__(self):
        self.order_data = []
        self.stats = {
            'total_orders': 0,
            'api_success': 0,
            'dom_success': 0,
            'failed': 0
        }

    async def extract_orders_from_api(self, page) -> List[Dict[str, Any]]:
        """通过API接口抓取订单数据"""
        orders_data = []

        def handle_response(response):
            """处理网络响应"""
            url = response.url
            # 检查是否是订单相关的API请求
            if (any(keyword in url for keyword in ['/order', '/trade', '/buyer', '/list']) and
                    response.request.resource_type == "xhr"):
                try:
                    data = response.json()
                    print(f"📡 捕获到订单API: {url}")

                    # 解析不同结构的订单数据
                    orders = self._parse_api_response(data)
                    if orders:
                        orders_data.extend(orders)
                        self.stats['api_success'] += len(orders)

                except (ValueError, TypeError, KeyError) as e:
                    print(f"⚠️ 解析API响应失败: {e}")

        # 监听响应
        page.on("response", handle_response)

        # 触发页面操作以产生API请求
        await self.trigger_api_requests(page)

        # 等待一段时间让API请求完成
        await page.wait_for_timeout(3000)

        return orders_data

    async def trigger_api_requests(self, page):
        """触发页面操作以产生API请求"""
        try:
            print("🔄 触发页面操作以获取订单数据...")

            # 方法1: 刷新页面
            print("   方法1: 刷新页面")
            await page.reload(wait_until="networkidle")
            await page.wait_for_timeout(2000)

            # 方法2: 尝试点击搜索或筛选按钮
            print("   方法2: 查找并点击搜索/筛选按钮")
            search_selectors = [
                'button:has-text("搜索")',
                'button:has-text("查询")',
                'button[type="submit"]',
                '.search-btn',
                '.query-btn',
                '[class*="search"]',
                '[class*="query"]'
            ]

            for selector in search_selectors:
                try:
                    button = page.locator(selector).first
                    if await button.is_visible() and await button.is_enabled():
                        print(f"   找到搜索按钮: {selector}")
                        await button.click()
                        await page.wait_for_timeout(2000)
                        break
                except Exception:
                    continue

            # 方法3: 尝试切换订单状态筛选
            print("   方法3: 尝试切换订单状态")
            status_selectors = [
                'select[name*="status"]',
                '[class*="status"] select',
                '[class*="filter"] select',
                'input[type="radio"]',
                '.tab-item',
                '[class*="tab"]'
            ]

            for selector in status_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements and len(elements) > 1:
                        print(f"   找到状态筛选: {selector}")
                        # 点击第二个选项（通常是不同的状态）
                        await elements[1].click()
                        await page.wait_for_timeout(2000)
                        # 再点击回第一个选项
                        await elements[0].click()
                        await page.wait_for_timeout(2000)
                        break
                except Exception:
                    continue

            # 方法4: 滚动页面触发懒加载
            print("   方法4: 滚动页面触发懒加载")
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(1000)
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(1000)

            # 方法5: 尝试分页操作
            print("   方法5: 尝试分页操作")
            pagination_selectors = [
                '.pagination a',
                '[class*="page"] a',
                'button:has-text("下一页")',
                'a:has-text("下一页")'
            ]

            for selector in pagination_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"   找到分页元素: {selector}")
                        # 点击第一个分页链接
                        await elements[0].click()
                        await page.wait_for_timeout(2000)
                        break
                except Exception:
                    continue

            print("   ✅ 页面操作完成")

        except Exception as e:
            print(f"⚠️ 触发API请求时出错: {e}")

    async def debug_page_state(self, page):
        """调试页面状态"""
        try:
            print("🔍 调试页面状态...")

            # 检查页面基本信息
            url = page.url
            title = await page.title()
            print(f"   📍 当前URL: {url}")
            print(f"   📄 页面标题: {title}")

            # 检查页面内容
            all_text = await page.evaluate("document.body.textContent")
            if "订单" in all_text:
                print("   ✅ 页面包含'订单'关键字")
            else:
                print("   ⚠️ 页面不包含'订单'关键字")

            if any(keyword in all_text for keyword in ["暂无", "没有", "空", "无数据"]):
                print("   ⚠️ 页面可能显示无数据状态")

            # 首先检查订单容器
            print("   📋 检查订单容器:")
            try:
                container = await page.query_selector('.order-list-container')
                if container:
                    print("      ✅ .order-list-container: 找到订单容器")
                    container_text = await container.text_content()
                    if container_text:
                        print(f"         容器内容长度: {len(container_text)} 字符")
                        if "订单" in container_text:
                            print("         ✅ 容器包含'订单'关键字")
                        if any(keyword in container_text for keyword in ["￥", "¥", "元"]):
                            print("         ✅ 容器包含价格信息")
                else:
                    print("      ❌ .order-list-container: 未找到")

                    # 检查备用容器
                    backup_containers = ['.order-list', '[class*="order-list"]', '[class*="order"][class*="container"]']
                    for backup in backup_containers:
                        backup_elem = await page.query_selector(backup)
                        if backup_elem:
                            print(f"      ✅ 找到备用容器: {backup}")
                            break
            except Exception as e:
                print(f"      ⚠️ 检查容器失败: {e}")

            # 检查容器内的订单元素 - 基于实际HTML结构（支持Shadow DOM）
            # 注意：由于订单在Shadow DOM中，普通选择器可能找不到，主要依靠JavaScript穿透
            selectors_to_check = [
                'order-item',                                            # 普通order-item标签
                'order-item[data-tracker]',                              # 普通带data-tracker的order-item
                '[data-tracker*="order"]',                               # 普通包含order的data-tracker
                '.order-list-container',                                 # 订单容器本身
                '.order-item',                                           # 传统CSS类（备用）
                '[class*="order-item"]',
                '[class*="order"][class*="item"]'
            ]

            print("   📋 检查订单元素:")
            print("      💡 注意：订单可能在Shadow DOM中，普通选择器可能找不到")
            for selector in selectors_to_check:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"      ✅ {selector}: {len(elements)} 个元素")
                        # 获取第一个元素的文本预览
                        if len(elements) > 0:
                            text = await elements[0].text_content()
                            if text and text.strip():
                                preview = text.strip()[:50].replace('\n', ' ')
                                print(f"         预览: {preview}...")
                    else:
                        print(f"      ❌ {selector}: 0 个元素")
                except Exception as e:
                    print(f"      ⚠️ {selector}: 检查失败 - {e}")

            print("      🔍 将使用JavaScript Shadow DOM穿透来查找订单")

        except Exception as e:
            print(f"⚠️ 调试页面状态失败: {e}")

    async def debug_pagination_container(self, page):
        """调试翻页容器"""
        try:
            print("🔍 调试翻页容器...")

            # 检查翻页容器
            page_container = await page.query_selector('.page#page, div.page#page')
            if page_container:
                print("   ✅ 找到翻页容器: .page#page")

                # 获取容器内容
                container_text = await page_container.text_content()
                print(f"   📏 容器内容: {container_text.strip()}")

                # 检查容器内的所有链接和按钮
                links_and_buttons = await page.evaluate("""
                    () => {
                        const container = document.querySelector('.page#page') || document.querySelector('div.page#page');
                        if (!container) return [];

                        const elements = container.querySelectorAll('a, button, span, div');
                        return Array.from(elements).map((el, index) => ({
                            index: index,
                            tagName: el.tagName,
                            className: el.className,
                            text: el.textContent.trim(),
                            href: el.href || '',
                            title: el.title || '',
                            disabled: el.disabled || false,
                            isVisible: el.offsetParent !== null
                        }));
                    }
                """)

                print(f"   📊 容器内元素: {len(links_and_buttons)} 个")
                for elem in links_and_buttons:
                    if elem['text']:
                        status = "可见" if elem['isVisible'] else "隐藏"
                        disabled = "禁用" if elem['disabled'] else "启用"
                        print(f"      {elem['tagName']}: '{elem['text']}' ({status}, {disabled})")
                        if elem['href']:
                            print(f"         链接: {elem['href']}")

                # 专门查找下一页相关的元素
                next_page_elements = [elem for elem in links_and_buttons
                                    if any(keyword in elem['text'] for keyword in ['下一页', '下页', 'next', '>', '»'])]

                if next_page_elements:
                    print("   🎯 找到下一页相关元素:")
                    for elem in next_page_elements:
                        print(f"      {elem['tagName']}: '{elem['text']}' (可点击: {elem['isVisible'] and not elem['disabled']})")
                else:
                    print("   ⚠️ 未找到下一页相关元素")

            else:
                print("   ❌ 未找到翻页容器")

                # 检查是否有其他分页元素
                other_pagination = await page.query_selector_all('.pagination, [class*="page"], [class*="pager"]')
                if other_pagination:
                    print(f"   📋 找到其他分页元素: {len(other_pagination)} 个")
                    for i, elem in enumerate(other_pagination[:3]):
                        elem_class = await elem.get_attribute('class')
                        elem_text = await elem.text_content()
                        print(f"      分页元素 {i+1}: .{elem_class} - {elem_text[:50]}...")

        except Exception as e:
            print(f"⚠️ 调试翻页容器失败: {e}")

    def _parse_api_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析API响应数据"""
        if not data or not isinstance(data, dict):
            print(f"⚠️ 无效的API响应数据: "
                  f"{type(data)}")
            return []

        orders = []

        # 常见的订单数据结构路径
        possible_paths = [
            ['data', 'result', 'list'],
            ['data', 'list'],
            ['result', 'data'],
            ['data', 'orderList'],
            ['orderList'],
            ['list']
        ]

        def find_nested_value(obj, path):
            """查找嵌套值"""
            try:
                current = obj
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        return None
                return current
            except ValueError as e:
                print(f"⚠️ 查找嵌套值失败: {e}")
                return None

        for path in possible_paths:
            try:
                order_list = find_nested_value(data, path)
                if order_list and isinstance(order_list, list):
                    print(f"✅ 找到订单列表，路径: {' -> '.join(path)}, "
                          f"数量: {len(order_list)}")

                    for i, item in enumerate(order_list):
                        if not isinstance(item, dict):
                            print(f"⚠️ 订单项 {i} 不是字典类型: "
                                  f"{type(item)}")
                            continue

                        order = self._extract_order_fields(item)
                        if order:
                            orders.append(order)
                        else:
                            print(f"⚠️ 订单项 {i} 提取失败")

                    break
                elif order_list is not None:
                    print(f"⚠️ 路径 {' -> '.join(path)} 存在但不是列表: "
                          f"{type(order_list)}")
            except (ValueError, TypeError) as e:
                print(f"⚠️ 处理路径 {' -> '.join(path)} 失败: "
                      f"{e}")
                continue

        if not orders:
            print("⚠️ 未找到有效的订单数据")
            # 输出数据结构以便调试
            data_keys = list(data.keys()) if isinstance(data, dict) else type(data)
            print(f"🔍 数据结构: {data_keys}")

        return orders

    def _extract_order_fields(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从API数据中提取订单字段"""
        try:
            # 输入验证
            if not item or not isinstance(item, dict):
                print(f"⚠️ 无效的输入数据类型: {type(item)}")
                return None

            if not item:
                print("⚠️ 空的输入数据")
                return None

            # 常见的字段映射
            field_mapping = {
                'order_id': ['orderId', 'id', 'orderNo', 'orderCode'],
                'title': ['title', 'subject', 'productName', 'name', 'goodsName'],
                'price': ['price', 'amount', 'unitPrice', 'money'],
                'quantity': ['quantity', 'num', 'count', 'number'],
                'total_amount': ['totalAmount', 'totalMoney', 'sumMoney'],
                'shop_name': ['shopName', 'sellerName', 'companyName', 'storeName'],
                'order_time': ['createTime', 'orderTime', 'gmtCreate', 'time'],
                'status': ['status', 'orderStatus', 'state', 'tradeStatus'],
                'image_url': ['picUrl', 'imageUrl', 'imgUrl', 'imagePath'],
                'detail_url': ['detailUrl', 'url', 'link', 'href']
            }

            order = {}
            missing_fields = []

            for target_field, source_fields in field_mapping.items():
                found = False
                for source_field in source_fields:
                    if source_field in item and item[source_field]:
                        order[target_field] = item[source_field]
                        found = True
                        break

                if not found:
                    order[target_field] = ""
                    if target_field in ['title', 'order_id']:
                        missing_fields.append(target_field)

            # 标准化价格字段
            for price_field in ['price', 'total_amount']:
                if price_field in order:
                    original_value = order[price_field]
                    if isinstance(original_value, (int, float)):
                        order[price_field] = float(original_value)
                    elif isinstance(original_value, str):
                        # 移除货币符号和逗号
                        price_str = re.sub(r'[^\d.]', '', str(original_value))
                        order[price_field] = float(price_str) if price_str else 0.0
                    else:
                        order[price_field] = 0.0
                        print(f"⚠️ 价格字段类型异常: {price_field} = "
                              f"{original_value} ({type(original_value)})")

            # 标准化数量字段
            if 'quantity' in order:
                quantity_value = order['quantity']
                if isinstance(quantity_value, (int, float)):
                    order['quantity'] = int(quantity_value)
                elif isinstance(quantity_value, str):
                    try:
                        order['quantity'] = int(float(quantity_value))
                    except (ValueError, TypeError):
                        order['quantity'] = 0
                        print(f"⚠️ 数量字段转换失败: {quantity_value}")
                else:
                    order['quantity'] = 0

            # 验证必需字段
            if not order.get('title'):
                print("⚠️ 缺少必需字段: title")
                return None

            # 验证数据合理性
            if order.get('price', 0) < 0:
                print(f"⚠️ 价格不能为负数: {order.get('price')}")
                order['price'] = 0

            if order.get('quantity', 0) < 0:
                print(f"⚠️ 数量不能为负数: {order.get('quantity')}")
                order['quantity'] = 0

            if order.get('total_amount', 0) < 0:
                print(f"⚠️ 总金额不能为负数: {order.get('total_amount')}")
                order['total_amount'] = 0

            # 计算总金额验证
            if order.get('price') and order.get('quantity'):
                calculated_total = order['price'] * order['quantity']
                if abs(calculated_total - order.get('total_amount', 0)) > 0.01:
                    print(f"⚠️ 总金额计算不一致: 计算值={calculated_total}, "
                          f"实际值={order.get('total_amount')}")
                    # 以计算值为准
                    order['total_amount'] = calculated_total

            return order

        except (ValueError, TypeError, KeyError) as e:
            print(f"⚠️ 提取订单字段失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    async def extract_orders_from_dom(self, page) -> List[Dict[str, Any]]:
        """通过DOM解析抓取订单数据"""
        try:
            # 先检查页面状态
            await self.debug_page_state(page)

            # 使用JavaScript提取订单数据
            js_code = """
            () => {
                const toText = el => (el?.textContent || "").trim();
                const toNumber = str => (
                    parseFloat(String(str).replace(/[^\\d.]/g, "")) || 0
                );

                // 调试信息
                console.log("🔍 开始DOM解析（支持Shadow DOM）...");

                // Shadow DOM 穿透函数
                function querySelectorDeep(selector, root = document) {
                    const elements = [];

                    // 在当前层级查找
                    const found = root.querySelectorAll(selector);
                    elements.push(...found);

                    // 递归查找所有 Shadow DOM
                    const allElements = root.querySelectorAll('*');
                    for (const el of allElements) {
                        if (el.shadowRoot) {
                            console.log("发现Shadow DOM:", el.tagName);
                            const shadowElements = querySelectorDeep(selector, el.shadowRoot);
                            elements.push(...shadowElements);
                        }
                    }

                    return elements;
                }

                // 首先查找订单列表容器（可能在Shadow DOM中）
                let orderContainer = document.querySelector('.order-list-container');
                console.log("普通查找订单容器:", orderContainer ? "✅ 找到" : "❌ 未找到");

                if (!orderContainer) {
                    console.log("🔍 在Shadow DOM中查找订单容器...");
                    const shadowContainers = querySelectorDeep('.order-list-container');
                    if (shadowContainers.length > 0) {
                        orderContainer = shadowContainers[0];
                        console.log("✅ 在Shadow DOM中找到订单容器");
                    }
                }

                if (!orderContainer) {
                    console.log("⚠️ 未找到 .order-list-container，尝试其他容器...");
                    // 备用容器选择器
                    const containerSelectors = [
                        '.order-list',
                        '[class*="order-list"]',
                        '[class*="order"][class*="container"]',
                        '[class*="trade-list"]',
                        '.list-container'
                    ];

                    for (const selector of containerSelectors) {
                        let container = document.querySelector(selector);
                        if (!container) {
                            const shadowContainers = querySelectorDeep(selector);
                            if (shadowContainers.length > 0) {
                                container = shadowContainers[0];
                                console.log(`✅ 在Shadow DOM中找到备用容器: ${selector}`);
                                break;
                            }
                        } else {
                            console.log(`✅ 找到备用容器: ${selector}`);
                            break;
                        }
                    }
                }

                // 在容器内查找订单项（支持Shadow DOM）
                console.log("🔍 查找订单项（支持Shadow DOM）...");

                // 针对订单容器内的选择器 - 基于实际HTML结构
                const selectors = [
                    'order-item',                                    // 直接的order-item标签
                    'order-item[data-tracker]',                      // 带data-tracker的order-item
                    '[data-tracker*="order"]',                       // 任何包含order的data-tracker
                    '.order-item',                                   // 传统的CSS类选择器（备用）
                    '[class*="order-item"]',
                    '[class*="order"][class*="item"]'
                ];

                let items = [];
                let usedSelector = '';

                // 调试每个选择器（包括Shadow DOM）
                for (const selector of selectors) {
                    // 先尝试普通查找
                    let found = Array.from(document.querySelectorAll(selector));
                    console.log(`普通查找 ${selector}: 找到 ${found.length} 个元素`);

                    // 如果普通查找没找到，尝试Shadow DOM穿透
                    if (found.length === 0) {
                        found = querySelectorDeep(selector);
                        console.log(`Shadow DOM查找 ${selector}: 找到 ${found.length} 个元素`);
                    }

                    if (found.length > 0) {
                        items = found;
                        usedSelector = selector;
                        console.log(`✅ 使用选择器: ${selector} (找到 ${found.length} 个元素)`);
                        break;
                    }
                }

                if (items.length === 0 && orderContainer) {
                    console.log("⚠️ 在订单容器内未找到标准订单元素，尝试分析容器结构...");

                    // 分析容器内的直接子元素
                    const directChildren = Array.from(orderContainer.children);
                    console.log(`订单容器直接子元素数量: ${directChildren.length}`);

                    if (directChildren.length > 0) {
                        // 尝试找到包含订单信息的子元素
                        const possibleItems = directChildren.filter(el => {
                            const text = toText(el);
                            const hasPrice = text.includes('￥') || text.includes('¥') || text.includes('元');
                            const hasNumbers = /\\d+/.test(text);
                            const hasReasonableLength = text.length > 20 && text.length < 2000;
                            return hasPrice && hasNumbers && hasReasonableLength;
                        });

                        console.log(`找到 ${possibleItems.length} 个可能的订单元素`);
                        items = possibleItems;
                    }
                }

                return items.map((item, index) => {
                    console.log(`处理第 ${index + 1} 个订单项...`);

                    // 检查是否有Shadow DOM
                    const shadowRoot = item.shadowRoot;
                    if (!shadowRoot) {
                        console.log(`订单 ${index + 1} 没有Shadow DOM，使用普通方法`);
                        // 使用原来的方法处理
                        return null;
                    }

                    console.log(`订单 ${index + 1} 有Shadow DOM，提取详细信息...`);

                    // 调试：输出Shadow DOM的结构
                    console.log(`  Shadow DOM内容预览: ${shadowRoot.innerHTML.substring(0, 200)}...`);

                    // 查找所有可能的类名
                    const allElements = shadowRoot.querySelectorAll('*');
                    const classNames = new Set();
                    allElements.forEach(el => {
                        if (el.className) {
                            el.className.split(' ').forEach(cls => {
                                if (cls.trim()) classNames.add(cls.trim());
                            });
                        }
                    });
                    console.log(`  发现的类名: ${Array.from(classNames).slice(0, 10).join(', ')}...`);

                    // 从Shadow DOM中提取订单号
                    let orderId = "";

                    // 方法1: 从data-tracker属性中提取
                    const orderItemEl = shadowRoot.querySelector('order-item[data-tracker]');
                    if (orderItemEl) {
                        const tracker = orderItemEl.getAttribute('data-tracker');
                        if (tracker) {
                            orderId = tracker;
                            console.log(`  订单号(tracker): ${orderId}`);
                        }
                    }

                    // 方法2: 从文本中提取纯数字订单号
                    if (!orderId) {
                        const orderIdEl = shadowRoot.querySelector('.order-id, [class*="order-id"], [class*="orderId"]');
                        if (orderIdEl) {
                            const rawText = toText(orderIdEl);
                            // 提取纯数字订单号
                            const orderMatch = rawText.match(/\\d{15,}/);
                            if (orderMatch) {
                                orderId = orderMatch[0];
                                console.log(`  订单号(文本提取): ${orderId}`);
                            } else {
                                orderId = rawText.trim();
                                console.log(`  订单号(原始): ${orderId}`);
                            }
                        }
                    }

                    // 从Shadow DOM中提取订单时间
                    let orderTime = "";
                    const orderTimeEl = shadowRoot.querySelector('.order-time');
                    if (orderTimeEl) {
                        orderTime = toText(orderTimeEl);
                        console.log(`  订单时间: ${orderTime}`);
                    }

                    // 从Shadow DOM中提取下单账号
                    let buyerAccount = "";
                    const buyerAccountEl = shadowRoot.querySelector('.buyer-account');
                    if (buyerAccountEl) {
                        buyerAccount = toText(buyerAccountEl);
                        console.log(`  下单账号: ${buyerAccount}`);
                    }

                    // 提取商品信息 - 从Shadow DOM中的具体元素
                    let title = "";
                    let productCount = "";
                    let productSpecs = "";

                    // 提取商品名称 - 从order-item-entry-product中查找
                    const productEntryEl = shadowRoot.querySelector('order-item-entry-product');
                    if (productEntryEl && productEntryEl.shadowRoot) {
                        // 在商品条目的Shadow DOM中查找商品名称
                        const productNameEl = productEntryEl.shadowRoot.querySelector('.product-name, .title, .name');
                        if (productNameEl) {
                            title = toText(productNameEl);
                            console.log(`  商品名称: ${title}`);
                        }
                    }

                    // 如果没找到，尝试其他选择器
                    if (!title) {
                        const productNameEl = shadowRoot.querySelector('.product-name, .title, .name');
                        if (productNameEl) {
                            title = toText(productNameEl);
                            console.log(`  商品名称(备用): ${title}`);
                        }
                    }

                    // 提取商品规格信息（颜色等）
                    const skuInfoItems = shadowRoot.querySelectorAll('.sku-info-item');
                    if (skuInfoItems.length > 0) {
                        const specs = [];
                        skuInfoItems.forEach(item => {
                            const specText = toText(item);
                            if (specText && specText.length > 0) {
                                specs.push(specText);
                            }
                        });
                        productSpecs = specs.join(', ');
                        console.log(`  商品规格: ${productSpecs}`);
                    }

                    // 提取订单商品数量信息
                    const contentEl = shadowRoot.querySelector('.order-item-content');
                    if (contentEl) {
                        const contentText = toText(contentEl);
                        if (contentText.includes('共') && contentText.includes('种货品')) {
                            productCount = contentText;
                            console.log(`  商品数量信息: ${contentText}`);
                        }
                    }

                    // 如果没有找到商品名称，使用订单号作为标题
                    if (!title && orderId) {
                        title = `订单 ${orderId}`;
                    }

                    // 如果有规格信息，添加到标题中
                    if (title && productSpecs) {
                        title = `${title} (${productSpecs})`;
                    }

                    // 提取价格 - 从Shadow DOM中查找
                    let price = 0;
                    let totalAmount = 0;

                    if (shadowRoot) {
                        // 优先从具体的价格类名中查找
                        const actualPriceEl = shadowRoot.querySelector('.actual-unit-price');
                        if (actualPriceEl) {
                            const priceText = toText(actualPriceEl);
                            if (priceText) {
                                price = toNumber(priceText);
                                console.log(`  找到单价: ${priceText} -> ${price}`);
                            }
                        }

                        // 如果没找到，使用通用选择器
                        if (price === 0) {
                            const priceSelectors = [
                                '.price, .amount, .money, .unit-price, .total-price',
                                '[class*="price"], [class*="amount"], [class*="money"]',
                                '[class*="cost"], [class*="fee"], [class*="total"]'
                            ];

                            for (const selector of priceSelectors) {
                                const elements = shadowRoot.querySelectorAll(selector);
                                for (const el of elements) {
                                    const priceText = toText(el);
                                    if (priceText && (priceText.includes('￥') || priceText.includes('¥') || priceText.includes('元'))) {
                                        const extractedPrice = toNumber(priceText);
                                        if (extractedPrice > 0) {
                                            if (priceText.includes('总') || priceText.includes('合计') || priceText.includes('total')) {
                                                totalAmount = extractedPrice;
                                            } else if (price === 0) {
                                                price = extractedPrice;
                                            }
                                            console.log(`  找到价格: ${priceText} -> ${extractedPrice}`);
                                        }
                                    }
                                }
                            }
                        }

                        // 如果没找到价格，尝试从整个Shadow DOM文本中提取
                        if (price === 0 && totalAmount === 0) {
                            const shadowText = shadowRoot.textContent || '';
                            const priceMatches = shadowText.match(/[￥¥]([\\d,]+\\.?\\d*)|([\\d,]+\\.?\\d*)元/g);
                            if (priceMatches) {
                                for (const match of priceMatches) {
                                    const extractedPrice = toNumber(match);
                                    if (extractedPrice > 0) {
                                        price = extractedPrice;
                                        console.log(`  从文本提取价格: ${match} -> ${extractedPrice}`);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // 提取数量 - 优先从quantity-amount类中获取
                    let quantity = 1;

                    if (shadowRoot) {
                        const quantityEl = shadowRoot.querySelector('.quantity-amount');
                        if (quantityEl) {
                            const quantityText = toText(quantityEl);
                            if (quantityText) {
                                const qty = parseInt(quantityText);
                                if (qty > 0) {
                                    quantity = qty;
                                    console.log(`  找到数量: ${quantityText} -> ${quantity}`);
                                }
                            }
                        }
                    }

                    // 如果没找到，从商品信息中解析
                    if (quantity === 1 && productCount) {
                        // 从 "共4种货品" 这样的文本中提取数量
                        const qtyMatch = productCount.match(/共(\\d+)种/);
                        if (qtyMatch) {
                            quantity = parseInt(qtyMatch[1]) || 1;
                            console.log(`  从商品信息提取数量: ${quantity}`);
                        }
                    }

                    // 提取店铺名 - 从Shadow DOM中查找
                    let shopName = "";
                    if (shadowRoot) {
                        const shopSelectors = [
                            '.shop-name, .seller-name, .store-name',
                            '[class*="shop"], [class*="seller"], [class*="store"]'
                        ];
                        for (const selector of shopSelectors) {
                            const el = shadowRoot.querySelector(selector);
                            if (el && toText(el)) {
                                shopName = toText(el);
                                console.log(`  店铺名: ${shopName}`);
                                break;
                            }
                        }
                    }

                    // 提取图片 - 从Shadow DOM中查找
                    let imageUrl = "";
                    if (shadowRoot) {
                        const imgSelectors = [
                            'img[src*="img"], img[src*="photo"], img[src*="pic"]',
                            '.product-img img, .goods-img img, .item-img img'
                        ];
                        for (const selector of imgSelectors) {
                            const el = shadowRoot.querySelector(selector);
                            if (el && el.src) {
                                imageUrl = el.src;
                                console.log(`  图片URL: ${imageUrl}`);
                                break;
                            }
                        }
                    }

                    // 计算总金额
                    const calculatedTotal = price * quantity;
                    const finalTotal = totalAmount > 0 ? totalAmount : calculatedTotal;

                    const result = {
                        title: title,
                        price: price,
                        quantity: quantity,
                        shop_name: shopName,
                        order_time: orderTime,
                        order_id: orderId,
                        buyer_account: buyerAccount,
                        product_count: productCount,
                        product_specs: productSpecs,
                        image_url: imageUrl,
                        total_amount: finalTotal,
                        detail_url: "",
                        status: ""
                    };

                    console.log(`订单 ${index + 1} 提取结果:`, {
                        title: result.title,
                        orderId: result.order_id,
                        price: result.price,
                        quantity: result.quantity,
                        totalAmount: result.total_amount
                    });

                    return result;
                }).filter(item => {
                    // 过滤条件：必须有订单号或标题，并且不是null
                    if (item === null) {
                        return false;
                    }

                    const hasOrderId = item.order_id && item.order_id.length > 0;
                    const hasTitle = item.title && item.title.length > 0;
                    const hasValidData = hasOrderId || hasTitle;

                    if (!hasValidData) {
                        console.log('过滤掉无效订单:', {
                            title: item.title,
                            orderId: item.order_id,
                            price: item.price
                        });
                    }

                    return hasValidData;
                });

                console.log(`🎉 DOM解析完成，找到 ${result.length} 个有效订单`);
                return result;
            }
            """

            # 执行JavaScript提取数据
            orders_data = await page.evaluate(js_code)
            self.stats['dom_success'] = len(orders_data)

            print(f"🔍 DOM解析成功，提取到 {len(orders_data)} 个订单")
            return orders_data

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ DOM解析失败: {e}")
            return []

    async def extract_orders_from_page(
            self,
            page,
            url: str
    ) -> List[Dict[str, Any]]:
        """从订单页面提取订单数据"""
        print(f"🌐 目标订单页面: {url}")

        all_orders = []

        try:
            # 隐藏水印
            await page.add_style_tag(
                content=".quark-watermark{display:none!important}"
            )

            # 检查当前页面是否已经是订单页面
            current_url = page.url
            print(f"📍 当前页面: {current_url}")

            # 如果不是订单页面，则导航到订单页面
            if 'trade-order-list' not in current_url and 'buyer_order_list' not in current_url:
                print("🔄 导航到订单页面...")
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        print(f"🔄 尝试导航 (第 {attempt + 1}/{max_retries} 次)...")
                        await page.goto(
                            url, timeout=30000, wait_until="domcontentloaded"
                        )

                        # 等待页面加载
                        await page.wait_for_load_state(
                            "networkidle", timeout=30000
                        )
                        await page.wait_for_timeout(2000)

                        # 检查页面是否加载成功
                        page_title = await page.title()
                        if page_title:
                            print(f"📄 页面标题: {page_title}")
                            break
                        else:
                            print("⚠️ 页面标题为空，重试...")
                            if attempt < max_retries - 1:
                                await page.wait_for_timeout(3000)
                                continue
                    except Exception as e:
                        print(f"⚠️ 导航失败 (第 {attempt + 1} 次): {e}")
                        if attempt < max_retries - 1:
                            await page.wait_for_timeout(3000)
                            continue
                        else:
                            raise
            else:
                print("✅ 已在订单页面，无需导航")

            # 根据参数决定是否进行多页抓取
            if hasattr(self, 'enable_all_pages') and self.enable_all_pages:
                all_orders = await self.extract_all_pages_orders(page)
            else:
                # 单页抓取（原有逻辑）
                all_orders = await self.extract_single_page_orders(page)

            return all_orders

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ 提取订单数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def extract_single_page_orders(self, page) -> List[Dict[str, Any]]:
        """提取单页订单数据（原有逻辑）"""
        # 监听API响应
        api_orders = await self.extract_orders_from_api(page)

        # 滚动触发懒加载
        print("📜 滚动页面触发懒加载...")
        await page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await page.wait_for_timeout(1000)

        # 再次向上滚动，确保所有数据加载
        await page.evaluate("window.scrollTo(0, 0)")
        await page.wait_for_timeout(500)
        await page.evaluate(
            "window.scrollTo(0, document.body.scrollHeight)"
        )
        await page.wait_for_timeout(1000)

        # 如果API没有获取到数据，使用DOM解析
        if not api_orders:
            print("🔍 API未获取到数据，使用DOM解析...")
            dom_orders = await self.extract_orders_from_dom(page)

            if dom_orders:
                print(f"✅ DOM解析成功，提取到 {len(dom_orders)} 个订单")
                self.stats['dom_success'] = len(dom_orders)
                return dom_orders
            else:
                print("⚠️ DOM解析也失败")
                return []
        else:
            print(f"✅ API解析成功，提取到 {len(api_orders)} 个订单")
            self.stats['api_success'] = len(api_orders)
            return api_orders

    async def extract_all_pages_orders(self, page) -> List[Dict[str, Any]]:
        """提取所有页面的订单数据"""
        all_orders = []
        current_page = 1
        max_pages = getattr(self, 'max_pages', 10)  # 使用参数设置的最大页数

        print(f"🔄 开始多页数据抓取...")

        while current_page <= max_pages:
            print(f"\n📄 正在处理第 {current_page} 页...")

            # 监听API响应
            api_orders = await self.extract_orders_from_api(page)

            # 滚动触发懒加载
            print("📜 滚动页面触发懒加载...")
            await page.evaluate(
                "window.scrollTo(0, document.body.scrollHeight)"
            )
            await page.wait_for_timeout(1000)

            # 再次向上滚动，确保所有数据加载
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(500)
            await page.evaluate(
                "window.scrollTo(0, document.body.scrollHeight)"
            )
            await page.wait_for_timeout(1000)

            # 提取当前页数据
            page_orders = []
            if api_orders:
                page_orders = api_orders
                print(f"✅ API解析成功，提取到 {len(page_orders)} 个订单")
                self.stats['api_success'] += len(page_orders)
            else:
                print("🔍 API未获取到数据，使用DOM解析...")
                page_orders = await self.extract_orders_from_dom(page)
                if page_orders:
                    print(f"✅ DOM解析成功，提取到 {len(page_orders)} 个订单")
                    self.stats['dom_success'] += len(page_orders)
                else:
                    print("⚠️ 当前页面未提取到数据")

            # 如果当前页没有数据，可能已到最后一页
            if not page_orders:
                print(f"📄 第 {current_page} 页无数据，可能已到最后一页")
                break

            all_orders.extend(page_orders)
            print(f"📊 累计提取 {len(all_orders)} 个订单")

            # 调试翻页容器（仅在第一页时调试）
            if current_page == 1:
                await self.debug_pagination_container(page)

            # 尝试翻到下一页
            has_next_page = await self.go_to_next_page(page)
            if not has_next_page:
                print("📄 已到最后一页，抓取完成")
                break

            current_page += 1

            # 等待页面加载
            await page.wait_for_timeout(2000)

        print(f"🎉 多页抓取完成，总共提取 {len(all_orders)} 个订单")
        return all_orders

    async def go_to_next_page(self, page) -> bool:
        """尝试翻到下一页"""
        try:
            print("🔄 尝试翻到下一页...")

            # 首先检查翻页容器
            page_container = await page.query_selector('.page#page, div.page#page')
            if page_container:
                print("✅ 找到翻页容器: .page#page")

                # 在翻页容器内查找下一页按钮
                container_selectors = [
                    '.page#page a[title="下一页"]',
                    '.page#page a:has-text("下一页")',
                    '.page#page button:has-text("下一页")',
                    '.page#page .next',
                    '.page#page [class*="next"]',
                    '.page#page a:last-child'
                ]

                print("   在翻页容器内查找下一页按钮...")
                for selector in container_selectors:
                    try:
                        next_button = page.locator(selector).first
                        if await next_button.is_visible() and await next_button.is_enabled():
                            print(f"   ✅ 找到下一页按钮: {selector}")
                            await next_button.click()
                            await page.wait_for_load_state("networkidle", timeout=10000)
                            print("   ✅ 翻页成功")
                            return True
                    except Exception as e:
                        print(f"   ⚠️ 按钮 {selector} 点击失败: {e}")
                        continue
            else:
                print("⚠️ 未找到翻页容器，尝试全局查找...")

            # 备用方案：全局查找下一页按钮
            next_page_selectors = [
                'a[title="下一页"]',
                'a:has-text("下一页")',
                'button:has-text("下一页")',
                '.pagination .next',
                '.page-next',
                '[class*="next"]:has-text("下一页")',
                '[class*="pagination"] a:last-child',
                '.ant-pagination-next',
                '.el-pagination__next'
            ]

            for selector in next_page_selectors:
                try:
                    next_button = page.locator(selector).first
                    if await next_button.is_visible() and await next_button.is_enabled():
                        print(f"🔄 找到下一页按钮: {selector}")
                        await next_button.click()
                        await page.wait_for_load_state("networkidle", timeout=10000)
                        return True
                except Exception:
                    continue

            # 如果没找到下一页按钮，尝试通过URL参数翻页
            current_url = page.url
            if 'page=' in current_url:
                import re
                # 提取当前页码
                page_match = re.search(r'page=(\d+)', current_url)
                if page_match:
                    current_page_num = int(page_match.group(1))
                    next_page_num = current_page_num + 1
                    next_url = re.sub(r'page=\d+', f'page={next_page_num}', current_url)
                    print(f"🔄 通过URL翻页: page={next_page_num}")
                    await page.goto(next_url, wait_until="networkidle")
                    return True

            print("⚠️ 未找到下一页按钮或已到最后一页")
            return False

        except Exception as e:
            print(f"⚠️ 翻页失败: {e}")
            return False

    async def save_to_excel(
            self,
            orders: List[Dict[str, Any]],
            output_path: str
    ):
        """保存订单数据到Excel"""
        try:
            if not orders:
                print("⚠️ 没有订单数据可保存")
                return

            # 数据验证
            valid_orders = []
            for i, order in enumerate(orders):
                if not isinstance(order, dict):
                    print(f"⚠️ 订单 {i} 不是字典类型，跳过")
                    continue

                if not order.get('title'):
                    print(f"⚠️ 订单 {i} 缺少标题，跳过")
                    continue

                valid_orders.append(order)

            if not valid_orders:
                print("⚠️ 没有有效的订单数据可保存")
                return

            print(f"📊 有效订单数量: {len(valid_orders)}/{len(orders)}")

            df = pd.DataFrame(valid_orders)

            # 重命名列为中文
            column_mapping = {
                'order_id': '订单号',
                'title': '商品名称',
                'price': '单价',
                'quantity': '数量',
                'total_amount': '总金额',
                'shop_name': '店铺名称',
                'order_time': '下单时间',
                'status': '订单状态',
                'image_url': '商品图片',
                'detail_url': '详情链接'
            }

            # 确保所有列都存在
            for target_col in column_mapping.values():
                if target_col not in df.columns:
                    df[target_col] = ""

            df = df.rename(columns=column_mapping)

            # 创建输出目录
            import os
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存到Excel
            df.to_excel(output_path, index=False)
            print(f"✅ 订单数据已保存到: {output_path}")
            print(f"📊 共 {len(valid_orders)} 个订单")

            # 显示数据统计
            print("\n📋 数据统计:")
            print(f"  订单号: {df['订单号'].count()} 个")
            print(f"  店铺数量: {df['店铺名称'].nunique()} 个")
            try:
                total_sum = float(df['总金额'].sum())
                if total_sum != 0:
                    print(f"  总金额: {total_sum:.2f} 元")
                else:
                    print("  总金额: 0.00 元")
            except (ValueError, TypeError):
                print("  总金额: 计算失败")

        except (ValueError, TypeError, KeyError) as e:
            print(f"❌ 保存Excel失败: {e}")
            import traceback
            traceback.print_exc()

    def print_stats(self):
        """打印统计信息"""
        print("\n📊 抓取统计:")
        print(f"  总订单数: {self.stats['total_orders']}")
        print(f"  API成功: {self.stats['api_success']}")
        print(f"  DOM成功: {self.stats['dom_success']}")
        print(f"  失败: {self.stats['failed']}")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='1688订单数据抓取工具')
    parser.add_argument(
        '--url', '-u',
        default='https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html?tradeStatus=waitbuyerreceive&spm=a260k.home2025.topmenu.dmyorder&page=1&pageSize=10',
        help='订单页面URL'
    )
    parser.add_argument('--output', '-o', help='输出Excel文件路径（默认：自动生成）')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--all-pages', action='store_true', help='抓取所有页面的订单数据')
    parser.add_argument('--max-pages', type=int, default=10, help='最大抓取页数（默认：10页）')

    args = parser.parse_args()

    # 设置输出路径
    if not args.output:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        args.output = PROJECT_ROOT / "reports" / f"1688_orders_{timestamp}.xlsx"

    print("🚀 开始1688订单数据抓取...")
    print(f"目标URL: {args.url}")
    print(f"输出文件: {args.output}")

    # 确保输出路径是字符串
    output_path_str = str(args.output)

    # 创建输出目录
    import os
    os.makedirs(os.path.dirname(output_path_str), exist_ok=True)

    extractor = OrderDataExtractor()

    # 设置多页抓取参数
    extractor.enable_all_pages = args.all_pages
    extractor.max_pages = args.max_pages

    try:
        # 连接到Chrome调试实例
        debug_port = 9222
        print("🔗 正在连接到Chrome调试实例...")

        # 检查Chrome调试接口
        try:
            version_url = f"http://localhost:{debug_port}/json/version"
            proxies = {'http': None, 'https': None}
            response = requests.get(version_url, timeout=5, proxies=proxies)
            if response.status_code != 200:
                print(f"❌ Chrome调试接口不可用，状态码: {response.status_code}")
                print("💡 请先运行 start_debug_chrome.bat 启动Chrome调试模式")
                return

            version_info = response.json()
            ws_url = version_info.get('webSocketDebuggerUrl')
            if not ws_url:
                print("❌ 无法获取WebSocket URL")
                return

            print(f"✅ Chrome调试接口正常: {version_info.get('Browser', 'Unknown')}")

        except Exception as e:
            print(f"❌ Chrome调试接口连接失败: {e}")
            print("💡 请确保Chrome调试模式正在运行")
            return

        # 使用Playwright连接到Chrome
        async with async_playwright() as p:
            print(f"🔌 连接到WebSocket: {ws_url}")
            browser = await p.chromium.connect_over_cdp(ws_url)
            print("✅ 成功连接到Chrome调试实例!")

            # 获取所有标签页并查找1688页面
            page = None
            all_pages = []
            for context in browser.contexts:
                for p_page in context.pages:
                    all_pages.append(p_page)

            print(f"📄 找到 {len(all_pages)} 个标签页:")

            # 优先查找订单页面
            order_page_patterns = [
                'trade-order-list',
                'buyer-order-list',
                'order',
                'trade'
            ]

            for p_page in all_pages:
                url = p_page.url
                title = await p_page.title()
                print(f"   - {title[:50]}... ({url})")

                # 优先选择订单相关页面
                if '1688.com' in url and any(pattern in url for pattern in order_page_patterns):
                    page = p_page
                    print(f"   ✅ 选择订单页面: {url}")
                    break

            # 如果没找到订单页面，选择任意1688页面
            if not page:
                for p_page in all_pages:
                    if '1688.com' in p_page.url:
                        page = p_page
                        print(f"   ✅ 选择1688页面: {p_page.url}")
                        break

            if not page:
                print("❌ 未找到1688页面")
                print("💡 请在Chrome中打开1688网站并登录")
                await browser.close()
                return

            # 检查当前页面是否为订单页面，如果不是则导航到订单页面
            current_url = page.url
            if not any(pattern in current_url for pattern in order_page_patterns):
                print(f"🔄 当前页面不是订单页面，正在导航到订单页面...")
                order_url = "https://air.1688.com/app/ctf-page/trade-order-list/buyer-order-list.html?tradeStatus=waitbuyerreceive&spm=a260k.home2025.topmenu.dmyorder&page=1&pageSize=10"
                print(f"   目标URL: {order_url}")

                try:
                    await page.goto(order_url, timeout=30000, wait_until="domcontentloaded")
                    await page.wait_for_timeout(3000)  # 等待页面加载
                    print(f"   ✅ 成功导航到订单页面")
                    print(f"   📄 页面标题: {await page.title()}")
                except Exception as e:
                    print(f"   ⚠️ 导航失败: {e}")
                    print(f"   💡 请手动在Chrome中导航到订单页面")

            # 提取订单数据
            print(f"🚀 开始提取订单数据...")
            orders = await extractor.extract_orders_from_page(page, args.url)
            extractor.stats['total_orders'] = len(orders)

            # 保存数据
            if orders:
                await extractor.save_to_excel(orders, output_path_str)
                print(f"💾 数据已保存到: {output_path_str}")
            else:
                print("⚠️ 未提取到订单数据")

            # 打印统计
            extractor.print_stats()

            # 关闭连接
            await browser.close()

            print("\n🎉 订单数据抓取完成!")
            print(f"📄 输出文件: {output_path_str}")

    except (ValueError, TypeError, KeyError) as e:
        print(f"❌ 抓取失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())