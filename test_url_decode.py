#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URL解码功能
"""

from urllib.parse import unquote

# 测试URL解码
test_urls = [
    "D:%5C1688_automation_project%5Creports",
    "D:%5C1688_automation_project%5Creports%5Ccart_report_20250812_170248.xlsx",
    "file:///D:%5C1688_automation_project%5Creports%5Ccart_report_20250812_170248.xlsx"
]

print("URL解码测试:")
print("=" * 50)

for url in test_urls:
    print(f"原始URL: {url}")
    
    # 移除file:///前缀
    if url.startswith('file:///'):
        clean_url = url[8:]
    else:
        clean_url = url
    
    # 解码
    decoded = unquote(clean_url)
    print(f"解码后:   {decoded}")
    print()
