#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix encoding issues in cart data
"""

import json
import os
from typing import Dict, Any


def fix_encoding_issues(data: Dict[str, Any]) -> Dict[str, Any]:
    """Fix encoding issues in cart data"""
    fixed_data = data.copy()

    # Fix product names and other text fields
    if "商品数据" in fixed_data:
        for product in fixed_data["商品数据"]:
            # Fix product name - handle encoding issues
            if "品名" in product and isinstance(product["品名"], str):
                try:
                    # Try to normalize the text
                    product["品名"] = product["品名"].strip()
                    # Remove any non-printable characters
                    product["品名"] = ''.join(
                        char for char in product["品名"] if char.isprintable()
                    )
                except Exception:
                    pass

            # Fix specifications
            if "规格" in product and isinstance(product["规格"], str):
                try:
                    product["规格"] = product["规格"].strip()
                    product["规格"] = ''.join(
                        char for char in product["规格"] if char.isprintable()
                    )
                except Exception:
                    pass

            # Fix supplier info if exists
            if "供应商" in product and isinstance(product["供应商"], str):
                try:
                    product["供应商"] = product["供应商"].strip()
                    product["供应商"] = ''.join(
                        char for char in product["供应商"] if char.isprintable()
                    )
                except Exception:
                    pass

    return fixed_data


def main():
    """Main function"""
    input_file = "data/cart_data_20250809_134711.json"
    output_file = "data/cart_data_20250809_134711_fixed.json"

    if not os.path.exists(input_file):
        print(f"Input file not found: {input_file}")
        return

    # Load original data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Fix encoding issues
    fixed_data = fix_encoding_issues(data)

    # Save fixed data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_data, f, ensure_ascii=False, indent=2)

    print("Fixed encoding issues in cart data")
    print(f"Original: {input_file}")
    print(f"Fixed: {output_file}")

    # Show sample of fixed data
    if ("商品数据" in fixed_data and
            len(fixed_data["商品数据"]) > 0):
        print("\nSample fixed product name:")
        print(f"  {fixed_data['商品数据'][0]['品名']}")


if __name__ == "__main__":
    main()
