# -*- coding: utf-8 -*-
"""
1688自动化项目测试套件 - 配置模块测试
"""

import unittest
import tempfile
import os
import json
from pathlib import Path

import sys
sys.path.append('..')

from config import Config

class TestConfig(unittest.TestCase):
    """配置模块测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = Config()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_project_paths(self):
        """测试项目路径配置"""
        self.assertIsNotNone(self.config.PROJECT_ROOT)
        self.assertTrue(self.config.PROJECT_ROOT.exists())
        self.assertTrue(self.config.CACHE_DIR.name == 'cache')
        self.assertTrue(self.config.DATA_DIR.name == 'data')
        self.assertTrue(self.config.REPORTS_DIR.name == 'reports')
        self.assertTrue(self.config.LOGS_DIR.name == 'logs')
    
    def test_chrome_config(self):
        """测试Chrome配置"""
        self.assertEqual(self.config.CHROME_DEBUG_PORT, 9222)
        self.assertIn('chrome_debug', self.config.CHROME_USER_DATA_DIR)
        self.assertGreater(self.config.CHROME_LAUNCH_TIMEOUT, 0)
    
    def test_network_config(self):
        """测试网络配置"""
        self.assertGreater(self.config.REQUEST_TIMEOUT, 0)
        self.assertGreater(self.config.MAX_RETRIES, 0)
        self.assertGreater(self.config.CONCURRENT_LIMIT, 0)
    
    def test_selectors_config(self):
        """测试选择器配置"""
        self.assertIn('shop_container', self.config.CART_ITEM_SELECTORS)
        self.assertIn('company_name', self.config.CART_ITEM_SELECTORS)
        self.assertIn('item_group', self.config.CART_ITEM_SELECTORS)
        self.assertIn('main_image', self.config.CART_ITEM_SELECTORS)
    
    def test_excel_columns(self):
        """测试Excel列配置"""
        self.assertGreater(len(self.config.EXCEL_COLUMNS), 0)
        self.assertIn('序号', self.config.EXCEL_COLUMNS)
        self.assertIn('商品名称', self.config.EXCEL_COLUMNS)
        self.assertIn('数量', self.config.EXCEL_COLUMNS)
    
    def test_create_directories(self):
        """测试目录创建"""
        test_dirs = [
            Path(self.temp_dir) / 'cache',
            Path(self.temp_dir) / 'data',
            Path(self.temp_dir) / 'reports',
            Path(self.temp_dir) / 'logs'
        ]
        
        # 模拟目录创建
        for directory in test_dirs:
            directory.mkdir(parents=True, exist_ok=True)
            self.assertTrue(directory.exists())
    
    def test_user_config(self):
        """测试用户配置"""
        test_config = {'test_key': 'test_value'}
        config_file = Path(self.temp_dir) / 'test_config.json'
        
        # 保存配置
        self.config.save_user_config(test_config, str(config_file))
        self.assertTrue(config_file.exists())
        
        # 加载配置
        loaded_config = self.config.load_user_config(str(config_file))
        self.assertEqual(loaded_config, test_config)
    
    def test_chrome_launch_args(self):
        """测试Chrome启动参数"""
        args = self.config.get_chrome_launch_args()
        self.assertIsInstance(args, list)
        self.assertGreater(len(args), 0)
        
        # 检查是否包含关键参数
        args_str = ' '.join(args)
        self.assertIn('--remote-debugging-port', args_str)
        self.assertIn('--user-data-dir', args_str)
        self.assertIn('--disable-web-security', args_str)
    
    def test_env_config(self):
        """测试环境变量配置"""
        # 设置测试环境变量
        os.environ['CHROME_DEBUG_PORT'] = '9999'
        os.environ['REQUEST_TIMEOUT'] = '60'
        
        env_config = self.config.get_env_config()
        self.assertEqual(env_config['chrome_debug_port'], 9999)
        self.assertEqual(env_config['request_timeout'], 60)
        
        # 清理环境变量
        del os.environ['CHROME_DEBUG_PORT']
        del os.environ['REQUEST_TIMEOUT']
    
    def test_merge_config(self):
        """测试配置合并"""
        base_config = {'key1': 'base_value', 'key2': 'base_value2'}
        user_config = {'key2': 'user_value', 'key3': 'user_value3'}
        env_config = {'key3': 'env_value', 'key4': 'env_value4'}
        
        merged = self.config.merge_config(base_config, user_config, env_config)
        
        # 验证优先级
        self.assertEqual(merged['key1'], 'base_value')  # base
        self.assertEqual(merged['key2'], 'user_value')  # user覆盖base
        self.assertEqual(merged['key3'], 'env_value')   # env覆盖user
        self.assertEqual(merged['key4'], 'env_value4') # env

if __name__ == '__main__':
    unittest.main()