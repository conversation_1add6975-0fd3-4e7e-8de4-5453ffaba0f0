# -*- coding: utf-8 -*-
"""
1688自动化项目工具模块
通用工具函数和辅助方法
"""

import os
import re
import json
import time
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from urllib.parse import urlparse, urljoin
import requests
from PIL import Image
import io

from config import Config
from exceptions import NetworkError, ImageProcessingError, ValidationError
from logger import get_logger

logger = get_logger(__name__)

class NetworkUtils:
    """网络工具类"""
    
    @staticmethod
    async def download_image_async(session: aiohttp.ClientSession, 
                                 url: str, 
                                 timeout: int = 30) -> bytes:
        """异步下载图片"""
        try:
            async with session.get(url, timeout=timeout) as response:
                if response.status == 200:
                    return await response.read()
                else:
                    raise NetworkError(f"图片下载失败，状态码: {response.status}")
        except Exception as e:
            raise NetworkError(f"图片下载失败: {str(e)}")
    
    @staticmethod
    def download_image_sync(url: str, timeout: int = 30) -> bytes:
        """同步下载图片"""
        try:
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()
            return response.content
        except Exception as e:
            raise NetworkError(f"图片下载失败: {str(e)}")
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """验证URL格式"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    @staticmethod
    def extract_offer_id(url: str) -> Optional[str]:
        """从URL中提取offerId"""
        patterns = [
            r'offer/(\d+)',
            r'offerId=(\d+)',
            r'id=(\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None

class ImageUtils:
    """图片处理工具类"""
    
    @staticmethod
    def resize_image(image_data: bytes, 
                    max_size: tuple = (120, 120)) -> bytes:
        """调整图片大小"""
        try:
            img = Image.open(io.BytesIO(image_data))
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 转换为字节
            output = io.BytesIO()
            img.save(output, format='PNG', optimize=True)
            return output.getvalue()
        except Exception as e:
            raise ImageProcessingError(f"图片调整失败: {str(e)}")
    
    @staticmethod
    def convert_to_excel_format(image_data: bytes) -> bytes:
        """转换为Excel兼容格式"""
        try:
            img = Image.open(io.BytesIO(image_data))
            
            # 确保图片是RGB格式
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 转换为字节
            output = io.BytesIO()
            img.save(output, format='PNG', optimize=True)
            return output.getvalue()
        except Exception as e:
            raise ImageProcessingError(f"图片格式转换失败: {str(e)}")
    
    @staticmethod
    def get_image_info(image_data: bytes) -> Dict[str, Any]:
        """获取图片信息"""
        try:
            img = Image.open(io.BytesIO(image_data))
            return {
                'format': img.format,
                'mode': img.mode,
                'size': img.size,
                'width': img.width,
                'height': img.height,
                'file_size': len(image_data)
            }
        except Exception as e:
            raise ImageProcessingError(f"获取图片信息失败: {str(e)}")

class DataUtils:
    """数据处理工具类"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本数据"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,;:!?()-]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_price(text: str) -> float:
        """从文本中提取价格"""
        if not text:
            return 0.0
        
        # 匹配价格格式
        price_patterns = [
            r'(\d+\.?\d*)',
            r'￥(\d+\.?\d*)',
            r'¥(\d+\.?\d*)',
            r'(\d+)元'
        ]
        
        for pattern in price_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue
        
        return 0.0
    
    @staticmethod
    def extract_quantity(text: str) -> int:
        """从文本中提取数量"""
        if not text:
            return 0
        
        # 匹配数量格式
        quantity_patterns = [
            r'(\d+)',
            r'数量[：:](\d+)',
            r'(\d+)[件个套]'
        ]
        
        for pattern in quantity_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return int(match.group(1))
                except:
                    continue
        
        return 0
    
    @staticmethod
    def validate_cart_item(item: Dict[str, Any]) -> bool:
        """验证购物车商品数据"""
        required_fields = ['序号', '品名', '数量']
        
        for field in required_fields:
            if field not in item or not item[field]:
                return False
        
        # 数量必须大于0
        if isinstance(item['数量'], (int, float)) and item['数量'] <= 0:
            return False
        
        return True
    
    @staticmethod
    def normalize_cart_data(cart_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """标准化购物车数据"""
        normalized_data = []
        
        for item in cart_data:
            if not DataUtils.validate_cart_item(item):
                continue
            
            # 清理文本数据
            cleaned_item = {}
            for key, value in item.items():
                if isinstance(value, str):
                    cleaned_item[key] = DataUtils.clean_text(value)
                else:
                    cleaned_item[key] = value
            
            # 确保必要的字段存在
            cleaned_item.setdefault('生产商名称', '未知供应商')
            cleaned_item.setdefault('图片URL', '')
            cleaned_item.setdefault('商品链接', '')
            cleaned_item.setdefault('规格', '默认规格')
            
            normalized_data.append(cleaned_item)
        
        return normalized_data

class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def save_json(data: Any, file_path: Union[str, Path], 
                  indent: int = 2) -> None:
        """保存JSON文件"""
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=indent)
        except Exception as e:
            raise Exception(f"保存JSON文件失败: {str(e)}")
    
    @staticmethod
    def load_json(file_path: Union[str, Path]) -> Any:
        """加载JSON文件"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"加载JSON文件失败: {str(e)}")
    
    @staticmethod
    def generate_filename(prefix: str, extension: str = '.xlsx') -> str:
        """生成文件名"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{timestamp}{extension}"

class StringUtils:
    """字符串处理工具类"""
    
    @staticmethod
    def fuzzy_match(str1: str, str2: str, threshold: float = 0.7) -> bool:
        """模糊匹配"""
        try:
            from difflib import SequenceMatcher
            similarity = SequenceMatcher(None, str1, str2).ratio()
            return similarity >= threshold
        except:
            return str1 == str2
    
    @staticmethod
    def extract_number(text: str) -> float:
        """从文本中提取数字"""
        if not text:
            return 0.0
        
        # 匹配数字（包括小数）
        match = re.search(r'(\d+\.?\d*)', text)
        if match:
            try:
                return float(match.group(1))
            except:
                pass
        
        return 0.0
    
    @staticmethod
    def format_price(price: float) -> str:
        """格式化价格"""
        return f"{price:.2f}"

class ValidationUtils:
    """数据验证工具类"""
    
    @staticmethod
    def validate_excel_data(data: List[Dict[str, Any]], 
                          required_columns: List[str]) -> bool:
        """验证Excel数据"""
        if not data:
            return False
        
        # 检查必需的列
        for item in data:
            for column in required_columns:
                if column not in item:
                    return False
        
        return True
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL"""
        return NetworkUtils.is_valid_url(url)
    
    @staticmethod
    def validate_image_url(url: str) -> bool:
        """验证图片URL"""
        if not ValidationUtils.validate_url(url):
            return False
        
        # 检查图片扩展名
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        return any(url.lower().endswith(ext) for ext in image_extensions)

class CacheUtils:
    """缓存工具类"""
    
    @staticmethod
    def get_cache_key(url: str) -> str:
        """生成缓存键"""
        import hashlib
        return hashlib.md5(url.encode()).hexdigest()
    
    @staticmethod
    def get_cache_path(cache_dir: Union[str, Path], 
                      cache_key: str, 
                      extension: str = '.jpg') -> Path:
        """获取缓存文件路径"""
        cache_dir = Path(cache_dir)
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir / f"{cache_key}{extension}"
    
    @staticmethod
    def is_cache_valid(cache_file: Path, max_age: int = 86400) -> bool:
        """检查缓存是否有效"""
        if not cache_file.exists():
            return False
        
        import time
        file_age = time.time() - cache_file.stat().st_mtime
        return file_age < max_age

# 使用示例
if __name__ == "__main__":
    # 测试工具函数
    print("测试工具函数...")
    
    # 测试网络工具
    test_url = "https://example.com/image.jpg"
    print(f"URL验证: {NetworkUtils.is_valid_url(test_url)}")
    print(f"OfferID提取: {NetworkUtils.extract_offer_id('https://detail.1688.com/offer/123456.html')}")
    
    # 测试数据处理
    test_text = "  这 是 测 试 文 本  "
    print(f"文本清理: '{DataUtils.clean_text(test_text)}'")
    print(f"价格提取: {DataUtils.extract_price('￥29.9')}")
    print(f"数量提取: {DataUtils.extract_quantity('数量:5')}")
    
    # 测试字符串工具
    print(f"模糊匹配: {StringUtils.fuzzy_match('测试文本', '测试文本', 0.8)}")
    print(f"数字提取: {StringUtils.extract_number('价格123.45元')}")
    
    # 测试文件工具
    test_data = {"test": "data"}
    test_file = "test_output/test_utils.json"
    FileUtils.save_json(test_data, test_file)
    loaded_data = FileUtils.load_json(test_file)
    print(f"JSON操作: {loaded_data == test_data}")
    
    print("✅ 工具函数测试完成")