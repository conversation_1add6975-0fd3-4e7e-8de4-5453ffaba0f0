# 1688采购车数据处理工具 - PyQt6版本

## 概述

由于tkinter在某些Python环境中不可用，我们提供了基于PyQt6的界面版本，具有更好的兼容性和更小的打包体积。

## 版本说明

### 1. qt_simple.py - 标准版
- 完整的PyQt6界面
- 功能齐全，用户体验良好
- 适合日常使用

### 2. qt_minimal.py - 极简版
- 精简的PyQt6界面
- 减少不必要的功能
- 打包体积更小

### 3. qt_ultralight.py - 超轻量版
- 最小化依赖
- 包含控制台版本作为fallback
- 最佳的打包体积优化

## 安装要求

```bash
pip install PyQt6
```

## 运行方式

### 直接运行
```bash
python ui_app/qt_simple.py        # 标准版
python ui_app/qt_minimal.py       # 极简版
python ui_app/qt_ultralight.py    # 超轻量版
```

### 打包成可执行文件

#### 方法1: 使用完整打包脚本
```bash
build_qt_app.bat
```

#### 方法2: 使用优化打包脚本（推荐）
```bash
build_minimal.bat
```

#### 方法3: 手动打包
```bash
# 安装PyInstaller
pip install pyinstaller

# 打包超轻量版
pyinstaller --noconfirm --clean --name "1688数据工具" --windowed --onedir ui_app/qt_ultralight.py
```

## 打包优化技巧

### 1. 减少PyQt6模块
- 只包含必要的Qt模块
- 排除不需要的Qt组件

### 2. 清理不必要的文件
```bash
# 打包后删除这些文件和目录
dist/1688数据工具/PyQt6/Qt6/translations/
dist/1688数据工具/PyQt6/Qt6/plugins/imageformats/ (除了qjpeg.dll)
dist/1688数据工具/PyQt6/Qt6/plugins/platforms/ (除了qwindows.dll)
```

### 3. 使用UPX压缩
```bash
# 安装UPX
# 下载UPX并放到系统PATH中

# 使用UPX打包
pyinstaller --upx-dir=path_to_upx ...
```

### 4. 进一步优化
- 使用`--onefile`模式（单文件，但启动较慢）
- 手动编辑spec文件进行精细控制
- 考虑使用Nuitka替代PyInstaller

## 界面功能

### 主要功能
1. **采购车数据提取**
   - 从1688采购车页面提取商品数据
   - 自动下载商品图片
   - 生成Excel报告

2. **订单数据增强**
   - 为Excel订单文件添加商品信息
   - 匹配商品图片和详情链接
   - 增强数据完整性

### 界面特性
- 实时进度显示
- 详细日志记录
- 支持任务中断
- 文件选择对话框
- 错误处理和提示

## 系统要求

- Windows 7或更高版本
- Python 3.8+
- PyQt6 6.0+
- 可用内存: 至少100MB

## 故障排除

### 1. PyQt6导入失败
```bash
pip install --upgrade PyQt6
```

### 2. 打包文件过大
- 使用`build_minimal.bat`脚本
- 手动删除不必要的插件文件
- 考虑使用控制台版本

### 3. 运行时错误
- 检查是否缺少必要的DLL文件
- 确保目标系统安装了Visual C++ Redistributable
- 查看日志文件了解具体错误

## 开发说明

### 文件结构
```
ui_app/
├── qt_simple.py          # 标准版界面
├── qt_minimal.py         # 极简版界面
├── qt_ultralight.py      # 超轻量版界面
└── main.py               # 原始tkinter版本

build_qt_app.bat          # 完整打包脚本
build_minimal.bat         # 优化打包脚本
```

### 自定义修改
- 修改界面样式：调整`setStyleSheet()`部分
- 添加新功能：在对应的工作线程中实现
- 优化打包：编辑`.spec`文件或修改打包脚本

## 性能对比

| 版本 | 功能完整性 | 打包大小 | 内存使用 | 启动速度 |
|------|------------|----------|----------|----------|
| qt_simple.py | 完整 | ~80MB | ~50MB | 快 |
| qt_minimal.py | 基础 | ~60MB | ~40MB | 快 |
| qt_ultralight.py | 最小 | ~40MB | ~30MB | 最快 |

## 注意事项

1. PyQt6的打包体积仍然比tkinter大
2. 首次运行可能需要安装Visual C++ Redistributable
3. 在某些系统上可能需要管理员权限
4. 建议在目标系统上进行测试以确保兼容性