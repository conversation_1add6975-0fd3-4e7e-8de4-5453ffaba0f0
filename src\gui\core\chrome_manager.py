"""
Chrome管理器模块
"""

import subprocess
import time
import socket
import threading
import os
import sys
from pathlib import Path
from typing import Optional, Callable
import psutil

class ChromeManager:
    """Chrome浏览器管理器"""
    
    def __init__(self, debug_port: int = 9222):
        self.debug_port = debug_port
        self.chrome_process = None
        self.is_running = False
        self.startup_timeout = 30
        self.retry_attempts = 3
        
    def start_chrome(self, user_data_dir: Optional[str] = None) -> bool:
        """启动Chrome浏览器"""
        try:
            # 检查Chrome是否已运行
            if self.is_chrome_running():
                print("Chrome已在运行，尝试连接到现有实例")
                if self.test_connection():
                    return True
                else:
                    print("无法连接到现有Chrome实例，将启动新实例")
            
            # 构建Chrome启动命令
            chrome_cmd = self.build_chrome_command(user_data_dir)
            
            print(f"启动Chrome命令：{' '.join(chrome_cmd)}")
            
            # 启动Chrome
            self.chrome_process = subprocess.Popen(
                chrome_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == 'win32' else 0
            )
            
            # 等待Chrome启动
            if self.wait_for_chrome_startup():
                self.is_running = True
                print("Chrome启动成功")
                return True
            else:
                print("Chrome启动超时")
                return False
                
        except Exception as e:
            print(f"启动Chrome失败：{e}")
            return False
    
    def build_chrome_command(self, user_data_dir: Optional[str] = None) -> list:
        """构建Chrome启动命令"""
        # 查找Chrome可执行文件路径
        chrome_path = self.find_chrome_path()
        
        if not chrome_path:
            raise RuntimeError("未找到Chrome浏览器，请确保已安装Chrome")
        
        # 如果是启动脚本，直接返回脚本路径
        if chrome_path.endswith('.bat'):
            return [chrome_path]
        
        # 构建命令
        cmd = [chrome_path]
        
        # 添加调试参数
        cmd.extend([
            f'--remote-debugging-port={self.debug_port}',
            '--remote-allow-origins=*'
        ])
        
        # 添加用户数据目录
        if user_data_dir:
            cmd.extend([f'--user-data-dir={user_data_dir}'])
        else:
            # 使用默认用户数据目录
            default_user_dir = Path.home() / ".chrome_debug"
            default_user_dir.mkdir(exist_ok=True)
            cmd.extend([f'--user-data-dir={default_user_dir}'])
        
        # 添加其他有用参数
        cmd.extend([
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',  # 可选：禁用图片以提高性能
        ])
        
        return cmd
    
    def find_chrome_path(self) -> Optional[str]:
        """查找Chrome可执行文件路径"""
        possible_paths = []
        
        if sys.platform == 'win32':
            # Windows路径
            program_files = os.environ.get('ProgramFiles', 'C:\\Program Files')
            program_files_x86 = os.environ.get('ProgramFiles(x86)', 'C:\\Program Files (x86)')
            
            possible_paths = [
                f"{program_files}\\Google\\Chrome\\Application\\chrome.exe",
                f"{program_files_x86}\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            ]
            
            # 检查本地AppData
            local_app_data = os.environ.get('LOCALAPPDATA', '')
            if local_app_data:
                possible_paths.append(f"{local_app_data}\\Google\\Chrome\\Application\\chrome.exe")
                
            # 检查启动脚本
            script_path = Path(__file__).parent.parent.parent / "start_debug_chrome.bat"
            if script_path.exists():
                return str(script_path)
        
        elif sys.platform == 'darwin':
            # macOS路径
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chrome.app/Contents/MacOS/Google Chrome",
            ]
        
        else:
            # Linux路径
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
                "/snap/bin/chromium",
                "/opt/google/chrome/chrome",
            ]
        
        # 检查路径是否存在
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def wait_for_chrome_startup(self) -> bool:
        """等待Chrome启动"""
        start_time = time.time()
        
        while time.time() - start_time < self.startup_timeout:
            if self.test_connection():
                return True
            time.sleep(1)
        
        return False
    
    def test_connection(self) -> bool:
        """测试Chrome调试连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', self.debug_port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def is_chrome_running(self) -> bool:
        """检查Chrome是否正在运行"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            return False
        except Exception:
            return False
    
    def is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def stop_chrome(self) -> bool:
        """停止Chrome浏览器"""
        try:
            if self.chrome_process:
                self.chrome_process.terminate()
                self.chrome_process.wait(timeout=5)
                self.chrome_process = None
            
            self.is_running = False
            print("Chrome已停止")
            return True
            
        except Exception as e:
            print(f"停止Chrome失败：{e}")
            return False
    
    def kill_chrome_processes(self) -> bool:
        """强制终止所有Chrome进程"""
        try:
            killed_count = 0
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        proc.kill()
                        killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            print(f"已终止{killed_count}个Chrome进程")
            return killed_count > 0
            
        except Exception as e:
            print(f"终止Chrome进程失败：{e}")
            return False
    
    def get_chrome_version(self) -> Optional[str]:
        """获取Chrome版本"""
        try:
            chrome_path = self.find_chrome_path()
            if chrome_path:
                result = subprocess.run(
                    [chrome_path, '--version'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                return result.stdout.strip()
            return None
        except Exception:
            return None
    
    def is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result != 0
        except Exception:
            return False
    
    def get_available_port(self, start_port: int = 9222) -> int:
        """获取可用端口"""
        for port in range(start_port, start_port + 100):
            if self.is_port_available(port):
                return port
        return start_port  # 如果没有找到可用端口，返回默认端口
    
    def restart_chrome(self, user_data_dir: Optional[str] = None) -> bool:
        """重启Chrome浏览器"""
        self.stop_chrome()
        time.sleep(2)  # 等待Chrome完全停止
        return self.start_chrome(user_data_dir)
    
    def get_chrome_tabs(self) -> list:
        """获取Chrome标签页列表"""
        try:
            if not self.test_connection():
                return []
            
            import requests
            
            response = requests.get(f'http://localhost:{self.debug_port}/json')
            if response.status_code == 200:
                return response.json()
            return []
            
        except Exception as e:
            print(f"获取Chrome标签页失败：{e}")
            return []
    
    def create_new_tab(self, url: str = "about:blank") -> bool:
        """创建新标签页"""
        try:
            if not self.test_connection():
                return False
            
            import requests
            
            response = requests.post(
                f'http://localhost:{self.debug_port}/json/new',
                json={"url": url}
            )
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"创建新标签页失败：{e}")
            return False
    
    def close_tab(self, tab_id: str) -> bool:
        """关闭指定标签页"""
        try:
            if not self.test_connection():
                return False
            
            import requests
            
            response = requests.delete(
                f'http://localhost:{self.debug_port}/json/close/{tab_id}'
            )
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"关闭标签页失败：{e}")
            return False
    
    def navigate_to(self, tab_id: str, url: str) -> bool:
        """导航到指定URL"""
        try:
            if not self.test_connection():
                return False
            
            import requests
            
            response = requests.get(
                f'http://localhost:{self.debug_port}/json/activate/{tab_id}'
            )
            
            if response.status_code == 200:
                # 使用JavaScript导航
                js_code = f'window.location.href = "{url}";'
                return self.execute_javascript(tab_id, js_code)
            
            return False
            
        except Exception as e:
            print(f"导航失败：{e}")
            return False
    
    def execute_javascript(self, tab_id: str, javascript: str) -> Optional[str]:
        """在指定标签页执行JavaScript"""
        try:
            if not self.test_connection():
                return None
            
            import requests
            
            response = requests.post(
                f'http://localhost:{self.debug_port}/json/execute/{tab_id}',
                json={"expression": javascript}
            )
            
            if response.status_code == 200:
                return response.json().get('result')
            
            return None
            
        except Exception as e:
            print(f"执行JavaScript失败：{e}")
            return None
    
    def get_page_source(self, tab_id: str) -> Optional[str]:
        """获取页面源代码"""
        js_code = 'document.documentElement.outerHTML;'
        return self.execute_javascript(tab_id, js_code)
    
    def take_screenshot(self, tab_id: str, output_path: str) -> bool:
        """截取页面截图"""
        try:
            js_code = f'''
            (function() {{
                var canvas = document.createElement('canvas');
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                var ctx = canvas.getContext('2d');
                ctx.drawWindow(window, 0, 0, canvas.width, canvas.height, 'white');
                return canvas.toDataURL('image/png');
            }})();
            '''
            
            result = self.execute_javascript(tab_id, js_code)
            if result and result.startswith('data:image/png;base64,'):
                import base64
                image_data = base64.b64decode(result.split(',')[1])
                
                with open(output_path, 'wb') as f:
                    f.write(image_data)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"截图失败：{e}")
            return False
    
    def __del__(self):
        """析构函数"""
        if self.chrome_process:
            try:
                self.chrome_process.terminate()
            except:
                pass