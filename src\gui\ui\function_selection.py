"""
功能选择界面
"""

import tkinter as tk
from tkinter import ttk, font, messagebox
from typing import Callable

class FunctionSelectionWindow:
    """功能选择窗口"""
    
    def __init__(self, parent, on_cart_selected: Callable, on_order_selected: Callable, on_exit: Callable):
        self.parent = parent
        self.on_cart_selected = on_cart_selected
        self.on_order_selected = on_order_selected
        self.on_exit = on_exit
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("1688采购车数据处理工具")
        self.window.geometry("700x500")
        self.window.resizable(False, False)
        
        # 居中显示
        self.center_window()
        
        # 设置窗口样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_exit)
        
        # 禁用父窗口
        self.parent.withdraw()
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_styles(self):
        """设置样式"""
        # 创建字体
        self.title_font = font.Font(family="微软雅黑", size=16, weight="bold")
        self.subtitle_font = font.Font(family="微软雅黑", size=12)
        self.button_font = font.Font(family="微软雅黑", size=11)
        self.description_font = font.Font(family="微软雅黑", size=10)
        
        # 设置颜色主题
        self.bg_color = "#f5f5f5"
        self.primary_color = "#2196F3"
        self.secondary_color = "#FF9800"
        self.text_color = "#333333"
        self.border_color = "#dddddd"
        
        self.window.configure(bg=self.bg_color)
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.window, bg=self.bg_color, height=80)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        title_frame.pack_propagate(False)
        
        tk.Label(
            title_frame,
            text="1688采购车数据处理工具",
            font=self.title_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(expand=True)
        
        # 副标题
        subtitle_frame = tk.Frame(self.window, bg=self.bg_color, height=40)
        subtitle_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        subtitle_frame.pack_propagate(False)
        
        tk.Label(
            subtitle_frame,
            text="请选择您要使用的功能：",
            font=self.subtitle_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(expand=True)
        
        # 功能选择区域
        self.create_function_cards()
        
        # 底部按钮区域
        self.create_bottom_buttons()
    
    def create_function_cards(self):
        """创建功能卡片"""
        cards_frame = tk.Frame(self.window, bg=self.bg_color)
        cards_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 采购车数据提取卡片
        cart_card = self.create_function_card(
            cards_frame,
            title="🛒 采购车数据提取",
            description=[
                "从1688采购车页面直接提取商品数据",
                "无需Excel文件，直接生成包含图片的Excel报告"
            ],
            button_text="选择此功能",
            button_color=self.primary_color,
            on_click=self.on_cart_selected
        )
        cart_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 订单数据增强卡片
        order_card = self.create_function_card(
            cards_frame,
            title="📋 订单数据增强",
            description=[
                "从已有的Excel订单文件中增强数据",
                "添加商品图片、详情链接等信息"
            ],
            button_text="选择此功能",
            button_color=self.secondary_color,
            on_click=self.on_order_selected
        )
        order_card.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
    
    def create_function_card(self, parent, title: str, description: list, button_text: str, button_color: str, on_click: Callable):
        """创建功能卡片"""
        card_frame = tk.Frame(parent, bg="white", relief=tk.RAISED, bd=1)
        card_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 内部容器
        inner_frame = tk.Frame(card_frame, bg="white")
        inner_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        tk.Label(
            inner_frame,
            text=title,
            font=self.subtitle_font,
            bg="white",
            fg=self.text_color,
            anchor="w"
        ).pack(fill=tk.X, pady=(0, 15))
        
        # 描述
        for desc in description:
            tk.Label(
                inner_frame,
                text=f"• {desc}",
                font=self.description_font,
                bg="white",
                fg="#666666",
                anchor="w",
                justify=tk.LEFT
            ).pack(fill=tk.X, pady=(0, 5))
        
        # 按钮区域
        button_frame = tk.Frame(inner_frame, bg="white")
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        # 功能按钮
        button = tk.Button(
            button_frame,
            text=button_text,
            font=self.button_font,
            bg=button_color,
            fg="white",
            relief=tk.FLAT,
            cursor="hand2",
            command=on_click,
            padx=20,
            pady=8
        )
        button.pack(side=tk.LEFT)
        
        # 悬停效果
        def on_enter(e):
            button.config(bg=self.adjust_color(button_color, -20))
        
        def on_leave(e):
            button.config(bg=button_color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return card_frame
    
    def create_bottom_buttons(self):
        """创建底部按钮"""
        bottom_frame = tk.Frame(self.window, bg=self.bg_color, height=60)
        bottom_frame.pack(fill=tk.X, padx=20, pady=(10, 20))
        bottom_frame.pack_propagate(False)
        
        # 功能说明
        info_frame = tk.Frame(bottom_frame, bg=self.bg_color)
        info_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        
        tk.Label(
            info_frame,
            text="💡 功能说明：",
            font=self.description_font,
            bg=self.bg_color,
            fg=self.text_color
        ).pack(anchor="w")
        
        tk.Label(
            info_frame,
            text="采购车提取：适合需要从1688网站获取最新数据的用户",
            font=self.description_font,
            bg=self.bg_color,
            fg="#666666"
        ).pack(anchor="w")
        
        tk.Label(
            info_frame,
            text="订单增强：适合已有Excel订单文件，需要补充商品信息的用户",
            font=self.description_font,
            bg=self.bg_color,
            fg="#666666"
        ).pack(anchor="w")
        
        # 按钮区域
        button_frame = tk.Frame(bottom_frame, bg=self.bg_color)
        button_frame.pack(side=tk.RIGHT)
        
        # 帮助按钮
        help_button = tk.Button(
            button_frame,
            text="查看帮助",
            font=self.button_font,
            bg="#e0e0e0",
            fg=self.text_color,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.show_help,
            padx=15,
            pady=5
        )
        help_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 退出按钮
        exit_button = tk.Button(
            button_frame,
            text="退出",
            font=self.button_font,
            bg="#f44336",
            fg="white",
            relief=tk.FLAT,
            cursor="hand2",
            command=self.on_exit,
            padx=15,
            pady=5
        )
        exit_button.pack(side=tk.LEFT)
        
        # 按钮悬停效果
        help_button.bind("<Enter>", lambda e: help_button.config(bg="#d0d0d0"))
        help_button.bind("<Leave>", lambda e: help_button.config(bg="#e0e0e0"))
        exit_button.bind("<Enter>", lambda e: exit_button.config(bg="#d32f2f"))
        exit_button.bind("<Leave>", lambda e: exit_button.config(bg="#f44336"))
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """1688采购车数据处理工具使用说明

🛒 采购车数据提取功能：
• 用于从1688网站采购车页面直接提取商品数据
• 无需准备Excel文件，程序会自动生成包含图片的报告
• 适合需要获取最新采购车数据的用户

📋 订单数据增强功能：
• 用于为已有的Excel订单文件添加商品信息
• 会自动为每条订单记录添加商品图片和详情链接
• 适合已有订单数据，需要补充商品信息的用户

使用步骤：
1. 选择您需要的功能
2. 按照界面提示逐步操作
3. 等待处理完成
4. 保存结果文件

注意事项：
• 请确保电脑已安装Chrome浏览器
• 首次使用可能需要下载Chrome驱动
• 网络连接稳定可提高处理成功率

如需更多帮助，请联系技术支持。"""
        
        messagebox.showinfo("帮助信息", help_text)
    
    def adjust_color(self, color: str, amount: int) -> str:
        """调整颜色亮度"""
        # 简单的颜色调整函数
        if color.startswith("#"):
            color = color[1:]
        
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def destroy(self):
        """销毁窗口"""
        self.parent.deiconify()  # 恢复父窗口
        self.window.destroy()